{"_format": "hh-sol-artifact-1", "contractName": "ERC1155Upgradeable", "sourceName": "contracts/token/ERC1155/ERC1155Upgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "values", "type": "uint256[]"}], "name": "TransferBatch", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "TransferSingle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "value", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "URI", "type": "event"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}], "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeBatchTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "uri", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}