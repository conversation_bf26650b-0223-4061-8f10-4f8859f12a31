import apiService from './api.js'
import web3Service from './web3Service.js'

class CrossChainService {
  constructor() {
    this.bridgeTransactions = new Map()
    this.listeners = {}
    
    // Start monitoring transactions
    this.startTransactionMonitoring()
  }

  // Get supported chains from Web3 service
  getSupportedChains() {
    return web3Service.getSupportedChains()
  }

  // Get network status for all chains
  async getNetworkStatus() {
    try {
      // Check current MetaMask connection
      const connection = await web3Service.checkConnection()
      const networkHealth = await web3Service.checkAllNetworks()
      
      const status = {}
      this.getSupportedChains().forEach(chain => {
        status[chain.chainId] = {
          connected: connection && connection.chainId === chain.chainId,
          healthy: networkHealth[chain.chainId]?.isHealthy || false,
          ...chain
        }
      })
      
      return status
    } catch (error) {
      console.error('Error getting network status:', error)
      throw error
    }
  }

  // Connect to a specific network
  async connectToNetwork(chainId) {
    try {
      await web3Service.switchNetwork(chainId)
      return true
    } catch (error) {
      console.error(`Error connecting to network ${chainId}:`, error)
      throw error
    }
  }

  // Get bridge quote for token/NFT transfer
  async getBridgeQuote(fromChain, toChain, tokenContract, tokenId, bridgeName) {
    try {
      const response = await apiService.getBridgeQuote(
        fromChain, 
        toChain, 
        tokenContract, 
        tokenId, 
        bridgeName
      )
      return response.data || response
    } catch (error) {
      console.error('Error getting bridge quote:', error)
      
      // Fallback to mock data if API fails
      return this.getMockBridgeQuote(fromChain, toChain)
    }
  }

  // Bridge NFT between chains
  async bridgeNFT(fromChain, toChain, tokenContract, tokenId, toAddress, bridgeName = 'layerzero') {
    try {
      // Validate parameters
      if (!fromChain || !toChain || !tokenContract || !tokenId || !toAddress) {
        throw new Error('Missing required parameters for NFT bridge')
      }

      // Check if user is connected to the source chain
      const currentChain = web3Service.getCurrentChain()
      if (currentChain !== fromChain) {
        await web3Service.switchNetwork(fromChain)
      }

      // Get bridge quote first
      const quote = await this.getBridgeQuote(fromChain, toChain, tokenContract, tokenId, bridgeName)
      
      // Create transaction record
      const transaction = {
        id: this.generateTransactionId(),
        type: 'NFT Bridge',
        fromChain,
        toChain,
        tokenContract,
        tokenId,
        toAddress,
        bridgeName,
        status: 'pending',
        createdAt: new Date(),
        quote
      }

      this.bridgeTransactions.set(transaction.id, transaction)

      // Call backend API
      const response = await apiService.bridgeNFT(
        fromChain,
        toChain,
        tokenContract,
        tokenId,
        toAddress,
        bridgeName
      )

      // Update transaction with response
      transaction.txHash = response.txHash
      transaction.status = 'confirmed'
      
      this.emit('transactionUpdate', transaction)
      
      return transaction
    } catch (error) {
      console.error('Error bridging NFT:', error)
      throw error
    }
  }

  // Bridge tokens between chains
  async bridgeToken(fromChain, toChain, tokenContract, amount, toAddress) {
    try {
      // Validate parameters
      if (!fromChain || !toChain || !tokenContract || !amount || !toAddress) {
        throw new Error('Missing required parameters for token bridge')
      }

      // Validate amount
      const numericAmount = parseFloat(amount)
      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error('Invalid amount: must be a positive number')
      }

      // Validate address format
      if (!this.isValidAddress(toAddress)) {
        throw new Error('Invalid destination address format')
      }

      // Check if route is supported
      const isValidRoute = await this.validateBridgeRoute(fromChain, toChain)
      if (!isValidRoute) {
        throw new Error(`Bridge route from chain ${fromChain} to ${toChain} is not supported`)
      }

      // Check if user is connected to the source chain
      const currentChain = web3Service.getCurrentChain()
      if (currentChain !== fromChain) {
        await web3Service.switchNetwork(fromChain)
      }

      // Check user balance (for native tokens)
      if (tokenContract === 'native') {
        const balance = await web3Service.getTokenBalance('native', toAddress, fromChain)
        if (parseFloat(balance) < numericAmount) {
          throw new Error(`Insufficient balance. Available: ${balance}`)
        }
      }

      // Create transaction record
      const transaction = {
        id: this.generateTransactionId(),
        type: 'Token Bridge',
        fromChain,
        toChain,
        tokenContract,
        amount,
        toAddress,
        status: 'pending',
        createdAt: new Date()
      }

      this.bridgeTransactions.set(transaction.id, transaction)

      // Call backend API
      const response = await apiService.bridgeToken(
        fromChain,
        toChain,
        tokenContract,
        amount,
        toAddress
      )

      // Update transaction with response
      transaction.txHash = response.data?.txHash || response.txHash
      transaction.status = 'confirmed'

      this.emit('transactionUpdate', transaction)

      return transaction
    } catch (error) {
      console.error('Error bridging token:', error)
      throw error
    }
  }

  // Get bridge transaction by ID
  async getBridgeTransaction(transactionId) {
    try {
      // Check local cache first
      if (this.bridgeTransactions.has(transactionId)) {
        return this.bridgeTransactions.get(transactionId)
      }

      // Fetch from API
      const response = await apiService.getBridgeTransaction(transactionId)
      return response.data || response
    } catch (error) {
      console.error('Error getting bridge transaction:', error)
      throw error
    }
  }

  // Get bridge history for user
  async getBridgeHistory(userAddress, options = {}) {
    try {
      const response = await apiService.getBridgeHistory(userAddress, options)
      return response.data || response
    } catch (error) {
      console.error('Error getting bridge history:', error)
      
      // Return mock data if API fails
      return this.getMockBridgeHistory()
    }
  }

  // Get user's NFTs for a specific chain
  async getUserNFTs(userAddress, chainId) {
    try {
      const response = await apiService.getUserNFTs(userAddress, chainId)
      return response.data || response
    } catch (error) {
      console.error('Error getting user NFTs:', error)
      
      // Return mock data if API fails
      return this.getMockUserNFTs(chainId)
    }
  }

  // Get chain metrics
  async getChainMetrics(chainId) {
    try {
      const response = await apiService.getChainMetrics(chainId)
      return response.data || response
    } catch (error) {
      console.error('Error getting chain metrics:', error)
      
      // Fallback to Web3 service
      return await web3Service.checkNetworkHealth(chainId)
    }
  }

  // Start monitoring bridge transactions
  startTransactionMonitoring() {
    setInterval(() => {
      this.updateTransactionStatuses()
    }, 30000) // Check every 30 seconds
  }

  // Update transaction statuses
  async updateTransactionStatuses() {
    for (const [id, transaction] of this.bridgeTransactions) {
      if (transaction.status === 'confirmed' || transaction.status === 'bridging') {
        try {
          const updated = await this.getBridgeTransaction(id)
          if (updated && updated.status !== transaction.status) {
            transaction.status = updated.status
            transaction.completedAt = updated.completedAt
            this.emit('transactionUpdate', transaction)
          }
        } catch (error) {
          console.error(`Error updating transaction ${id}:`, error)
        }
      }
    }
  }

  // Validate bridge route
  async validateBridgeRoute(fromChain, toChain) {
    try {
      // Check if both chains are supported
      const supportedChains = this.getSupportedChains()
      const fromSupported = supportedChains.some(chain => chain.chainId === fromChain)
      const toSupported = supportedChains.some(chain => chain.chainId === toChain)

      if (!fromSupported || !toSupported) {
        return false
      }

      // Check if chains are different
      if (fromChain === toChain) {
        return false
      }

      // For now, assume all supported chains can bridge to each other
      // In a real implementation, this would check specific bridge protocols
      return true
    } catch (error) {
      console.error('Error validating bridge route:', error)
      return false
    }
  }

  // Validate address format
  isValidAddress(address) {
    // Basic validation for Ethereum-style addresses
    if (typeof address !== 'string') return false

    // Check if it's a valid Ethereum address format
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/
    if (ethAddressRegex.test(address)) return true

    // Add more address format validations for other chains as needed
    // For now, accept any string that looks like an address
    return address.length > 10
  }

  // Estimate bridge fees
  async estimateBridgeFees(fromChain, toChain, amount) {
    try {
      const quote = await this.getBridgeQuote(fromChain, toChain, 'native', null)
      return quote[0]?.fees || { gas: '0.001 ETH', bridge: '0.005 ETH', total: '0.006 ETH' }
    } catch (error) {
      console.error('Error estimating bridge fees:', error)
      return { gas: '0.001 ETH', bridge: '0.005 ETH', total: '0.006 ETH' }
    }
  }

  // Generate unique transaction ID
  generateTransactionId() {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Mock data functions for fallback
  getMockBridgeQuote(fromChain, toChain) {
    const fromChainConfig = web3Service.getChainConfig(fromChain)
    const toChainConfig = web3Service.getChainConfig(toChain)
    
    return [{
      bridge: 'LayerZero',
      fees: {
        gas: fromChainConfig?.gasPrice || '15 gwei',
        bridge: fromChainConfig?.bridgeFee || '0.005 ETH',
        total: '0.01 ETH'
      },
      estimatedTime: parseInt(toChainConfig?.avgTime) || 15,
      route: [fromChainConfig?.name, toChainConfig?.name]
    }]
  }

  getMockBridgeHistory() {
    return [
      {
        id: 'tx_1',
        type: 'NFT Bridge',
        asset: 'Gaming Sword #123',
        fromChain: 1,
        toChain: 137,
        status: 'completed',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        txHash: '0xabc123...'
      },
      {
        id: 'tx_2',
        type: 'Token Bridge',
        asset: '50 USDC',
        fromChain: 137,
        toChain: 1,
        status: 'pending',
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        txHash: '0xdef456...'
      }
    ]
  }

  getMockUserNFTs(chainId) {
    const chainConfig = web3Service.getChainConfig(chainId)
    return [
      {
        id: 1,
        name: 'Cosmic Warrior #1234',
        collection: 'MetaGaming Heroes',
        currentChain: chainId,
        availableChains: [1, 137, 56].filter(id => id !== chainId),
        estimatedValue: '2.5 ETH',
        utility: ['Gaming', 'Staking', 'Governance'],
        bridgeCost: chainConfig?.bridgeFee || '0.005 ETH'
      }
    ]
  }

  // Event emitter functionality
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data))
    }
  }

  on(event, callback) {
    if (!this.listeners[event]) this.listeners[event] = []
    this.listeners[event].push(callback)
  }

  off(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback)
    }
  }
}

// Create singleton instance
const crossChainService = new CrossChainService()

export default crossChainService
export { CrossChainService }
