{"_format": "hh-sol-artifact-1", "contractName": "GovernorVotesQuorumFraction", "sourceName": "contracts/governance/extensions/GovernorVotesQuorumFraction.sol", "abi": [{"inputs": [], "name": "Empty", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "address[]", "name": "targets", "type": "address[]"}, {"indexed": false, "internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"indexed": false, "internalType": "string[]", "name": "signatures", "type": "string[]"}, {"indexed": false, "internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"indexed": false, "internalType": "uint256", "name": "startBlock", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "endBlock", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldQuorumNumerator", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newQuorumNumerator", "type": "uint256"}], "name": "QuorumNumeratorUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "support", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "VoteCast", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "support", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "params", "type": "bytes"}], "name": "VoteCastWithParams", "type": "event"}, {"inputs": [], "name": "BALLOT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COUNTING_MODE", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "EXTENDED_BALLOT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "uint8", "name": "support", "type": "uint8"}], "name": "castVote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "uint8", "name": "support", "type": "uint8"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "castVoteBySig", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "uint8", "name": "support", "type": "uint8"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "castVoteWithReason", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "uint8", "name": "support", "type": "uint8"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "castVoteWithReasonAndParams", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "uint8", "name": "support", "type": "uint8"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "bytes", "name": "params", "type": "bytes"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "castVoteWithReasonAndParamsBySig", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"internalType": "bytes32", "name": "descriptionHash", "type": "bytes32"}], "name": "execute", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "name": "getVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "getVotesWithParams", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"internalType": "bytes32", "name": "descriptionHash", "type": "bytes32"}], "name": "hashProposal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC1155BatchReceived", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC1155Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "proposalDeadline", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "proposalSnapshot", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "propose", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "name": "quorum", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorumDenominator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "name": "quorumNumerator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorumNumerator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "relay", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "state", "outputs": [{"internalType": "enum IGovernor.ProposalState", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IVotes", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newQuorumNumerator", "type": "uint256"}], "name": "updateQuorumNumerator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "voting<PERSON><PERSON>y", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "votingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}