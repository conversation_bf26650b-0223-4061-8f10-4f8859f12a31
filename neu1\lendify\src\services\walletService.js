import web3Service from './web3Service'

// Wallet configurations
export const WALLET_PROVIDERS = {
  metamask: {
    id: 'metamask',
    name: 'MetaMask',
    icon: '🦊',
    supportedChains: [1, 137, 56, 43114], // Ethereum, Polygon, BSC, Avalanche
    isInstalled: () => typeof window !== 'undefined' && window.ethereum && window.ethereum.isMetaMask,
    connect: async () => {
      if (!window.ethereum) {
        throw new Error('MetaMask not installed')
      }
      return await web3Service.connectWallet()
    }
  },
  phantom: {
    id: 'phantom',
    name: 'Phantom',
    icon: '👻',
    supportedChains: [101], // Solana
    isInstalled: () => typeof window !== 'undefined' && window.solana && window.solana.isPhantom,
    connect: async () => {
      if (!window.solana) {
        throw new Error('Phantom wallet not installed')
      }
      try {
        const response = await window.solana.connect()
        return {
          address: response.publicKey.toString(),
          chainId: 101,
          chainName: '<PERSON>ana'
        }
      } catch (error) {
        throw new Error('Failed to connect to Phantom wallet')
      }
    }
  },
  coinbase: {
    id: 'coinbase',
    name: 'Coinbase Wallet',
    icon: '🔵',
    supportedChains: [1, 137], // Ethereum, Polygon
    isInstalled: () => typeof window !== 'undefined' && window.ethereum && window.ethereum.isCoinbaseWallet,
    connect: async () => {
      if (!window.ethereum || !window.ethereum.isCoinbaseWallet) {
        throw new Error('Coinbase Wallet not installed')
      }
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })
      const chainId = await window.ethereum.request({ method: 'eth_chainId' })
      return {
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        chainName: web3Service.getChainName(parseInt(chainId, 16))
      }
    }
  },
  trust: {
    id: 'trust',
    name: 'Trust Wallet',
    icon: '🛡️',
    supportedChains: [1, 56], // Ethereum, BSC
    isInstalled: () => typeof window !== 'undefined' && window.ethereum && window.ethereum.isTrust,
    connect: async () => {
      if (!window.ethereum || !window.ethereum.isTrust) {
        throw new Error('Trust Wallet not installed')
      }
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })
      const chainId = await window.ethereum.request({ method: 'eth_chainId' })
      return {
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        chainName: web3Service.getChainName(parseInt(chainId, 16))
      }
    }
  },
  walletconnect: {
    id: 'walletconnect',
    name: 'WalletConnect',
    icon: '🔗',
    supportedChains: [1, 137, 56, 43114], // Multi-chain support
    isInstalled: () => true, // WalletConnect is always available
    connect: async () => {
      throw new Error('WalletConnect integration coming soon')
    }
  }
}

class WalletService {
  constructor() {
    this.connectedWallets = new Map()
    this.listeners = {}
    this.currentWallet = null
  }

  // Get all available wallet providers
  getAvailableWallets() {
    return Object.values(WALLET_PROVIDERS).map(wallet => ({
      ...wallet,
      isInstalled: wallet.isInstalled(),
      status: this.connectedWallets.has(wallet.id) ? 'connected' : 'disconnected'
    }))
  }

  // Connect to a specific wallet
  async connectWallet(walletId) {
    try {
      const wallet = WALLET_PROVIDERS[walletId]
      if (!wallet) {
        throw new Error(`Wallet ${walletId} not supported`)
      }

      if (!wallet.isInstalled()) {
        throw new Error(`${wallet.name} is not installed`)
      }

      const connection = await wallet.connect()
      
      // Store connection info
      this.connectedWallets.set(walletId, {
        ...connection,
        walletId,
        connectedAt: new Date()
      })

      // Set as current wallet if it's the first one
      if (!this.currentWallet) {
        this.currentWallet = walletId
      }

      this.emit('walletConnected', { walletId, connection })
      
      return connection
    } catch (error) {
      console.error(`Error connecting to ${walletId}:`, error)
      throw error
    }
  }

  // Disconnect a wallet
  async disconnectWallet(walletId) {
    try {
      if (this.connectedWallets.has(walletId)) {
        this.connectedWallets.delete(walletId)
        
        // If this was the current wallet, switch to another or clear
        if (this.currentWallet === walletId) {
          const remaining = Array.from(this.connectedWallets.keys())
          this.currentWallet = remaining.length > 0 ? remaining[0] : null
        }

        this.emit('walletDisconnected', { walletId })
      }
    } catch (error) {
      console.error(`Error disconnecting ${walletId}:`, error)
      throw error
    }
  }

  // Get connected wallets
  getConnectedWallets() {
    return Array.from(this.connectedWallets.entries()).map(([walletId, connection]) => ({
      walletId,
      ...connection,
      wallet: WALLET_PROVIDERS[walletId]
    }))
  }

  // Get current active wallet
  getCurrentWallet() {
    if (!this.currentWallet) return null
    
    const connection = this.connectedWallets.get(this.currentWallet)
    return connection ? {
      walletId: this.currentWallet,
      ...connection,
      wallet: WALLET_PROVIDERS[this.currentWallet]
    } : null
  }

  // Switch active wallet
  switchWallet(walletId) {
    if (this.connectedWallets.has(walletId)) {
      this.currentWallet = walletId
      this.emit('walletSwitched', { walletId })
      return true
    }
    return false
  }

  // Check if a wallet supports a specific chain
  isChainSupported(walletId, chainId) {
    const wallet = WALLET_PROVIDERS[walletId]
    return wallet ? wallet.supportedChains.includes(chainId) : false
  }

  // Get wallets that support a specific chain
  getWalletsForChain(chainId) {
    return Object.values(WALLET_PROVIDERS).filter(wallet => 
      wallet.supportedChains.includes(chainId)
    )
  }

  // Auto-detect and connect to available wallets
  async autoDetectWallets() {
    const results = []
    
    for (const wallet of Object.values(WALLET_PROVIDERS)) {
      if (wallet.isInstalled()) {
        try {
          // For MetaMask, check if already connected
          if (wallet.id === 'metamask' && window.ethereum) {
            const accounts = await window.ethereum.request({ method: 'eth_accounts' })
            if (accounts.length > 0) {
              const chainId = await window.ethereum.request({ method: 'eth_chainId' })
              const connection = {
                address: accounts[0],
                chainId: parseInt(chainId, 16),
                chainName: web3Service.getChainName(parseInt(chainId, 16))
              }
              
              this.connectedWallets.set(wallet.id, {
                ...connection,
                walletId: wallet.id,
                connectedAt: new Date()
              })
              
              if (!this.currentWallet) {
                this.currentWallet = wallet.id
              }
              
              results.push({ walletId: wallet.id, status: 'connected', connection })
            }
          }
        } catch (error) {
          console.error(`Error auto-detecting ${wallet.name}:`, error)
        }
      }
    }
    
    return results
  }

  // Get wallet connection status
  getWalletStatus(walletId) {
    return this.connectedWallets.has(walletId) ? 'connected' : 'disconnected'
  }

  // Event emitter functionality
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data))
    }
  }

  on(event, callback) {
    if (!this.listeners[event]) this.listeners[event] = []
    this.listeners[event].push(callback)
  }

  off(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback)
    }
  }
}

// Create singleton instance
const walletService = new WalletService()

export default walletService
export { WalletService }
