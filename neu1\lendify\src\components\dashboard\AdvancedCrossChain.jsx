import React, { useState, useEffect } from 'react'
import './AdvancedCrossChain.css'
import web3Service from '../../services/web3Service'
import crossChainService from '../../services/crossChainService'
import walletService from '../../services/walletService'
import { useAuth } from '../../hooks/useAuth'

const AdvancedCrossChain = ({ userStats, setUserStats }) => {
  const { walletAddress } = useAuth()
  const [activeTab, setActiveTab] = useState('bridge')
  const [selectedFromChain, setSelectedFromChain] = useState(1) // Ethereum
  const [selectedToChain, setSelectedToChain] = useState(137) // Polygon
  const [bridgeAmount, setBridgeAmount] = useState('')
  const [selectedNFT, setSelectedNFT] = useState(null)
  const [networkStatus, setNetworkStatus] = useState({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [bridgeTransactions, setBridgeTransactions] = useState([])
  const [userNFTs, setUserNFTs] = useState([])
  const [bridgeQuote, setBridgeQuote] = useState(null)
  const [availableWallets, setAvailableWallets] = useState([])
  const [connectedWallets, setConnectedWallets] = useState([])
  const [realTimeUpdates, setRealTimeUpdates] = useState(true)
  const [lastUpdate, setLastUpdate] = useState(new Date())

  // Get supported chains from Web3 service
  const supportedChains = web3Service.getSupportedChains()

  // Initialize component
  useEffect(() => {
    initializeComponent()
  }, [walletAddress])

  // Set up event listeners and real-time updates
  useEffect(() => {
    const handleTransactionUpdate = (transaction) => {
      setBridgeTransactions(prev => {
        const index = prev.findIndex(tx => tx.id === transaction.id)
        if (index >= 0) {
          const updated = [...prev]
          updated[index] = transaction
          return updated
        } else {
          return [transaction, ...prev]
        }
      })
      setLastUpdate(new Date())
    }

    const handleWalletConnected = () => {
      updateWalletData()
    }

    const handleWalletDisconnected = () => {
      updateWalletData()
    }

    crossChainService.on('transactionUpdate', handleTransactionUpdate)
    walletService.on('walletConnected', handleWalletConnected)
    walletService.on('walletDisconnected', handleWalletDisconnected)

    return () => {
      crossChainService.off('transactionUpdate', handleTransactionUpdate)
      walletService.off('walletConnected', handleWalletConnected)
      walletService.off('walletDisconnected', handleWalletDisconnected)
    }
  }, [])

  // Real-time updates polling
  useEffect(() => {
    if (!realTimeUpdates) return

    const interval = setInterval(async () => {
      try {
        // Update network status
        const status = await crossChainService.getNetworkStatus()
        setNetworkStatus(status)

        // Update bridge transactions if wallet is connected
        if (walletAddress) {
          const history = await crossChainService.getBridgeHistory(walletAddress)
          setBridgeTransactions(history)
        }

        setLastUpdate(new Date())
      } catch (error) {
        console.error('Error during real-time update:', error)
      }
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [realTimeUpdates, walletAddress])

  const initializeComponent = async () => {
    try {
      setLoading(true)
      setError(null)

      // Get network status
      const status = await crossChainService.getNetworkStatus()
      setNetworkStatus(status)

      // Initialize wallet data
      await updateWalletData()

      // Load user's bridge history if wallet is connected
      if (walletAddress) {
        const history = await crossChainService.getBridgeHistory(walletAddress)
        setBridgeTransactions(history)

        // Load user's NFTs for current chain
        const currentChain = web3Service.getCurrentChain()
        if (currentChain) {
          const nfts = await crossChainService.getUserNFTs(walletAddress, currentChain)
          setUserNFTs(nfts)
        }
      }
    } catch (err) {
      console.error('Error initializing cross-chain component:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const updateWalletData = async () => {
    try {
      // Auto-detect connected wallets
      await walletService.autoDetectWallets()

      // Get available wallets
      const available = walletService.getAvailableWallets()
      setAvailableWallets(available)

      // Get connected wallets
      const connected = walletService.getConnectedWallets()
      setConnectedWallets(connected)
    } catch (err) {
      console.error('Error updating wallet data:', err)
    }
  }

  // Cross-chain NFT assets
  const crossChainNFTs = [
    {
      id: 1,
      name: 'Cosmic Warrior #1234',
      collection: 'MetaGaming Heroes',
      currentChain: 'ethereum',
      availableChains: ['polygon', 'bsc'],
      estimatedValue: '2.5 ETH',
      utility: ['Gaming', 'Staking', 'Governance'],
      bridgeCost: '0.003 ETH'
    },
    {
      id: 2,
      name: 'Digital Land Parcel #567',
      collection: 'Virtual Worlds',
      currentChain: 'polygon',
      availableChains: ['ethereum', 'avalanche'],
      estimatedValue: '1.8 ETH',
      utility: ['Metaverse', 'Rental Income'],
      bridgeCost: '2.5 MATIC'
    },
    {
      id: 3,
      name: 'Rare Avatar #890',
      collection: 'Avatar Collection',
      currentChain: 'bsc',
      availableChains: ['ethereum', 'polygon', 'solana'],
      estimatedValue: '0.9 ETH',
      utility: ['Profile', 'Social Status'],
      bridgeCost: '0.001 BNB'
    }
  ]

  // Bridge transactions history
  const bridgeHistory = [
    {
      id: 1,
      type: 'NFT Bridge',
      asset: 'Gaming Sword #123',
      fromChain: 'ethereum',
      toChain: 'polygon',
      status: 'completed',
      timestamp: '2 hours ago',
      txHash: '0xabc123...'
    },
    {
      id: 2,
      type: 'Token Bridge',
      asset: '50 USDC',
      fromChain: 'polygon',
      toChain: 'ethereum',
      status: 'pending',
      timestamp: '1 hour ago',
      txHash: '0xdef456...'
    },
    {
      id: 3,
      type: 'NFT Bridge',
      asset: 'Land Plot #456',
      fromChain: 'bsc',
      toChain: 'avalanche',
      status: 'completed',
      timestamp: '5 hours ago',
      txHash: '0xghi789...'
    }
  ]

  // Multi-wallet management
  const walletOptions = [
    {
      id: 'metamask',
      name: 'MetaMask',
      chains: ['ethereum', 'polygon', 'bsc', 'avalanche'],
      icon: '🦊',
      status: 'connected'
    },
    {
      id: 'phantom',
      name: 'Phantom',
      chains: ['solana'],
      icon: '👻',
      status: 'disconnected'
    },
    {
      id: 'coinbase',
      name: 'Coinbase Wallet',
      chains: ['ethereum', 'polygon'],
      icon: '🔵',
      status: 'disconnected'
    },
    {
      id: 'trust',
      name: 'Trust Wallet',
      chains: ['ethereum', 'bsc'],
      icon: '🛡️',
      status: 'disconnected'
    }
  ]

  const handleChainSwitch = async (chainId) => {
    try {
      setLoading(true)
      setError(null)

      await crossChainService.connectToNetwork(chainId)

      // Update network status
      const status = await crossChainService.getNetworkStatus()
      setNetworkStatus(status)

      // Reload user NFTs for new chain
      if (walletAddress) {
        const nfts = await crossChainService.getUserNFTs(walletAddress, chainId)
        setUserNFTs(nfts)
      }
    } catch (err) {
      console.error('Error switching chain:', err)
      setError(`Failed to switch to network: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleWalletConnect = async (walletId) => {
    try {
      setLoading(true)
      setError(null)

      const connection = await walletService.connectWallet(walletId)

      // Show success message
      const wallet = availableWallets.find(w => w.id === walletId)
      alert(`Successfully connected to ${wallet?.name}!\nAddress: ${connection.address}`)

      // Refresh component data
      await initializeComponent()
    } catch (err) {
      console.error('Error connecting wallet:', err)
      setError(`Failed to connect wallet: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleWalletDisconnect = async (walletId) => {
    try {
      setLoading(true)
      setError(null)

      await walletService.disconnectWallet(walletId)

      // Refresh component data
      await updateWalletData()
    } catch (err) {
      console.error('Error disconnecting wallet:', err)
      setError(`Failed to disconnect wallet: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleBridgeNFT = async (nft, targetChain) => {
    try {
      setLoading(true)
      setError(null)

      if (!walletAddress) {
        throw new Error('Please connect your wallet first')
      }

      const transaction = await crossChainService.bridgeNFT(
        nft.currentChain,
        targetChain,
        nft.tokenContract,
        nft.tokenId,
        walletAddress,
        'layerzero'
      )

      // Show success message
      alert(`NFT bridge initiated! Transaction ID: ${transaction.id}`)

      // Refresh data
      await initializeComponent()
    } catch (err) {
      console.error('Error bridging NFT:', err)
      setError(`Failed to bridge NFT: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleTokenBridge = async () => {
    try {
      // Validation
      if (!bridgeAmount) {
        setError('Please enter amount to bridge')
        return
      }

      if (!walletAddress) {
        setError('Please connect your wallet first')
        return
      }

      const numericAmount = parseFloat(bridgeAmount)
      if (isNaN(numericAmount) || numericAmount <= 0) {
        setError('Please enter a valid positive amount')
        return
      }

      if (selectedFromChain === selectedToChain) {
        setError('Source and destination chains must be different')
        return
      }

      setLoading(true)
      setError(null)

      // Show confirmation dialog
      const fromChain = supportedChains.find(c => c.chainId === selectedFromChain)
      const toChain = supportedChains.find(c => c.chainId === selectedToChain)

      const confirmed = window.confirm(
        `Confirm Token Bridge:\n\n` +
        `Amount: ${bridgeAmount} ${fromChain?.symbol}\n` +
        `From: ${fromChain?.name}\n` +
        `To: ${toChain?.name}\n` +
        `Destination: ${walletAddress}\n\n` +
        `This action cannot be undone. Continue?`
      )

      if (!confirmed) {
        setLoading(false)
        return
      }

      // Get bridge quote first
      try {
        const quote = await crossChainService.getBridgeQuote(
          selectedFromChain,
          selectedToChain,
          'native', // For native tokens
          null
        )
        setBridgeQuote(quote)
      } catch (quoteError) {
        console.warn('Failed to get bridge quote:', quoteError)
        // Continue without quote
      }

      // Execute bridge transaction
      const transaction = await crossChainService.bridgeToken(
        selectedFromChain,
        selectedToChain,
        'native',
        bridgeAmount,
        walletAddress
      )

      // Show success message with transaction details
      alert(
        `Token bridge initiated successfully!\n\n` +
        `Transaction ID: ${transaction.id}\n` +
        `Status: ${transaction.status}\n` +
        `${transaction.txHash ? `TX Hash: ${transaction.txHash}` : ''}`
      )

      // Clear form and refresh data
      setBridgeAmount('')
      setBridgeQuote(null)
      await initializeComponent()
    } catch (err) {
      console.error('Error bridging tokens:', err)
      setError(`Failed to bridge tokens: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="advanced-crosschain">
      <div className="crosschain-header">
        <div className="header-content">
          <h2>🌉 Cross-Chain Infrastructure</h2>
          <p>Bridge assets across multiple blockchains seamlessly</p>
        </div>
        <div className="real-time-status">
          <div className="status-indicator">
            <span className={`status-dot ${realTimeUpdates ? 'active' : 'inactive'}`}></span>
            <span className="status-text">
              {realTimeUpdates ? 'Live Updates' : 'Updates Paused'}
            </span>
          </div>
          <div className="last-update">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
          <button
            className="toggle-updates-btn"
            onClick={() => setRealTimeUpdates(!realTimeUpdates)}
          >
            {realTimeUpdates ? 'Pause' : 'Resume'}
          </button>
        </div>
        {error && (
          <div className="error-message" style={{
            background: 'rgba(255, 0, 0, 0.1)',
            border: '1px solid rgba(255, 0, 0, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            margin: '12px 0',
            color: '#ff6b6b'
          }}>
            {error}
          </div>
        )}
        {loading && (
          <div className="loading-message" style={{
            background: 'rgba(102, 126, 234, 0.1)',
            border: '1px solid rgba(102, 126, 234, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            margin: '12px 0',
            color: '#667eea'
          }}>
            Loading...
          </div>
        )}
      </div>

      <div className="crosschain-tabs">
        <button
          className={`crosschain-tab ${activeTab === 'bridge' ? 'active' : ''}`}
          onClick={() => setActiveTab('bridge')}
        >
          Asset Bridge
        </button>
        <button
          className={`crosschain-tab ${activeTab === 'nft-bridge' ? 'active' : ''}`}
          onClick={() => setActiveTab('nft-bridge')}
        >
          NFT Bridge
        </button>
        <button
          className={`crosschain-tab ${activeTab === 'wallets' ? 'active' : ''}`}
          onClick={() => setActiveTab('wallets')}
        >
          Multi-Wallet
        </button>
        <button
          className={`crosschain-tab ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          Bridge History
        </button>
      </div>

      {/* Chain Status Overview */}
      <div className="chain-status-overview">
        <h3>Network Status</h3>
        <div className="chains-grid">
          {supportedChains.map(chain => {
            const status = networkStatus[chain.chainId] || {}
            return (
              <div key={chain.chainId} className="chain-card">
                <div className="chain-header">
                  <span className="chain-icon" style={{color: chain.color}}>
                    {chain.icon}
                  </span>
                  <span className="chain-name">{chain.name}</span>
                  <div className={`connection-status ${status.connected ? 'connected' : 'disconnected'}`}>
                    {status.connected ? '🟢' : '🔴'}
                  </div>
                </div>
              
                <div className="chain-metrics">
                  <div className="metric">
                    <span>Gas:</span>
                    <span>{chain.gasPrice}</span>
                  </div>
                  <div className="metric">
                    <span>Bridge Fee:</span>
                    <span>{chain.bridgeFee}</span>
                  </div>
                  <div className="metric">
                    <span>Time:</span>
                    <span>{chain.avgTime}</span>
                  </div>
                  <div className="metric">
                    <span>Health:</span>
                    <span style={{color: status.healthy ? '#4ade80' : '#f87171'}}>
                      {status.healthy ? 'Healthy' : 'Offline'}
                    </span>
                  </div>
                </div>

                <button
                  className="chain-action-btn"
                  onClick={() => handleChainSwitch(chain.chainId)}
                  disabled={status.connected || loading}
                >
                  {status.connected ? 'Connected' : 'Connect'}
                </button>
              </div>
            )
          })}
        </div>
      </div>

      {/* Asset Bridge Section */}
      {activeTab === 'bridge' && (
        <div className="bridge-section">
          <div className="bridge-interface">
            <h3>🔄 Token Bridge</h3>
            
            <div className="bridge-form">
              <div className="bridge-row">
                <div className="chain-selector">
                  <label>From Chain</label>
                  <select
                    value={selectedFromChain}
                    onChange={(e) => setSelectedFromChain(parseInt(e.target.value))}
                  >
                    {supportedChains.map(chain => (
                      <option key={chain.chainId} value={chain.chainId}>
                        {chain.icon} {chain.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="bridge-arrow">⇄</div>

                <div className="chain-selector">
                  <label>To Chain</label>
                  <select
                    value={selectedToChain}
                    onChange={(e) => setSelectedToChain(parseInt(e.target.value))}
                  >
                    {supportedChains
                      .filter(chain => chain.chainId !== selectedFromChain)
                      .map(chain => (
                        <option key={chain.chainId} value={chain.chainId}>
                          {chain.icon} {chain.name}
                        </option>
                      ))}
                  </select>
                </div>
              </div>

              <div className="amount-input">
                <label>Amount to Bridge</label>
                <input 
                  type="number"
                  value={bridgeAmount}
                  onChange={(e) => setBridgeAmount(e.target.value)}
                  placeholder="Enter amount"
                />
                <span className="token-symbol">
                  {supportedChains.find(c => c.chainId === selectedFromChain)?.symbol}
                </span>
              </div>

              <div className="bridge-summary">
                <div className="summary-row">
                  <span>Bridge Fee:</span>
                  <span>{supportedChains.find(c => c.chainId === selectedFromChain)?.bridgeFee}</span>
                </div>
                <div className="summary-row">
                  <span>Estimated Time:</span>
                  <span>{supportedChains.find(c => c.chainId === selectedToChain)?.avgTime}</span>
                </div>
                <div className="summary-row">
                  <span>You'll Receive:</span>
                  <span className="receive-amount">
                    ~{bridgeAmount || '0'} {supportedChains.find(c => c.chainId === selectedToChain)?.symbol}
                  </span>
                </div>
                {bridgeQuote && (
                  <div className="summary-row">
                    <span>Quote:</span>
                    <span>{bridgeQuote[0]?.fees?.total || 'Loading...'}</span>
                  </div>
                )}
              </div>

              <button
                className="bridge-execute-btn"
                onClick={handleTokenBridge}
                disabled={!bridgeAmount || loading}
              >
                {loading ? 'Processing...' : 'Bridge Tokens'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* NFT Bridge Section */}
      {activeTab === 'nft-bridge' && (
        <div className="nft-bridge-section">
          <div className="nft-bridge-header">
            <h3>🖼️ NFT Cross-Chain Bridge</h3>
            <p>Move your NFTs across different blockchains</p>
          </div>

          <div className="nft-bridge-grid">
            {userNFTs.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: '12px',
                gridColumn: '1 / -1'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🖼️</div>
                <h4 style={{ color: 'white', marginBottom: '0.5rem' }}>No NFTs Found</h4>
                <p style={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                  {walletAddress ? 'Connect to a network with NFTs or mint some NFTs first' : 'Connect your wallet to view your NFTs'}
                </p>
              </div>
            ) : (
              userNFTs.map(nft => {
                const currentChainConfig = supportedChains.find(c => c.chainId === nft.currentChain)
                return (
                  <div key={nft.id} className="nft-bridge-card">
                    <div className="nft-bridge-info">
                      <h4>{nft.name}</h4>
                      <p>{nft.collection}</p>
                      <div className="current-chain">
                        <span className="chain-indicator">
                          {currentChainConfig?.icon}
                        </span>
                        <span>Current: {currentChainConfig?.name}</span>
                      </div>
                    </div>

                    <div className="nft-metrics">
                      <div className="metric">
                        <span>Value:</span>
                        <span>{nft.estimatedValue}</span>
                      </div>
                      <div className="metric">
                        <span>Bridge Cost:</span>
                        <span>{nft.bridgeCost}</span>
                      </div>
                    </div>

                    <div className="utility-tags">
                      {nft.utility?.map(util => (
                        <span key={util} className="utility-tag">{util}</span>
                      ))}
                    </div>

                    <div className="available-chains">
                      <span className="available-label">Bridge to:</span>
                      <div className="chain-options">
                        {nft.availableChains?.map(chainId => {
                          const chain = supportedChains.find(c => c.chainId === chainId)
                          return (
                            <button
                              key={chainId}
                              className="chain-option"
                              onClick={() => handleBridgeNFT(nft, chainId)}
                              style={{borderColor: chain?.color}}
                              disabled={loading}
                            >
                              <span style={{color: chain?.color}}>{chain?.icon}</span>
                              {chain?.name}
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                )
              })
            )}
          </div>
        </div>
      )}

      {/* Multi-Wallet Section */}
      {activeTab === 'wallets' && (
        <div className="wallet-section">
          <div className="wallet-header">
            <h3>💼 Multi-Wallet Management</h3>
            <p>Connect multiple wallets for different blockchains</p>
          </div>

          <div className="wallet-grid">
            {availableWallets.map(wallet => (
              <div key={wallet.id} className="wallet-card">
                <div className="wallet-info">
                  <span className="wallet-icon">{wallet.icon}</span>
                  <div className="wallet-details">
                    <h4>{wallet.name}</h4>
                    <div className="wallet-install-status">
                      {wallet.isInstalled ? (
                        <span style={{color: '#4ade80'}}>✓ Installed</span>
                      ) : (
                        <span style={{color: '#f87171'}}>Not Installed</span>
                      )}
                    </div>
                    <div className="supported-chains">
                      {wallet.supportedChains.map(chainId => {
                        const chain = supportedChains.find(c => c.chainId === chainId)
                        return (
                          <span
                            key={chainId}
                            className="supported-chain"
                            style={{color: chain?.color}}
                            title={chain?.name}
                          >
                            {chain?.icon}
                          </span>
                        )
                      })}
                    </div>
                  </div>
                </div>

                <div className="wallet-status">
                  <div className={`status-indicator ${wallet.status}`}>
                    {wallet.status === 'connected' ? '🟢' : '🔴'}
                  </div>
                  <span className={`status-text ${wallet.status}`}>
                    {wallet.status === 'connected' ? 'Connected' : 'Disconnected'}
                  </span>
                </div>

                <div className="wallet-actions">
                  {wallet.status === 'connected' ? (
                    <button
                      className="wallet-action-btn disconnect"
                      onClick={() => handleWalletDisconnect(wallet.id)}
                      disabled={loading}
                    >
                      Disconnect
                    </button>
                  ) : (
                    <button
                      className="wallet-action-btn connect"
                      onClick={() => handleWalletConnect(wallet.id)}
                      disabled={!wallet.isInstalled || loading}
                    >
                      {!wallet.isInstalled ? 'Install Wallet' : 'Connect Wallet'}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Connected Wallets Summary */}
          {connectedWallets.length > 0 && (
            <div className="connected-wallets-summary">
              <h4>Connected Wallets ({connectedWallets.length})</h4>
              <div className="connected-wallets-list">
                {connectedWallets.map(wallet => (
                  <div key={wallet.walletId} className="connected-wallet-item">
                    <div className="wallet-info">
                      <span className="wallet-icon">{wallet.wallet.icon}</span>
                      <div className="wallet-details">
                        <div className="wallet-name">{wallet.wallet.name}</div>
                        <div className="wallet-address">
                          {wallet.address.substring(0, 6)}...{wallet.address.substring(wallet.address.length - 4)}
                        </div>
                        <div className="wallet-chain">
                          {supportedChains.find(c => c.chainId === wallet.chainId)?.name || `Chain ${wallet.chainId}`}
                        </div>
                      </div>
                    </div>
                    <div className="wallet-actions">
                      <button
                        className="switch-wallet-btn"
                        onClick={() => walletService.switchWallet(wallet.walletId)}
                      >
                        Switch
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="wallet-actions">
            <button
              className="action-btn primary"
              onClick={() => setError('Custom wallet integration coming soon')}
            >
              Add Custom Wallet
            </button>
            <button
              className="action-btn secondary"
              onClick={() => setError('Permission management coming soon')}
            >
              Manage Permissions
            </button>
          </div>
        </div>
      )}

      {/* Bridge History Section */}
      {activeTab === 'history' && (
        <div className="history-section">
          <div className="history-header">
            <h3>📋 Bridge History</h3>
            <p>Track all your cross-chain transactions</p>
          </div>

          <div className="history-filters">
            <select className="filter-select">
              <option>All Chains</option>
              <option>Ethereum</option>
              <option>Polygon</option>
              <option>BSC</option>
            </select>
            <select className="filter-select">
              <option>All Types</option>
              <option>NFT Bridge</option>
              <option>Token Bridge</option>
            </select>
          </div>

          <div className="history-list">
            {bridgeTransactions.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: '12px'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
                <h4 style={{ color: 'white', marginBottom: '0.5rem' }}>No Bridge History</h4>
                <p style={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                  {walletAddress ? 'Start bridging assets to see your transaction history' : 'Connect your wallet to view bridge history'}
                </p>
              </div>
            ) : (
              bridgeTransactions.map(transaction => {
                const fromChain = supportedChains.find(c => c.chainId === transaction.fromChain)
                const toChain = supportedChains.find(c => c.chainId === transaction.toChain)
                return (
                  <div key={transaction.id} className="history-item">
                    <div className="transaction-info">
                      <div className="transaction-type">{transaction.type}</div>
                      <div className="transaction-asset">{transaction.asset || `${transaction.amount || '1'} ${transaction.tokenId ? 'NFT' : 'Token'}`}</div>
                      <div className="transaction-route">
                      <span className="from-chain">
                          {fromChain?.icon} 
                          {fromChain?.name}
                        </span>
                        <span className="arrow">→</span>
                        <span className="to-chain">
                          {toChain?.icon}
                          {toChain?.name}
                        </span>
                      </div>
                    </div>

                    <div className="transaction-status">
                      <div className={`status-badge ${transaction.status}`}>
                        {transaction.status === 'completed' ? '✅' :
                         transaction.status === 'pending' ? '🔄' :
                         transaction.status === 'confirmed' ? '⏳' :
                         transaction.status === 'bridging' ? '🌉' : '❌'}
                        {transaction.status}
                      </div>
                      <div className="transaction-time">
                        {transaction.timestamp ?
                          new Date(transaction.timestamp).toLocaleString() :
                          transaction.createdAt ?
                          new Date(transaction.createdAt).toLocaleString() :
                          'Unknown'
                        }
                      </div>
                </div>

                    <div className="transaction-actions">
                      <button
                        className="view-tx-btn"
                        onClick={() => {
                          if (transaction.txHash) {
                            const chain = supportedChains.find(c => c.chainId === transaction.fromChain)
                            if (chain) {
                              window.open(`${chain.blockExplorerUrl}/tx/${transaction.txHash}`, '_blank')
                            }
                          }
                        }}
                        disabled={!transaction.txHash}
                      >
                        View TX
                      </button>
                    </div>
                  </div>
                )
              })
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default AdvancedCrossChain