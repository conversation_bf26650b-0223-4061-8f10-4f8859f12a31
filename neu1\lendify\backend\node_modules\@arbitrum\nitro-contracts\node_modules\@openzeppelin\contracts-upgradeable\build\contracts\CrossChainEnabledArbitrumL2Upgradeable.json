{"_format": "hh-sol-artifact-1", "contractName": "CrossChainEnabledArbitrumL2Upgradeable", "sourceName": "contracts/crosschain/arbitrum/CrossChainEnabledArbitrumL2Upgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}