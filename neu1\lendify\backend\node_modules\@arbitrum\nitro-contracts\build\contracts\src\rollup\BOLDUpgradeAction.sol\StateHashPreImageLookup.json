{"_format": "hh-sol-artifact-1", "contractName": "StateHashPreImageLookup", "sourceName": "src/rollup/BOLDUpgradeAction.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "h", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "indexed": false, "internalType": "struct ExecutionState", "name": "executionState", "type": "tuple"}, {"indexed": false, "internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}], "name": "HashSet", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "h", "type": "bytes32"}], "name": "get", "outputs": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "executionState", "type": "tuple"}, {"internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "h", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "executionState", "type": "tuple"}, {"internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}], "name": "set", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "executionState", "type": "tuple"}, {"internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}], "name": "stateHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}