import { ethers, JsonRpcProvider, Wallet, Contract } from 'ethers';
export interface ChainConfig {
    chainId: number;
    name: string;
    rpcUrl: string;
    currency: string;
    blockExplorerUrl: string;
    contracts: {
        nftRentalMarketplace?: string;
        reputationSystem?: string;
        nftCollateralizedLending?: string;
        flashRentalLoan?: string;
        dynamicPricingOracle?: string;
    };
}
export interface TransactionResult {
    success: boolean;
    txHash?: string;
    receipt?: ethers.TransactionReceipt;
    error?: string;
    gasUsed?: bigint;
    effectiveGasPrice?: bigint;
}
export interface NFTInfo {
    tokenId: string;
    owner: string;
    approved: string;
    tokenURI: string;
}
export interface RentalInfo {
    listingId: number;
    nftContract: string;
    tokenId: number;
    owner: string;
    user: string;
    pricePerDay: bigint;
    collateral: bigint;
    duration: number;
    expires: number;
    isActive: boolean;
    category: string;
}
export declare class Web3Service {
    private providers;
    private signers;
    private contracts;
    private chainConfigs;
    constructor();
    private initializeChainConfigs;
    initialize(): Promise<void>;
    private initializeContractsForChain;
    getProvider(chainId: number): JsonRpcProvider | undefined;
    getSigner(chainId: number): Wallet | undefined;
    getContract(contractType: string, chainId: number): Contract | undefined;
    getSupportedChains(): ChainConfig[];
    getChainConfig(chainId: number): ChainConfig | undefined;
    getNFTInfo(nftContract: string, tokenId: string, chainId: number): Promise<NFTInfo>;
    listNFTForRent(nftContract: string, tokenId: number, pricePerDay: string, collateral: string, duration: number, category: string, chainId: number): Promise<TransactionResult>;
    rentNFT(listingId: number, chainId: number, paymentAmount: string): Promise<TransactionResult>;
    getRentalInfo(listingId: number, chainId: number): Promise<RentalInfo | null>;
    requestLoan(nftContract: string, tokenId: number, loanAmount: string, duration: number, chainId: number): Promise<TransactionResult>;
    fundLoan(loanId: number, chainId: number, fundAmount: string): Promise<TransactionResult>;
    executeFlashRental(nftContract: string, tokenId: number, rentalDuration: number, data: string, chainId: number): Promise<TransactionResult>;
    getUserReputation(userAddress: string, chainId: number): Promise<any>;
    getDynamicPrice(nftContract: string, tokenId: number, chainId: number): Promise<string | null>;
    estimateGas(contract: Contract, methodName: string, params: any[]): Promise<bigint>;
    waitForTransaction(txHash: string, chainId: number, confirmations?: number): Promise<ethers.TransactionReceipt | null>;
    getContractEvents(contractType: string, chainId: number, eventName: string, fromBlock?: number, toBlock?: number): Promise<any[]>;
    getBlockNumber(chainId: number): Promise<number>;
    getBalance(address: string, chainId: number): Promise<string>;
    getGasPrice(chainId: number): Promise<string>;
    healthCheck(): Promise<{
        [chainId: number]: boolean;
    }>;
}
//# sourceMappingURL=Web3Service.d.ts.map