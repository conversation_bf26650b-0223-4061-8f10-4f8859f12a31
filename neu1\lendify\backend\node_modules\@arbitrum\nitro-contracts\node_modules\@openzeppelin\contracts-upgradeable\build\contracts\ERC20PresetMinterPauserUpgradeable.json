{"_format": "hh-sol-artifact-1", "contractName": "ERC20PresetMinterPauserUpgradeable", "sourceName": "contracts/token/ERC20/presets/ERC20PresetMinterPauserUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}