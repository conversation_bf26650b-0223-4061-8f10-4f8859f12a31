{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220ac289c6da9cb9cd6302e404e6d39b52e8d2fb632e8a096a3e00fddde2fdc08b464736f6c63430008110033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220ac289c6da9cb9cd6302e404e6d39b52e8d2fb632e8a096a3e00fddde2fdc08b464736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}