{"_format": "hh-sol-artifact-1", "contractName": "MessageTester", "sourceName": "src/test-helpers/MessageTester.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "inbox", "type": "bytes32"}, {"internalType": "bytes32", "name": "message", "type": "bytes32"}], "name": "accumulateInboxMessage", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "messageType", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint64", "name": "blockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint256", "name": "inboxSeqNum", "type": "uint256"}, {"internalType": "uint256", "name": "gasPriceL1", "type": "uint256"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "messageHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b50610267806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c80638f3c79c01461003b578063bf00905214610087575b600080fd5b610075610049366004610160565b604080516020808201949094528082019290925280518083038201815260609092019052805191012090565b60405190815260200160405180910390f35b61007561009536600461019f565b6040805160f89890981b7fff00000000000000000000000000000000000000000000000000000000000000166020808a019190915260609790971b7fffffffffffffffffffffffffffffffffffffffff00000000000000000000000016602189015260c095861b7fffffffffffffffff00000000000000000000000000000000000000000000000090811660358a01529490951b909316603d870152604586019190915260658501526085808501919091528151808503909101815260a59093019052815191012090565b6000806040838503121561017357600080fd5b50508035926020909101359150565b803567ffffffffffffffff8116811461019a57600080fd5b919050565b600080600080600080600060e0888a0312156101ba57600080fd5b873560ff811681146101cb57600080fd5b9650602088013573ffffffffffffffffffffffffffffffffffffffff811681146101f457600080fd5b955061020260408901610182565b945061021060608901610182565b9699959850939660808101359560a0820135955060c090910135935091505056fea26469706673582212201ef985b7b2d2e83c32bfb5f9e119656b406db072cdf368ec48e42d856ece852b64736f6c63430008110033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100365760003560e01c80638f3c79c01461003b578063bf00905214610087575b600080fd5b610075610049366004610160565b604080516020808201949094528082019290925280518083038201815260609092019052805191012090565b60405190815260200160405180910390f35b61007561009536600461019f565b6040805160f89890981b7fff00000000000000000000000000000000000000000000000000000000000000166020808a019190915260609790971b7fffffffffffffffffffffffffffffffffffffffff00000000000000000000000016602189015260c095861b7fffffffffffffffff00000000000000000000000000000000000000000000000090811660358a01529490951b909316603d870152604586019190915260658501526085808501919091528151808503909101815260a59093019052815191012090565b6000806040838503121561017357600080fd5b50508035926020909101359150565b803567ffffffffffffffff8116811461019a57600080fd5b919050565b600080600080600080600060e0888a0312156101ba57600080fd5b873560ff811681146101cb57600080fd5b9650602088013573ffffffffffffffffffffffffffffffffffffffff811681146101f457600080fd5b955061020260408901610182565b945061021060608901610182565b9699959850939660808101359560a0820135955060c090910135935091505056fea26469706673582212201ef985b7b2d2e83c32bfb5f9e119656b406db072cdf368ec48e42d856ece852b64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}