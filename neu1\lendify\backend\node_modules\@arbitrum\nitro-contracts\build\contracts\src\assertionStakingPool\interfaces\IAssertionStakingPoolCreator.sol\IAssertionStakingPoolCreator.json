{"_format": "hh-sol-artifact-1", "contractName": "IAssertionStakingPoolCreator", "sourceName": "src/assertionStakingPool/interfaces/IAssertionStakingPoolCreator.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "rollup", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "_assertionHash", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "assertionPool", "type": "address"}], "name": "NewAssertionPoolCreated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_rollup", "type": "address"}, {"internalType": "bytes32", "name": "_assertionHash", "type": "bytes32"}], "name": "createPool", "outputs": [{"internalType": "contract IAssertionStakingPool", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_rollup", "type": "address"}, {"internalType": "bytes32", "name": "_assertionHash", "type": "bytes32"}], "name": "getPool", "outputs": [{"internalType": "contract IAssertionStakingPool", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}