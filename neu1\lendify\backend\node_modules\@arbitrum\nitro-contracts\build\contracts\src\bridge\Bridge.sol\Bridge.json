{"_format": "hh-sol-artifact-1", "contractName": "Bridge", "sourceName": "src/bridge/Bridge.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "stored", "type": "uint256"}, {"internalType": "uint256", "name": "received", "type": "uint256"}], "name": "BadSequencerMessageNumber", "type": "error"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "InvalidOutboxSet", "type": "error"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "NotContract", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotDelayedInbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotOutbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "NotRollupOrOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotSequencerInbox", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "BridgeCallTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "InboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageIndex", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "beforeInboxAcc", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "kind", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "baseFeeL1", "type": "uint256"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "MessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "OutboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rollup", "type": "address"}], "name": "RollupUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxUpdated", "type": "event"}, {"inputs": [], "name": "acceptFundsFromOldBridge", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "activeOutbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedDelayedInboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}], "name": "allowedDelayedInboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedOutboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "allowedOutboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "delayedInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "delayedMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "enqueueDelayedMessage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "afterDelayedMessagesRead", "type": "uint256"}, {"internalType": "uint256", "name": "prevMessageCount", "type": "uint256"}, {"internalType": "uint256", "name": "newMessageCount", "type": "uint256"}], "name": "enqueueSequencerMessage", "outputs": [{"internalType": "uint256", "name": "seqMessageIndex", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "delayedAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "acc", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "rollup_", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sequencerInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerReportedSubMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newMsgCount", "type": "uint256"}], "name": "setSequencerReportedSubMessageCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "submitBatchSpendingReport", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "_rollup", "type": "address"}], "name": "updateRollupAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60a06040523060805234801561001457600080fd5b50608051611a6e6100306000396000610f270152611a6e6000f3fe60806040526004361061017f5760003560e01c80639e5d4c49116100d6578063d5719dc21161007f578063eca067ad11610059578063eca067ad14610457578063ee35f3271461046c578063f81ff3b31461048c57600080fd5b8063d5719dc214610417578063e76f5c8d14610437578063e77145f41461023457600080fd5b8063c4d66de8116100b0578063c4d66de8146103b7578063cb23bcb5146103d7578063cee3d728146103f757600080fd5b80639e5d4c4914610337578063ab5d894314610365578063ae60bd131461037a57600080fd5b80635fca4a16116101385780638db5993b116101125780638db5993b146102cc578063919cc706146102df578063945e1147146102ff57600080fd5b80635fca4a16146102565780637a88b1071461026c57806386598a561461028c57600080fd5b8063413b35bd11610169578063413b35bd146101c857806347fb24c5146102145780634f61f8501461023657600080fd5b806284120c1461018457806316bf5579146101a8575b600080fd5b34801561019057600080fd5b506007545b6040519081526020015b60405180910390f35b3480156101b457600080fd5b506101956101c336600461175e565b6104ac565b3480156101d457600080fd5b506102046101e336600461178c565b6001600160a01b031660009081526002602052604090206001015460ff1690565b604051901515815260200161019f565b34801561022057600080fd5b5061023461022f3660046117b0565b6104cd565b005b34801561024257600080fd5b5061023461025136600461178c565b6107d3565b34801561026257600080fd5b50610195600a5481565b34801561027857600080fd5b506101956102873660046117ee565b6108ff565b34801561029857600080fd5b506102ac6102a736600461181a565b610960565b60408051948552602085019390935291830152606082015260800161019f565b6101956102da36600461184c565b610af6565b3480156102eb57600080fd5b506102346102fa36600461178c565b610b0c565b34801561030b57600080fd5b5061031f61031a36600461175e565b610c31565b6040516001600160a01b03909116815260200161019f565b34801561034357600080fd5b50610357610352366004611893565b610c5b565b60405161019f929190611940565b34801561037157600080fd5b5061031f610df1565b34801561038657600080fd5b5061020461039536600461178c565b6001600160a01b03166000908152600160208190526040909120015460ff1690565b3480156103c357600080fd5b506102346103d236600461178c565b610e34565b3480156103e357600080fd5b5060085461031f906001600160a01b031681565b34801561040357600080fd5b506102346104123660046117b0565b611058565b34801561042357600080fd5b5061019561043236600461175e565b6113c6565b34801561044357600080fd5b5061031f61045236600461175e565b6113d6565b34801561046357600080fd5b50600654610195565b34801561047857600080fd5b5060095461031f906001600160a01b031681565b34801561049857600080fd5b506102346104a736600461175e565b6113e6565b600781815481106104bc57600080fd5b600091825260209091200154905081565b6008546001600160a01b0316331461059c5760085460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa158015610529573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061054d919061197c565b9050336001600160a01b0382161461059a57600854604051630739600760e01b81523360048201526001600160a01b03918216602482015290821660448201526064015b60405180910390fd5b505b6001600160a01b0382166000818152600160208181526040928390209182015492518515158152919360ff90931692917f6675ce8882cb71637de5903a193d218cc0544be9c0650cb83e0955f6aa2bf521910160405180910390a2821515811515036106085750505050565b82156106a357604080518082018252600380548252600160208084018281526001600160a01b038a166000818152928490529582209451855551938201805460ff1916941515949094179093558154908101825591527fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b01805473ffffffffffffffffffffffffffffffffffffffff191690911790556107cc565b600380546106b390600190611999565b815481106106c3576106c36119ba565b6000918252602090912001548254600380546001600160a01b039093169290919081106106f2576106f26119ba565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b031602179055508160000154600160006003856000015481548110610740576107406119ba565b60009182526020808320909101546001600160a01b031683528201929092526040019020556003805480610776576107766119d0565b600082815260208082208301600019908101805473ffffffffffffffffffffffffffffffffffffffff191690559092019092556001600160a01b03861682526001908190526040822091825501805460ff191690555b50505b5050565b6008546001600160a01b0316331461089d5760085460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa15801561082f573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610853919061197c565b9050336001600160a01b0382161461089b57600854604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610591565b505b6009805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b0383169081179091556040519081527f8c1e6003ed33ca6748d4ad3dd4ecc949065c89dceb31fdf546a5289202763c6a906020015b60405180910390a150565b6009546000906001600160a01b03163314610948576040517f88f84f04000000000000000000000000000000000000000000000000000000008152336004820152602401610591565b610957600d84434248876114b5565b90505b92915050565b6009546000908190819081906001600160a01b031633146109af576040517f88f84f04000000000000000000000000000000000000000000000000000000008152336004820152602401610591565b85600a54141580156109c057508515155b80156109cd5750600a5415155b15610a1257600a546040517fe2051feb000000000000000000000000000000000000000000000000000000008152600481019190915260248101879052604401610591565b600a85905560075493508315610a4d576007610a2f600186611999565b81548110610a3f57610a3f6119ba565b906000526020600020015492505b8615610a7e576006610a60600189611999565b81548110610a7057610a706119ba565b906000526020600020015491505b60408051602081018590529081018990526060810183905260800160408051601f198184030181529190528051602090910120600780546001810182556000919091527fa66cc928b5edb82af9bd49922954155ab7b0942694bea4ce44661d9a8736c688018190559398929750909550919350915050565b6000610b0484848434611687565b949350505050565b6008546001600160a01b03163314610bd65760085460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa158015610b68573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b8c919061197c565b9050336001600160a01b03821614610bd457600854604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610591565b505b6008805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b0383169081179091556040519081527fae1f5aa15f6ff844896347ceca2a3c24c8d3a27785efdeacd581a0a95172784a906020016108f4565b60048181548110610c4157600080fd5b6000918252602090912001546001600160a01b0316905081565b3360009081526002602052604081206001015460609060ff16610cac576040517f32ea82ab000000000000000000000000000000000000000000000000000000008152336004820152602401610591565b8215801590610cc357506001600160a01b0386163b155b15610d05576040517fb5cf5b8f0000000000000000000000000000000000000000000000000000000081526001600160a01b0387166004820152602401610591565b6005805473ffffffffffffffffffffffffffffffffffffffff1981163317909155604080516020601f87018190048102820181019092528581526001600160a01b0390921691610d73918991899189908990819084018382808284376000920191909152506116ef92505050565b6005805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b038581169190911790915560405192955090935088169033907f2d9d115ef3e4a606d698913b1eae831a3cdfe20d9a83d48007b0526749c3d46690610ddf908a908a908a906119e6565b60405180910390a35094509492505050565b6005546000906001600160a01b03167fffffffffffffffffffffffff00000000000000000000000000000000000000018101610e2f57600091505090565b919050565b600054610100900460ff1615808015610e545750600054600160ff909116105b80610e6e5750303b158015610e6e575060005460ff166001145b610efa576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201527f647920696e697469616c697a65640000000000000000000000000000000000006064820152608401610591565b6000805460ff191660011790558015610f1d576000805461ff0019166101001790555b6001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000163003610fd5576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602c60248201527f46756e6374696f6e206d7573742062652063616c6c6564207468726f7567682060448201527f64656c656761746563616c6c00000000000000000000000000000000000000006064820152608401610591565b600580546001600160a01b0373ffffffffffffffffffffffffffffffffffffffff1991821681179092556008805490911691841691909117905580156107cf576000805461ff0019169055604051600181527f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb38474024989060200160405180910390a15050565b6008546001600160a01b031633146111225760085460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa1580156110b4573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906110d8919061197c565b9050336001600160a01b0382161461112057600854604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610591565b505b7fffffffffffffffffffffffff00000000000000000000000000000000000000016001600160a01b0383160161118f576040517f77abed100000000000000000000000000000000000000000000000000000000081526001600160a01b0383166004820152602401610591565b6001600160a01b038216600081815260026020908152604091829020600181015492518515158152909360ff90931692917f49477e7356dbcb654ab85d7534b50126772d938130d1350e23e2540370c8dffa910160405180910390a2821515811515036111fc5750505050565b821561129857604080518082018252600480548252600160208084018281526001600160a01b038a16600081815260029093529582209451855551938201805460ff1916941515949094179093558154908101825591527f8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19b01805473ffffffffffffffffffffffffffffffffffffffff191690911790556107cc565b600480546112a890600190611999565b815481106112b8576112b86119ba565b6000918252602090912001548254600480546001600160a01b039093169290919081106112e7576112e76119ba565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b031602179055508160000154600260006004856000015481548110611335576113356119ba565b60009182526020808320909101546001600160a01b03168352820192909252604001902055600480548061136b5761136b6119d0565b600082815260208082208301600019908101805473ffffffffffffffffffffffffffffffffffffffff191690559092019092556001600160a01b03861682526002905260408120908155600101805460ff1916905550505050565b600681815481106104bc57600080fd5b60038181548110610c4157600080fd5b6008546001600160a01b031633146114b05760085460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa158015611442573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611466919061197c565b9050336001600160a01b038216146114ae57600854604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610591565b505b600a55565b600654604080517fff0000000000000000000000000000000000000000000000000000000000000060f88a901b166020808301919091527fffffffffffffffffffffffffffffffffffffffff00000000000000000000000060608a901b1660218301527fffffffffffffffff00000000000000000000000000000000000000000000000060c089811b8216603585015288901b16603d830152604582018490526065820186905260858083018690528351808403909101815260a5909201909252805191012060009190600082156115b2576006611594600185611999565b815481106115a4576115a46119ba565b906000526020600020015490505b6040805160208082018490528183018590528251808303840181526060830180855281519190920120600680546001810182556000919091527ff652222313e28459528d920b65115c16c04f3efc82aaedc97be59f3f377c0d3f015533905260ff8c1660808201526001600160a01b038b1660a082015260c0810187905260e0810188905267ffffffffffffffff89166101008201529051829185917f5e3c1311ea442664e8b1611bfabef659120ea7a0a2cfc0667700bebc69cbffe1918190036101200190a3509098975050505050505050565b3360009081526001602081905260408220015460ff166116d5576040517fb6c60ea3000000000000000000000000000000000000000000000000000000008152336004820152602401610591565b60006116e58686434248896114b5565b9695505050505050565b60006060846001600160a01b0316848460405161170c9190611a1c565b60006040518083038185875af1925050503d8060008114611749576040519150601f19603f3d011682016040523d82523d6000602084013e61174e565b606091505b5090969095509350505050565b50565b60006020828403121561177057600080fd5b5035919050565b6001600160a01b038116811461175b57600080fd5b60006020828403121561179e57600080fd5b81356117a981611777565b9392505050565b600080604083850312156117c357600080fd5b82356117ce81611777565b9150602083013580151581146117e357600080fd5b809150509250929050565b6000806040838503121561180157600080fd5b823561180c81611777565b946020939093013593505050565b6000806000806080858703121561183057600080fd5b5050823594602084013594506040840135936060013592509050565b60008060006060848603121561186157600080fd5b833560ff8116811461187257600080fd5b9250602084013561188281611777565b929592945050506040919091013590565b600080600080606085870312156118a957600080fd5b84356118b481611777565b935060208501359250604085013567ffffffffffffffff808211156118d857600080fd5b818701915087601f8301126118ec57600080fd5b8135818111156118fb57600080fd5b88602082850101111561190d57600080fd5b95989497505060200194505050565b60005b8381101561193757818101518382015260200161191f565b50506000910152565b8215158152604060208201526000825180604084015261196781606085016020870161191c565b601f01601f1916919091016060019392505050565b60006020828403121561198e57600080fd5b81516117a981611777565b8181038181111561095a57634e487b7160e01b600052601160045260246000fd5b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052603160045260246000fd5b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f1916010192915050565b60008251611a2e81846020870161191c565b919091019291505056fea2646970667358221220bae7eaf18c61f7d8559a1edc92b1ee8dea4400c55ec0b1b0c4df659b4984b6f764736f6c63430008110033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}