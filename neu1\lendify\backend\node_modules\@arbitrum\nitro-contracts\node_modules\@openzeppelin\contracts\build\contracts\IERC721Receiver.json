{"_format": "hh-sol-artifact-1", "contractName": "IERC721Receiver", "sourceName": "contracts/token/ERC721/IERC721Receiver.sol", "abi": [{"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}