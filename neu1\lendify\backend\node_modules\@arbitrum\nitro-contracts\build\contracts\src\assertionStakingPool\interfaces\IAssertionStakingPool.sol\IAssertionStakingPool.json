{"_format": "hh-sol-artifact-1", "contractName": "IAssertionStakingPool", "sourceName": "src/assertionStakingPool/interfaces/IAssertionStakingPool.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}], "name": "AmountExceedsBalance", "type": "error"}, {"inputs": [], "name": "EmptyAssertionId", "type": "error"}, {"inputs": [], "name": "ZeroAmount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeWithdrawn", "type": "event"}, {"inputs": [], "name": "assertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertionInputs", "type": "tuple"}], "name": "createAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "depositBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "depositIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "makeStakeWithdrawable", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "makeStakeWithdrawableAndWithdrawBackIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawStakeBackIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}