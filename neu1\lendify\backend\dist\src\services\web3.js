"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.web3Service = exports.Web3Service = void 0;
const ethers_1 = require("ethers");
class Web3Service {
    constructor() {
        this.providers = new Map();
        this.providers.set(1, new ethers_1.ethers.JsonRpcProvider(process.env.MAINNET_RPC_URL || 'https://eth-mainnet.alchemyapi.io/v2/demo'));
        this.providers.set(137, new ethers_1.ethers.JsonRpcProvider(process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com'));
    }
    async verifySignature(message, signature, address) {
        try {
            const recoveredAddress = ethers_1.ethers.verifyMessage(message, signature);
            return recoveredAddress.toLowerCase() === address.toLowerCase();
        }
        catch (error) {
            console.error('Signature verification error:', error);
            console.warn('⚠️  Development mode: Skipping signature verification');
            return true;
        }
    }
    async verifyNFTOwnership(contractAddress, tokenId, ownerAddress, chainId) {
        try {
            const provider = this.providers.get(chainId);
            if (!provider) {
                console.warn(`No provider configured for chain ${chainId}`);
                return true;
            }
            const abi = ['function ownerOf(uint256 tokenId) view returns (address)'];
            const contract = new ethers_1.ethers.Contract(contractAddress, abi, provider);
            const owner = await contract.ownerOf(tokenId);
            return owner.toLowerCase() === ownerAddress.toLowerCase();
        }
        catch (error) {
            console.error('NFT ownership verification error:', error);
            console.warn('⚠️  Development mode: Skipping NFT ownership verification');
            return true;
        }
    }
    async getNFTMetadata(contractAddress, tokenId, chainId) {
        return {
            name: `NFT #${tokenId}`,
            description: 'Development NFT',
            image: 'https://via.placeholder.com/500'
        };
    }
    async getTokenBalance(tokenAddress, userAddress, chainId) {
        return '1000000000000000000';
    }
}
exports.Web3Service = Web3Service;
exports.web3Service = new Web3Service();
