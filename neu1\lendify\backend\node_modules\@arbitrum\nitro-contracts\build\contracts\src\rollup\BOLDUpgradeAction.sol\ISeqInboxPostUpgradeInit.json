{"_format": "hh-sol-artifact-1", "contractName": "ISeqInboxPostUpgradeInit", "sourceName": "src/rollup/BOLDUpgradeAction.sol", "abi": [{"inputs": [{"components": [{"internalType": "uint64", "name": "threshold", "type": "uint64"}, {"internalType": "uint64", "name": "max", "type": "uint64"}, {"internalType": "uint64", "name": "replenishRateInBasis", "type": "uint64"}], "internalType": "struct BufferConfig", "name": "bufferConfig_", "type": "tuple"}], "name": "postUpgradeInit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}