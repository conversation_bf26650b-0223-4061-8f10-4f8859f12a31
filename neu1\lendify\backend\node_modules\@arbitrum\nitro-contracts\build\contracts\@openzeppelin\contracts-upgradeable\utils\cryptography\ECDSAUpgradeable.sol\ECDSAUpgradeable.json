{"_format": "hh-sol-artifact-1", "contractName": "ECDSAUpgradeable", "sourceName": "@openzeppelin/contracts-upgradeable/utils/cryptography/ECDSAUpgradeable.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220f235d21dc545e5a2de150edca3c58722fd6f119a6fe3dab9dc1a33cdbd135b4c64736f6c63430008110033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220f235d21dc545e5a2de150edca3c58722fd6f119a6fe3dab9dc1a33cdbd135b4c64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}