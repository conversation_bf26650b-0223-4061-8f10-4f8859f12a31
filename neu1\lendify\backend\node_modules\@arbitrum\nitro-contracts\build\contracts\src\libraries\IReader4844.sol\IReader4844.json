{"_format": "hh-sol-artifact-1", "contractName": "IReader4844", "sourceName": "src/libraries/IReader4844.sol", "abi": [{"inputs": [], "name": "getBlobBaseFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDataHashes", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}