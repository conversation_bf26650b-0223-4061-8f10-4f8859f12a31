"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const events_1 = require("events");
class AnalyticsService extends events_1.EventEmitter {
    constructor() {
        super();
        this.metricsCache = new Map();
        this.performanceHistory = [];
        this.realtimeMetrics = new Map();
        this.isCollecting = false;
        this.initializeRealtimeMetrics();
    }
    initializeRealtimeMetrics() {
        this.realtimeMetrics.set('api_requests', 0);
        this.realtimeMetrics.set('failed_requests', 0);
        this.realtimeMetrics.set('successful_requests', 0);
        this.realtimeMetrics.set('active_users', 0);
        this.realtimeMetrics.set('total_volume_24h', 0);
        this.realtimeMetrics.set('new_users_today', 0);
        this.realtimeMetrics.set('rentals_today', 0);
        this.realtimeMetrics.set('listings_today', 0);
    }
    startMetricsCollection() {
        if (this.isCollecting)
            return;
        this.isCollecting = true;
        this.metricsInterval = setInterval(() => {
            this.collectPerformanceMetrics();
        }, 60000);
        console.log('📊 Analytics service started collecting metrics');
    }
    stopMetricsCollection() {
        if (!this.isCollecting)
            return;
        this.isCollecting = false;
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
        }
        console.log('📊 Analytics service stopped collecting metrics');
    }
    async collectPerformanceMetrics() {
        try {
            const memoryUsage = process.memoryUsage();
            const cpuUsage = process.cpuUsage();
            const performanceMetric = {
                timestamp: new Date(),
                apiResponseTime: this.getAverageResponseTime(),
                blockchainResponseTime: this.getBlockchainResponseTime(),
                databaseResponseTime: this.getDatabaseResponseTime(),
                totalRequests: this.realtimeMetrics.get('api_requests') || 0,
                failedRequests: this.realtimeMetrics.get('failed_requests') || 0,
                successRate: this.calculateSuccessRate(),
                memoryUsage,
                cpuUsage: this.calculateCPUPercent(cpuUsage),
                activeConnections: this.getActiveConnections(),
                cacheHitRate: this.calculateCacheHitRate()
            };
            this.performanceHistory.push(performanceMetric);
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            this.performanceHistory = this.performanceHistory.filter(metric => metric.timestamp > oneDayAgo);
            this.emit('performanceMetrics', performanceMetric);
            if (performanceMetric.successRate < 0.95 || performanceMetric.apiResponseTime > 2000) {
                this.emit('performanceAlert', {
                    type: 'performance_degradation',
                    metrics: performanceMetric,
                    timestamp: new Date()
                });
            }
        }
        catch (error) {
            console.error('Error collecting performance metrics:', error);
        }
    }
    async getUserMetrics(userId) {
        const cacheKey = `user_metrics_${userId}`;
        const cached = this.metricsCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 300000) {
            return cached.data;
        }
        try {
            const userMetrics = {
                userId,
                totalRentals: 0,
                totalListings: 0,
                totalEarnings: 0,
                totalSpent: 0,
                avgRating: 0,
                successfulTransactions: 0,
                disputes: 0,
                activeRentals: 0,
                activeLendings: 0,
                joinDate: new Date(),
                lastActivity: new Date(),
                preferredCategories: [],
                reputationScore: 0
            };
            this.metricsCache.set(cacheKey, {
                data: userMetrics,
                timestamp: Date.now()
            });
            return userMetrics;
        }
        catch (error) {
            console.error(`Error getting user metrics for ${userId}:`, error);
            throw error;
        }
    }
    async getUserAnalytics(userId, timeframe = '30d') {
        try {
            const endDate = new Date();
            const startDate = new Date();
            switch (timeframe) {
                case '7d':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case '30d':
                    startDate.setDate(endDate.getDate() - 30);
                    break;
                case '90d':
                    startDate.setDate(endDate.getDate() - 90);
                    break;
            }
            return {
                userId,
                timeframe,
                startDate,
                endDate,
                rentalActivity: await this.getUserRentalActivity(userId, startDate, endDate),
                listingActivity: await this.getUserListingActivity(userId, startDate, endDate),
                earnings: await this.getUserEarnings(userId, startDate, endDate),
                spending: await this.getUserSpending(userId, startDate, endDate),
                ratings: await this.getUserRatings(userId, startDate, endDate),
                categoryPreferences: await this.getUserCategoryPreferences(userId),
                performanceScores: await this.getUserPerformanceScores(userId)
            };
        }
        catch (error) {
            console.error(`Error getting user analytics for ${userId}:`, error);
            throw error;
        }
    }
    async getNFTMetrics(contractAddress, tokenId) {
        const cacheKey = `nft_metrics_${contractAddress}_${tokenId}`;
        const cached = this.metricsCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 600000) {
            return cached.data;
        }
        try {
            const nftMetrics = {
                contractAddress,
                tokenId,
                totalRentals: 0,
                totalRevenue: 0,
                avgRentalDuration: 0,
                avgDailyPrice: 0,
                avgRating: 0,
                demandScore: 0,
                utilizationRate: 0,
                category: 'unknown',
                owner: ''
            };
            this.metricsCache.set(cacheKey, {
                data: nftMetrics,
                timestamp: Date.now()
            });
            return nftMetrics;
        }
        catch (error) {
            console.error(`Error getting NFT metrics for ${contractAddress}:${tokenId}:`, error);
            throw error;
        }
    }
    async getNFTAnalytics(contractAddress, tokenId, timeframe = '30d') {
        try {
            return {
                contractAddress,
                tokenId,
                timeframe,
                rentalHistory: await this.getNFTRentalHistory(contractAddress, tokenId, timeframe),
                priceHistory: await this.getNFTPriceHistory(contractAddress, tokenId, timeframe),
                ratingHistory: await this.getNFTRatingHistory(contractAddress, tokenId, timeframe),
                demandAnalysis: await this.getNFTDemandAnalysis(contractAddress, tokenId),
                competitorAnalysis: await this.getNFTCompetitorAnalysis(contractAddress, tokenId),
                recommendations: await this.getNFTRecommendations(contractAddress, tokenId)
            };
        }
        catch (error) {
            console.error(`Error getting NFT analytics for ${contractAddress}:${tokenId}:`, error);
            throw error;
        }
    }
    async getPlatformMetrics() {
        const cacheKey = 'platform_metrics';
        const cached = this.metricsCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 300000) {
            return cached.data;
        }
        try {
            const platformMetrics = {
                totalUsers: 0,
                activeUsers: 0,
                totalNFTs: 0,
                totalRentals: 0,
                totalVolume: 0,
                platformFees: 0,
                avgTransactionValue: 0,
                growthRate: 0,
                retentionRate: 0,
                churnRate: 0,
                topCategories: [],
                topCollections: []
            };
            this.metricsCache.set(cacheKey, {
                data: platformMetrics,
                timestamp: Date.now()
            });
            return platformMetrics;
        }
        catch (error) {
            console.error('Error getting platform metrics:', error);
            throw error;
        }
    }
    async getPlatformAnalytics(timeframe = '30d') {
        try {
            return {
                timeframe,
                timestamp: new Date(),
                userGrowth: await this.getUserGrowthAnalytics(timeframe),
                volumeAnalytics: await this.getVolumeAnalytics(timeframe),
                categoryAnalytics: await this.getCategoryAnalytics(timeframe),
                collectionAnalytics: await this.getCollectionAnalytics(timeframe),
                geographicAnalytics: await this.getGeographicAnalytics(timeframe),
                deviceAnalytics: await this.getDeviceAnalytics(timeframe),
                conversionAnalytics: await this.getConversionAnalytics(timeframe)
            };
        }
        catch (error) {
            console.error('Error getting platform analytics:', error);
            throw error;
        }
    }
    async getMarketMetrics(chainId) {
        try {
            const supportedChains = chainId ? [chainId] : [1, 137, 42161, 10, 8453];
            const marketMetrics = [];
            for (const chain of supportedChains) {
                const metrics = await this.getChainMetrics(chain);
                marketMetrics.push(metrics);
            }
            return marketMetrics;
        }
        catch (error) {
            console.error('Error getting market metrics:', error);
            throw error;
        }
    }
    async getChainMetrics(chainId) {
        const chainNames = {
            1: 'Ethereum',
            137: 'Polygon',
            42161: 'Arbitrum',
            10: 'Optimism',
            8453: 'Base'
        };
        return {
            chainId,
            chainName: chainNames[chainId] || 'Unknown',
            totalVolume24h: 0,
            totalVolume7d: 0,
            totalVolume30d: 0,
            transactionCount24h: 0,
            transactionCount7d: 0,
            transactionCount30d: 0,
            avgGasPrice: 0,
            avgTransactionValue: 0,
            topNFTs: [],
            activeUsers24h: 0,
            newUsers24h: 0
        };
    }
    async getRevenueMetrics(period, startDate, endDate) {
        const end = endDate || new Date();
        const start = startDate || this.getStartDateForPeriod(period, end);
        try {
            return {
                period,
                startDate: start,
                endDate: end,
                totalRevenue: 0,
                platformFees: 0,
                rentalRevenue: 0,
                lendingRevenue: 0,
                flashLoanRevenue: 0,
                oracleRevenue: 0,
                transactionCount: 0,
                avgRevenuePerUser: 0,
                avgRevenuePerTransaction: 0
            };
        }
        catch (error) {
            console.error('Error getting revenue metrics:', error);
            throw error;
        }
    }
    incrementMetric(metricName, value = 1) {
        const currentValue = this.realtimeMetrics.get(metricName) || 0;
        this.realtimeMetrics.set(metricName, currentValue + value);
    }
    setMetric(metricName, value) {
        this.realtimeMetrics.set(metricName, value);
    }
    getRealtimeMetrics() {
        return Object.fromEntries(this.realtimeMetrics.entries());
    }
    getPerformanceHistory(hours = 24) {
        const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
        return this.performanceHistory.filter(metric => metric.timestamp > cutoff);
    }
    getCurrentPerformance() {
        return this.performanceHistory.length > 0
            ? this.performanceHistory[this.performanceHistory.length - 1]
            : null;
    }
    trackEvent(eventName, userId, properties) {
        const event = {
            eventName,
            userId,
            properties,
            timestamp: new Date()
        };
        this.emit('event', event);
        this.incrementMetric(`event_${eventName}`);
    }
    trackUserAction(action, userId, metadata) {
        this.trackEvent('user_action', userId, { action, ...metadata });
    }
    trackTransactionEvent(type, txHash, value, userId) {
        this.trackEvent('transaction', userId, { type, txHash, value });
        this.incrementMetric(`${type}_transactions`);
        this.incrementMetric('total_volume_24h', value);
    }
    getStartDateForPeriod(period, endDate) {
        const start = new Date(endDate);
        switch (period) {
            case 'daily':
                start.setHours(0, 0, 0, 0);
                break;
            case 'weekly':
                start.setDate(start.getDate() - 7);
                break;
            case 'monthly':
                start.setMonth(start.getMonth() - 1);
                break;
            case 'yearly':
                start.setFullYear(start.getFullYear() - 1);
                break;
        }
        return start;
    }
    getAverageResponseTime() {
        return 250;
    }
    getBlockchainResponseTime() {
        return 800;
    }
    getDatabaseResponseTime() {
        return 150;
    }
    calculateSuccessRate() {
        const total = this.realtimeMetrics.get('api_requests') || 0;
        const failed = this.realtimeMetrics.get('failed_requests') || 0;
        return total > 0 ? (total - failed) / total : 1;
    }
    calculateCPUPercent(cpuUsage) {
        return (cpuUsage.user + cpuUsage.system) / 1000000;
    }
    getActiveConnections() {
        return 0;
    }
    calculateCacheHitRate() {
        return 0.85;
    }
    async getUserRentalActivity(userId, startDate, endDate) {
        return { totalRentals: 0, avgDuration: 0, categories: [] };
    }
    async getUserListingActivity(userId, startDate, endDate) {
        return { totalListings: 0, activeListings: 0, avgPrice: 0 };
    }
    async getUserEarnings(userId, startDate, endDate) {
        return { totalEarnings: 0, rentalEarnings: 0, lendingEarnings: 0 };
    }
    async getUserSpending(userId, startDate, endDate) {
        return { totalSpent: 0, rentalSpent: 0, borrowingCosts: 0 };
    }
    async getUserRatings(userId, startDate, endDate) {
        return { avgRating: 0, totalRatings: 0, ratingDistribution: {} };
    }
    async getUserCategoryPreferences(userId) {
        return { preferredCategories: [], categorySpending: {} };
    }
    async getUserPerformanceScores(userId) {
        return { reputationScore: 0, trustScore: 0, activityScore: 0 };
    }
    async getNFTRentalHistory(contractAddress, tokenId, timeframe) {
        return { totalRentals: 0, rentalDates: [], prices: [] };
    }
    async getNFTPriceHistory(contractAddress, tokenId, timeframe) {
        return { priceChanges: [], avgPrice: 0, priceVolatility: 0 };
    }
    async getNFTRatingHistory(contractAddress, tokenId, timeframe) {
        return { avgRating: 0, ratingTrend: 'stable', totalRatings: 0 };
    }
    async getNFTDemandAnalysis(contractAddress, tokenId) {
        return { demandScore: 0, demandTrend: 'stable', demandFactors: [] };
    }
    async getNFTCompetitorAnalysis(contractAddress, tokenId) {
        return { competitors: [], marketPosition: 'unknown' };
    }
    async getNFTRecommendations(contractAddress, tokenId) {
        return { priceOptimization: [], marketingTips: [], timingAdvice: [] };
    }
    async getUserGrowthAnalytics(timeframe) {
        return { newUsers: 0, growthRate: 0, retentionRate: 0 };
    }
    async getVolumeAnalytics(timeframe) {
        return { totalVolume: 0, volumeGrowth: 0, avgTransactionSize: 0 };
    }
    async getCategoryAnalytics(timeframe) {
        return { topCategories: [], categoryGrowth: {} };
    }
    async getCollectionAnalytics(timeframe) {
        return { topCollections: [], collectionGrowth: {} };
    }
    async getGeographicAnalytics(timeframe) {
        return { topCountries: [], regionGrowth: {} };
    }
    async getDeviceAnalytics(timeframe) {
        return { deviceTypes: [], browserTypes: [] };
    }
    async getConversionAnalytics(timeframe) {
        return { conversionRate: 0, funnelAnalysis: {} };
    }
    clearCache() {
        this.metricsCache.clear();
    }
    getCacheStats() {
        return {
            size: this.metricsCache.size,
            keys: Array.from(this.metricsCache.keys())
        };
    }
}
exports.AnalyticsService = AnalyticsService;
