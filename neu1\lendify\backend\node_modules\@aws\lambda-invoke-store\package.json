{"name": "@aws/lambda-invoke-store", "version": "0.0.1", "description": "Invoke scoped data storage for AWS Lambda Node.js Runtime Environment", "homepage": "https://github.com/awslabs/aws-lambda-invoke-store", "main": "./dist/invoke-store.js", "types": "./dist/invoke-store.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/awslabs/aws-lambda-invoke-store.git"}, "license": "Apache-2.0", "author": {"name": "Amazon Web Services", "url": "http://aws.amazon.com"}, "scripts": {"build": "tsc", "test": "vitest run", "test:watch": "vitest watch", "clean": "rm -rf dist"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.19.112", "typescript": "~5.4.5", "vitest": "^3.1.1"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@4.9.4"}