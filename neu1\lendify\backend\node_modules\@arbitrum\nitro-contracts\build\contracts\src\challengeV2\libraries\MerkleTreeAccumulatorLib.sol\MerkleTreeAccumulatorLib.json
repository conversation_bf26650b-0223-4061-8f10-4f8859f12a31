{"_format": "hh-sol-artifact-1", "contractName": "MerkleTreeAccumulatorLib", "sourceName": "src/challengeV2/libraries/MerkleTreeAccumulatorLib.sol", "abi": [{"inputs": [], "name": "MAX_LEVEL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6087610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063a49062d4146038575b600080fd5b603f604081565b60405190815260200160405180910390f3fea264697066735822122075be2c1ed8124e35d75ecbf65fc8abc16b35caf1c460e010a3b87224c5f6f8ad64736f6c63430008110033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063a49062d4146038575b600080fd5b603f604081565b60405190815260200160405180910390f3fea264697066735822122075be2c1ed8124e35d75ecbf65fc8abc16b35caf1c460e010a3b87224c5f6f8ad64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}