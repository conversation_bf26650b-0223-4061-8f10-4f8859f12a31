"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Loan = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const LoanSchema = new mongoose_1.Schema({
    borrower: {
        type: String,
        required: true,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    lender: {
        type: String,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    nft: {
        contractAddress: {
            type: String,
            required: true,
            lowercase: true,
            match: /^0x[a-fA-F0-9]{40}$/
        },
        tokenId: {
            type: String,
            required: true
        },
        chainId: {
            type: Number,
            required: true,
            enum: [1, 137, 42161, 10, 8453]
        },
        nftId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'NFT' },
        estimatedValue: { type: Number, required: true, min: 0 },
        oraclePrice: { type: Number, min: 0 }
    },
    loan: {
        principal: { type: Number, required: true, min: 0 },
        currency: { type: String, required: true, default: 'ETH' },
        interestRate: { type: Number, required: true, min: 0, max: 100 },
        duration: { type: Number, required: true, min: 1, max: 365 },
        ltv: { type: Number, required: true, min: 0, max: 100 },
        startDate: Date,
        dueDate: Date,
        actualDueDate: Date
    },
    repayment: {
        totalAmount: { type: Number, required: true, min: 0 },
        amountPaid: { type: Number, default: 0, min: 0 },
        remainingAmount: { type: Number, required: true, min: 0 },
        payments: [{
                amount: { type: Number, required: true, min: 0 },
                timestamp: { type: Date, required: true },
                txHash: { type: String, required: true },
                type: { type: String, enum: ['partial', 'full', 'interest_only'], required: true }
            }],
        gracePeriod: { type: Number, default: 7, min: 0 },
        lateFee: { type: Number, default: 0, min: 0 }
    },
    collateral: {
        isEscrowed: { type: Boolean, default: false },
        escrowContract: String,
        escrowTxHash: String,
        releaseCondition: { type: String, enum: ['repayment', 'liquidation'], default: 'repayment' },
        liquidationThreshold: { type: Number, required: true, min: 0, max: 100 },
        currentValue: { type: Number, min: 0 },
        lastValuation: Date
    },
    blockchain: {
        loanContract: {
            type: String,
            required: true,
            lowercase: true,
            match: /^0x[a-fA-F0-9]{40}$/
        },
        loanId: Number,
        requestTxHash: String,
        fundingTxHash: String,
        repaymentTxHash: String,
        liquidationTxHash: String,
        blockNumber: Number
    },
    status: {
        type: String,
        enum: ['requested', 'funded', 'active', 'repaid', 'defaulted', 'liquidated', 'cancelled'],
        default: 'requested'
    },
    timeline: [{
            status: { type: String, required: true },
            timestamp: { type: Date, required: true },
            txHash: String,
            amount: Number,
            notes: String
        }],
    terms: {
        autoLiquidation: { type: Boolean, default: true },
        partialRepayment: { type: Boolean, default: true },
        earlyRepayment: { type: Boolean, default: true },
        extensionAllowed: { type: Boolean, default: false },
        maxExtensions: { type: Number, default: 0, min: 0 },
        extensionFee: { type: Number, default: 0, min: 0 },
        penaltyRate: { type: Number, default: 0, min: 0 }
    },
    extensions: [{
            originalDueDate: { type: Date, required: true },
            newDueDate: { type: Date, required: true },
            additionalDays: { type: Number, required: true },
            extensionFee: { type: Number, required: true },
            additionalInterest: { type: Number, required: true },
            timestamp: { type: Date, required: true },
            txHash: String,
            reason: String
        }],
    risk: {
        creditScore: { type: Number, min: 0, max: 1000 },
        riskLevel: { type: String, enum: ['low', 'medium', 'high'], required: true },
        riskFactors: [String],
        volatilityScore: { type: Number, required: true, min: 0, max: 100 },
        liquidityScore: { type: Number, required: true, min: 0, max: 100 }
    },
    dispute: {
        isDisputed: { type: Boolean, default: false },
        reason: String,
        initiator: { type: String, enum: ['borrower', 'lender'] },
        description: String,
        evidence: [{
                type: { type: String, enum: ['text', 'image', 'document'], required: true },
                content: { type: String, required: true },
                uploadedBy: { type: String, enum: ['borrower', 'lender'], required: true },
                timestamp: { type: Date, required: true }
            }],
        resolution: {
            decision: {
                type: String,
                enum: ['favor_borrower', 'favor_lender', 'partial_relief', 'no_fault']
            },
            reasoning: String,
            adjustedAmount: Number,
            newTerms: mongoose_1.Schema.Types.Mixed,
            resolvedBy: String,
            resolvedAt: Date
        }
    },
    analytics: {
        inquiries: { type: Number, default: 0, min: 0 },
        views: { type: Number, default: 0, min: 0 },
        competingOffers: { type: Number, default: 0, min: 0 }
    },
    metadata: {
        purpose: String,
        notes: String,
        visibility: { type: String, enum: ['public', 'private'], default: 'public' }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
LoanSchema.index({ borrower: 1 });
LoanSchema.index({ lender: 1 });
LoanSchema.index({ status: 1 });
LoanSchema.index({ 'nft.contractAddress': 1, 'nft.tokenId': 1, 'nft.chainId': 1 });
LoanSchema.index({ 'loan.dueDate': 1 });
LoanSchema.index({ 'loan.actualDueDate': 1 });
LoanSchema.index({ 'loan.principal': 1 });
LoanSchema.index({ 'loan.interestRate': 1 });
LoanSchema.index({ 'risk.riskLevel': 1 });
LoanSchema.index({ createdAt: -1 });
LoanSchema.index({ status: 1, 'loan.dueDate': 1 });
LoanSchema.index({ borrower: 1, status: 1 });
LoanSchema.index({ lender: 1, status: 1 });
LoanSchema.virtual('isActive').get(function () {
    return ['funded', 'active'].includes(this.status);
});
LoanSchema.virtual('isOverdue').get(function () {
    const dueDate = this.loan.actualDueDate || this.loan.dueDate;
    return dueDate && new Date() > dueDate && this.status === 'active';
});
LoanSchema.virtual('daysRemaining').get(function () {
    if (!this.isActive)
        return 0;
    const dueDate = this.loan.actualDueDate || this.loan.dueDate;
    if (!dueDate)
        return 0;
    const now = new Date();
    const diff = dueDate.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
});
LoanSchema.virtual('currentLTV').get(function () {
    const currentValue = this.collateral.currentValue || this.nft.estimatedValue;
    return (this.repayment.remainingAmount / currentValue) * 100;
});
LoanSchema.virtual('totalInterest').get(function () {
    return this.repayment.totalAmount - this.loan.principal;
});
LoanSchema.methods.updateStatus = function (newStatus, txHash, amount, notes) {
    this.status = newStatus;
    this.timeline.push({
        status: newStatus,
        timestamp: new Date(),
        txHash,
        amount,
        notes
    });
    switch (newStatus) {
        case 'funded':
        case 'active':
            if (!this.loan.startDate) {
                this.loan.startDate = new Date();
                this.loan.dueDate = new Date(Date.now() + (this.loan.duration * 24 * 60 * 60 * 1000));
                this.loan.actualDueDate = this.loan.dueDate;
            }
            break;
    }
    return this.save();
};
LoanSchema.methods.makePayment = function (amount, txHash, type = 'partial') {
    this.repayment.amountPaid += amount;
    this.repayment.remainingAmount = Math.max(0, this.repayment.totalAmount - this.repayment.amountPaid);
    this.repayment.payments.push({
        amount,
        timestamp: new Date(),
        txHash,
        type
    });
    if (this.repayment.remainingAmount === 0) {
        this.updateStatus('repaid', txHash, amount, 'Loan fully repaid');
    }
    else {
        this.timeline.push({
            status: 'payment_made',
            timestamp: new Date(),
            txHash,
            amount,
            notes: `${type} payment of ${amount} ${this.loan.currency}`
        });
    }
    return this.save();
};
LoanSchema.methods.extendLoan = function (additionalDays, extensionFee, additionalInterest, txHash, reason) {
    if (!this.terms.extensionAllowed) {
        throw new Error('Loan extensions not allowed');
    }
    if (this.extensions.length >= this.terms.maxExtensions) {
        throw new Error('Maximum extensions reached');
    }
    const originalDueDate = new Date(this.loan.actualDueDate || this.loan.dueDate);
    const newDueDate = new Date(originalDueDate.getTime() + (additionalDays * 24 * 60 * 60 * 1000));
    this.extensions.push({
        originalDueDate,
        newDueDate,
        additionalDays,
        extensionFee,
        additionalInterest,
        timestamp: new Date(),
        txHash,
        reason
    });
    this.loan.actualDueDate = newDueDate;
    this.repayment.totalAmount += extensionFee + additionalInterest;
    this.repayment.remainingAmount += extensionFee + additionalInterest;
    this.timeline.push({
        status: 'extended',
        timestamp: new Date(),
        txHash,
        notes: `Extended by ${additionalDays} days`
    });
    return this.save();
};
LoanSchema.methods.liquidate = function (txHash, liquidationValue) {
    this.status = 'liquidated';
    this.timeline.push({
        status: 'liquidated',
        timestamp: new Date(),
        txHash,
        amount: liquidationValue,
        notes: 'Collateral liquidated due to default or threshold breach'
    });
    if (txHash) {
        this.blockchain.liquidationTxHash = txHash;
    }
    return this.save();
};
LoanSchema.methods.updateCollateralValue = function (newValue, source = 'oracle') {
    const previousValue = this.collateral.currentValue || this.nft.estimatedValue;
    this.collateral.currentValue = newValue;
    this.collateral.lastValuation = new Date();
    const valueDropPercentage = ((previousValue - newValue) / previousValue) * 100;
    if (valueDropPercentage >= this.collateral.liquidationThreshold && this.isActive) {
        this.timeline.push({
            status: 'liquidation_threshold_reached',
            timestamp: new Date(),
            notes: `Collateral value dropped by ${valueDropPercentage.toFixed(2)}%, threshold: ${this.collateral.liquidationThreshold}%`
        });
        if (this.terms.autoLiquidation) {
            this.status = 'defaulted';
        }
    }
    return this.save();
};
LoanSchema.methods.calculateAccruedInterest = function (asOfDate) {
    const date = asOfDate || new Date();
    const startDate = this.loan.startDate;
    if (!startDate || date <= startDate)
        return 0;
    const daysElapsed = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const dailyRate = this.loan.interestRate / 365 / 100;
    return this.loan.principal * dailyRate * daysElapsed;
};
LoanSchema.statics.findByNFT = function (contractAddress, tokenId, chainId) {
    return this.find({
        'nft.contractAddress': contractAddress,
        'nft.tokenId': tokenId,
        'nft.chainId': chainId
    });
};
LoanSchema.statics.findActiveLoans = function () {
    return this.find({
        status: { $in: ['funded', 'active'] }
    });
};
LoanSchema.statics.findOverdueLoans = function () {
    return this.find({
        status: 'active',
        $or: [
            { 'loan.dueDate': { $lt: new Date() } },
            { 'loan.actualDueDate': { $lt: new Date() } }
        ]
    });
};
LoanSchema.statics.findByUser = function (userAddress, role) {
    const query = {};
    if (role === 'borrower') {
        query.borrower = userAddress;
    }
    else if (role === 'lender') {
        query.lender = userAddress;
    }
    else {
        query.$or = [{ borrower: userAddress }, { lender: userAddress }];
    }
    return this.find(query);
};
LoanSchema.statics.findLiquidationCandidates = function () {
    return this.find({
        status: 'active',
        'terms.autoLiquidation': true,
        $expr: {
            $gte: [
                {
                    $multiply: [
                        {
                            $divide: [
                                { $subtract: ['$nft.estimatedValue', '$collateral.currentValue'] },
                                '$nft.estimatedValue'
                            ]
                        },
                        100
                    ]
                },
                '$collateral.liquidationThreshold'
            ]
        }
    });
};
LoanSchema.pre('save', function (next) {
    if (this.isModified('loan') && this.loan.principal && this.loan.interestRate && this.loan.duration) {
        const dailyRate = this.loan.interestRate / 365 / 100;
        const totalInterest = this.loan.principal * dailyRate * this.loan.duration;
        this.repayment.totalAmount = this.loan.principal + totalInterest;
        this.repayment.remainingAmount = this.repayment.totalAmount - this.repayment.amountPaid;
    }
    if (this.isModified('loan') || this.isModified('nft')) {
        this.loan.ltv = (this.loan.principal / this.nft.estimatedValue) * 100;
    }
    next();
});
exports.Loan = mongoose_1.default.model('Loan', LoanSchema);
