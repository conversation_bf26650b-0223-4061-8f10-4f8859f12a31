export interface NotificationPayload {
    userId: string;
    type: string;
    title: string;
    message: string;
    data?: any;
    priority?: 'low' | 'normal' | 'high';
}
export declare class NotificationService {
    sendNotification(payload: NotificationPayload): Promise<void>;
    sendBulkNotifications(payloads: NotificationPayload[]): Promise<void>;
    markAsRead(notificationId: string, userId: string): Promise<void>;
    getUnreadCount(userId: string): Promise<number>;
}
export declare const notificationService: NotificationService;
//# sourceMappingURL=notifications.d.ts.map