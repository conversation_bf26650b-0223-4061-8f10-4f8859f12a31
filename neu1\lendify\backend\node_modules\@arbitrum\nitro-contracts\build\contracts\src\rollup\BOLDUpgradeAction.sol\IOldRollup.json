{"_format": "hh-sol-artifact-1", "contractName": "IOldRollup", "sourceName": "src/rollup/BOLDUpgradeAction.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint64", "name": "nodeNum", "type": "uint64"}, {"indexed": true, "internalType": "bytes32", "name": "parentNodeHash", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "nodeHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "executionHash", "type": "bytes32"}, {"components": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "afterState", "type": "tuple"}, {"internalType": "uint64", "name": "numBlocks", "type": "uint64"}], "indexed": false, "internalType": "struct IOldRollup.Assertion", "name": "assertion", "type": "tuple"}, {"indexed": false, "internalType": "bytes32", "name": "afterInboxBatchAcc", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}], "name": "NodeCreated", "type": "event"}, {"inputs": [], "name": "anyTrustFastConfirmer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "nodeNum", "type": "uint64"}], "name": "getNode", "outputs": [{"components": [{"internalType": "bytes32", "name": "stateHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "challengeHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "confirmData", "type": "bytes32"}, {"internalType": "uint64", "name": "prevNum", "type": "uint64"}, {"internalType": "uint64", "name": "deadlineBlock", "type": "uint64"}, {"internalType": "uint64", "name": "noChildConfirmedBeforeBlock", "type": "uint64"}, {"internalType": "uint64", "name": "stakerCount", "type": "uint64"}, {"internalType": "uint64", "name": "child<PERSON>takerCount", "type": "uint64"}, {"internalType": "uint64", "name": "first<PERSON><PERSON><PERSON><PERSON>lock", "type": "uint64"}, {"internalType": "uint64", "name": "latestChildNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createdAtBlock", "type": "uint64"}, {"internalType": "bytes32", "name": "nodeHash", "type": "bytes32"}], "internalType": "struct Node", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "get<PERSON><PERSON>", "outputs": [{"components": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "uint64", "name": "latestStakedNode", "type": "uint64"}, {"internalType": "uint64", "name": "currentChallenge", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}], "internalType": "struct <PERSON><PERSON><PERSON>", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "stakerNum", "type": "uint64"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "validator", "type": "address"}], "name": "isValidator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "isZombie", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfirmed", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakerCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorWalletCreator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "wasmModuleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawStakerFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}