import { EventEmitter } from 'events';
export interface UserMetrics {
    userId: string;
    totalRentals: number;
    totalListings: number;
    totalEarnings: number;
    totalSpent: number;
    avgRating: number;
    successfulTransactions: number;
    disputes: number;
    activeRentals: number;
    activeLendings: number;
    joinDate: Date;
    lastActivity: Date;
    preferredCategories: string[];
    reputationScore: number;
}
export interface NFTMetrics {
    contractAddress: string;
    tokenId: string;
    totalRentals: number;
    totalRevenue: number;
    avgRentalDuration: number;
    avgDailyPrice: number;
    avgRating: number;
    demandScore: number;
    utilizationRate: number;
    lastRentalDate?: Date;
    category: string;
    owner: string;
}
export interface PlatformMetrics {
    totalUsers: number;
    activeUsers: number;
    totalNFTs: number;
    totalRentals: number;
    totalVolume: number;
    platformFees: number;
    avgTransactionValue: number;
    growthRate: number;
    retentionRate: number;
    churnRate: number;
    topCategories: Array<{
        category: string;
        count: number;
        volume: number;
    }>;
    topCollections: Array<{
        collection: string;
        count: number;
        volume: number;
    }>;
}
export interface MarketMetrics {
    chainId: number;
    chainName: string;
    totalVolume24h: number;
    totalVolume7d: number;
    totalVolume30d: number;
    transactionCount24h: number;
    transactionCount7d: number;
    transactionCount30d: number;
    avgGasPrice: number;
    avgTransactionValue: number;
    topNFTs: Array<{
        contract: string;
        tokenId: string;
        volume: number;
    }>;
    activeUsers24h: number;
    newUsers24h: number;
}
export interface PerformanceMetrics {
    timestamp: Date;
    apiResponseTime: number;
    blockchainResponseTime: number;
    databaseResponseTime: number;
    totalRequests: number;
    failedRequests: number;
    successRate: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    activeConnections: number;
    cacheHitRate: number;
}
export interface RevenueMetrics {
    period: 'daily' | 'weekly' | 'monthly' | 'yearly';
    startDate: Date;
    endDate: Date;
    totalRevenue: number;
    platformFees: number;
    rentalRevenue: number;
    lendingRevenue: number;
    flashLoanRevenue: number;
    oracleRevenue: number;
    transactionCount: number;
    avgRevenuePerUser: number;
    avgRevenuePerTransaction: number;
}
export declare class AnalyticsService extends EventEmitter {
    private metricsCache;
    private performanceHistory;
    private realtimeMetrics;
    private isCollecting;
    private metricsInterval?;
    constructor();
    private initializeRealtimeMetrics;
    startMetricsCollection(): void;
    stopMetricsCollection(): void;
    private collectPerformanceMetrics;
    getUserMetrics(userId: string): Promise<UserMetrics>;
    getUserAnalytics(userId: string, timeframe?: '7d' | '30d' | '90d'): Promise<any>;
    getNFTMetrics(contractAddress: string, tokenId: string): Promise<NFTMetrics>;
    getNFTAnalytics(contractAddress: string, tokenId: string, timeframe?: '7d' | '30d' | '90d'): Promise<any>;
    getPlatformMetrics(): Promise<PlatformMetrics>;
    getPlatformAnalytics(timeframe?: '1d' | '7d' | '30d' | '90d'): Promise<any>;
    getMarketMetrics(chainId?: number): Promise<MarketMetrics[]>;
    private getChainMetrics;
    getRevenueMetrics(period: 'daily' | 'weekly' | 'monthly' | 'yearly', startDate?: Date, endDate?: Date): Promise<RevenueMetrics>;
    incrementMetric(metricName: string, value?: number): void;
    setMetric(metricName: string, value: number): void;
    getRealtimeMetrics(): {
        [key: string]: number;
    };
    getPerformanceHistory(hours?: number): PerformanceMetrics[];
    getCurrentPerformance(): PerformanceMetrics | null;
    trackEvent(eventName: string, userId?: string, properties?: any): void;
    trackUserAction(action: string, userId: string, metadata?: any): void;
    trackTransactionEvent(type: 'rental' | 'lending' | 'flashloan', txHash: string, value: number, userId?: string): void;
    private getStartDateForPeriod;
    private getAverageResponseTime;
    private getBlockchainResponseTime;
    private getDatabaseResponseTime;
    private calculateSuccessRate;
    private calculateCPUPercent;
    private getActiveConnections;
    private calculateCacheHitRate;
    private getUserRentalActivity;
    private getUserListingActivity;
    private getUserEarnings;
    private getUserSpending;
    private getUserRatings;
    private getUserCategoryPreferences;
    private getUserPerformanceScores;
    private getNFTRentalHistory;
    private getNFTPriceHistory;
    private getNFTRatingHistory;
    private getNFTDemandAnalysis;
    private getNFTCompetitorAnalysis;
    private getNFTRecommendations;
    private getUserGrowthAnalytics;
    private getVolumeAnalytics;
    private getCategoryAnalytics;
    private getCollectionAnalytics;
    private getGeographicAnalytics;
    private getDeviceAnalytics;
    private getConversionAnalytics;
    clearCache(): void;
    getCacheStats(): any;
}
//# sourceMappingURL=AnalyticsService.d.ts.map