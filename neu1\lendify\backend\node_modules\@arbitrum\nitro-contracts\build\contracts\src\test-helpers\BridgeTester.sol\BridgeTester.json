{"_format": "hh-sol-artifact-1", "contractName": "BridgeTester", "sourceName": "src/test-helpers/BridgeTester.sol", "abi": [{"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "NotContract", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotDelayedInbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotOutbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "NotRollupOrOwner", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "BridgeCallTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "InboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageIndex", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "beforeInboxAcc", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "kind", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "baseFeeL1", "type": "uint256"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "MessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "OutboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rollup", "type": "address"}], "name": "RollupUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxUpdated", "type": "event"}, {"inputs": [], "name": "acceptFundsFromOldBridge", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "activeOutbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedDelayedInboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}], "name": "allowedDelayedInboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedOutboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "allowedOutboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "delayedInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "delayedMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "enqueueDelayedMessage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "afterDelayedMessagesRead", "type": "uint256"}, {"internalType": "uint256", "name": "prevMessageCount", "type": "uint256"}, {"internalType": "uint256", "name": "newMessageCount", "type": "uint256"}], "name": "enqueueSequencerMessage", "outputs": [{"internalType": "uint256", "name": "seqMessageIndex", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "delayedAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "acc", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "rollup_", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "nativeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nativeTokenDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sequencerInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerReportedSubMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "batchPoster", "type": "address"}, {"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}], "name": "submitBatchSpendingReport", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "_rollup", "type": "address"}], "name": "updateRollupAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x60a06040523060805234801561001457600080fd5b5060805161159561002d600039600050506115956000f3fe60806040526004361061018e5760003560e01c8063ab5d8943116100d6578063d5719dc21161007f578063e77145f411610059578063e77145f41461024a578063eca067ad14610508578063ee35f3271461051d57600080fd5b8063d5719dc2146104a8578063e1758bd8146104c8578063e76f5c8d146104e857600080fd5b8063c4d66de8116100b0578063c4d66de814610448578063cb23bcb514610468578063cee3d7281461048857600080fd5b8063ab5d8943146103b2578063ad48cb5e146103c7578063ae60bd131461040b57600080fd5b80637a88b10711610138578063919cc70611610112578063919cc70614610302578063945e11471461034c5780639e5d4c491461038457600080fd5b80637a88b1071461028257806386598a56146102a55780638db5993b146102ef57600080fd5b806347fb24c51161016957806347fb24c51461022a5780634f61f8501461024c5780635fca4a161461026c57600080fd5b806284120c1461019a57806316bf5579146101be578063413b35bd146101de57600080fd5b3661019557005b600080fd5b3480156101a657600080fd5b50600a545b6040519081526020015b60405180910390f35b3480156101ca57600080fd5b506101ab6101d9366004611296565b61053d565b3480156101ea57600080fd5b5061021a6101f93660046112c7565b6001600160a01b031660009081526002602052604090206001015460ff1690565b60405190151581526020016101b5565b34801561023657600080fd5b5061024a6102453660046112eb565b61055e565b005b34801561025857600080fd5b5061024a6102673660046112c7565b61087a565b34801561027857600080fd5b506101ab600b5481565b34801561028e57600080fd5b506101ab61029d366004611329565b600092915050565b3480156102b157600080fd5b506102cf6102c0366004611355565b50600093849350839250829150565b6040805194855260208501939093529183015260608201526080016101b5565b6101ab6102fd366004611387565b6109a5565b34801561030e57600080fd5b5061024a61031d3660046112c7565b6006805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b0392909216919091179055565b34801561035857600080fd5b5061036c610367366004611296565b610a09565b6040516001600160a01b0390911681526020016101b5565b34801561039057600080fd5b506103a461039f3660046113ce565b610a33565b6040516101b5929190611457565b3480156103be57600080fd5b5061036c610be0565b3480156103d357600080fd5b506008546103f99074010000000000000000000000000000000000000000900460ff1681565b60405160ff90911681526020016101b5565b34801561041757600080fd5b5061021a6104263660046112c7565b6001600160a01b03166000908152600160208190526040909120015460ff1690565b34801561045457600080fd5b5061024a6104633660046112c7565b610c2b565b34801561047457600080fd5b5060065461036c906001600160a01b031681565b34801561049457600080fd5b5061024a6104a33660046112eb565b610d97565b3480156104b457600080fd5b506101ab6104c3366004611296565b6110ae565b3480156104d457600080fd5b5060085461036c906001600160a01b031681565b3480156104f457600080fd5b5061036c610503366004611296565b6110be565b34801561051457600080fd5b506009546101ab565b34801561052957600080fd5b5060075461036c906001600160a01b031681565b600a818154811061054d57600080fd5b600091825260209091200154905081565b6006546001600160a01b0316331461062d5760065460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa1580156105ba573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105de91906114af565b9050336001600160a01b0382161461062b57600654604051630739600760e01b81523360048201526001600160a01b03918216602482015290821660448201526064015b60405180910390fd5b505b6001600160a01b0382166000818152600160208181526040928390209182015492518515158152919360ff90931692917f6675ce8882cb71637de5903a193d218cc0544be9c0650cb83e0955f6aa2bf521910160405180910390a28080156106925750825b806106a45750801580156106a4575082155b156106af5750505050565b821561074a57604080518082018252600380548252600160208084018281526001600160a01b038a166000818152928490529582209451855551938201805460ff1916941515949094179093558154908101825591527fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b01805473ffffffffffffffffffffffffffffffffffffffff19169091179055610873565b6003805461075a906001906114cc565b8154811061076a5761076a6114ed565b6000918252602090912001548254600380546001600160a01b03909316929091908110610799576107996114ed565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555081600001546001600060038560000154815481106107e7576107e76114ed565b60009182526020808320909101546001600160a01b03168352820192909252604001902055600380548061081d5761081d611503565b600082815260208082208301600019908101805473ffffffffffffffffffffffffffffffffffffffff191690559092019092556001600160a01b03861682526001908190526040822091825501805460ff191690555b50505b5050565b6006546001600160a01b031633146109445760065460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa1580156108d6573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906108fa91906114af565b9050336001600160a01b0382161461094257600654604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610622565b505b6007805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b0383169081179091556040519081527f8c1e6003ed33ca6748d4ad3dd4ecc949065c89dceb31fdf546a5289202763c6a9060200160405180910390a150565b3360009081526001602081905260408220015460ff166109f3576040517fb6c60ea3000000000000000000000000000000000000000000000000000000008152336004820152602401610622565b610a018484434248876110ce565b949350505050565b60048181548110610a1957600080fd5b6000918252602090912001546001600160a01b0316905081565b3360009081526002602052604081206001015460609060ff16610a84576040517f32ea82ab000000000000000000000000000000000000000000000000000000008152336004820152602401610622565b8215801590610a9b57506001600160a01b0386163b155b15610add576040517fb5cf5b8f0000000000000000000000000000000000000000000000000000000081526001600160a01b0387166004820152602401610622565b6005805473ffffffffffffffffffffffffffffffffffffffff19811633179091556040516001600160a01b03918216918816908790610b1f9088908890611519565b60006040518083038185875af1925050503d8060008114610b5c576040519150601f19603f3d011682016040523d82523d6000602084013e610b61565b606091505b506005805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b038581169190911790915560405192955090935088169033907f2d9d115ef3e4a606d698913b1eae831a3cdfe20d9a83d48007b0526749c3d46690610bce908a908a908a90611529565b60405180910390a35094509492505050565b6005546000906001600160a01b03167fffffffffffffffffffffffff000000000000000000000000000000000000000101610c1b5750600090565b506005546001600160a01b031690565b600054610100900460ff1615808015610c4b5750600054600160ff909116105b80610c655750303b158015610c65575060005460ff166001145b610cf1576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201527f647920696e697469616c697a65640000000000000000000000000000000000006064820152608401610622565b6000805460ff191660011790558015610d14576000805461ff0019166101001790555b600580546001600160a01b0373ffffffffffffffffffffffffffffffffffffffff199182168117909255600680549091169184169190911790558015610876576000805461ff0019169055604051600181527f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb38474024989060200160405180910390a15050565b6006546001600160a01b03163314610e615760065460408051638da5cb5b60e01b815290516000926001600160a01b031691638da5cb5b9160048083019260209291908290030181865afa158015610df3573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610e1791906114af565b9050336001600160a01b03821614610e5f57600654604051630739600760e01b81523360048201526001600160a01b0391821660248201529082166044820152606401610622565b505b6001600160a01b038216600081815260026020908152604091829020600181015492518515158152909360ff90931692917f49477e7356dbcb654ab85d7534b50126772d938130d1350e23e2540370c8dffa910160405180910390a2808015610ec75750825b80610ed9575080158015610ed9575082155b15610ee45750505050565b8215610f8057604080518082018252600480548252600160208084018281526001600160a01b038a16600081815260029093529582209451855551938201805460ff1916941515949094179093558154908101825591527f8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19b01805473ffffffffffffffffffffffffffffffffffffffff19169091179055610873565b60048054610f90906001906114cc565b81548110610fa057610fa06114ed565b6000918252602090912001548254600480546001600160a01b03909316929091908110610fcf57610fcf6114ed565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b03160217905550816000015460026000600485600001548154811061101d5761101d6114ed565b60009182526020808320909101546001600160a01b03168352820192909252604001902055600480548061105357611053611503565b600082815260208082208301600019908101805473ffffffffffffffffffffffffffffffffffffffff191690559092019092556001600160a01b03861682526002905260408120908155600101805460ff1916905550505050565b6009818154811061054d57600080fd5b60038181548110610a1957600080fd5b600954604080517fff0000000000000000000000000000000000000000000000000000000000000060f88a901b166020808301919091527fffffffffffffffffffffffffffffffffffffffff00000000000000000000000060608a901b1660218301527fffffffffffffffff00000000000000000000000000000000000000000000000060c089811b8216603585015288901b16603d830152604582018490526065820186905260858083018690528351808403909101815260a5909201909252805191012060009190600082156111cb5760096111ad6001856114cc565b815481106111bd576111bd6114ed565b906000526020600020015490505b60096111d78284611267565b8154600181018355600092835260209283902001556040805133815260ff8d16928101929092526001600160a01b038b1682820152606082018790526080820188905267ffffffffffffffff891660a083015251829185917f5e3c1311ea442664e8b1611bfabef659120ea7a0a2cfc0667700bebc69cbffe19181900360c00190a3509098975050505050505050565b604080516020808201859052818301849052825180830384018152606090920190925280519101205b92915050565b6000602082840312156112a857600080fd5b5035919050565b6001600160a01b03811681146112c457600080fd5b50565b6000602082840312156112d957600080fd5b81356112e4816112af565b9392505050565b600080604083850312156112fe57600080fd5b8235611309816112af565b91506020830135801515811461131e57600080fd5b809150509250929050565b6000806040838503121561133c57600080fd5b8235611347816112af565b946020939093013593505050565b6000806000806080858703121561136b57600080fd5b5050823594602084013594506040840135936060013592509050565b60008060006060848603121561139c57600080fd5b833560ff811681146113ad57600080fd5b925060208401356113bd816112af565b929592945050506040919091013590565b600080600080606085870312156113e457600080fd5b84356113ef816112af565b935060208501359250604085013567ffffffffffffffff8082111561141357600080fd5b818701915087601f83011261142757600080fd5b81358181111561143657600080fd5b88602082850101111561144857600080fd5b95989497505060200194505050565b821515815260006020604081840152835180604085015260005b8181101561148d57858101830151858201606001528201611471565b506000606082860101526060601f19601f830116850101925050509392505050565b6000602082840312156114c157600080fd5b81516112e4816112af565b8181038181111561129057634e487b7160e01b600052601160045260246000fd5b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052603160045260246000fd5b8183823760009101908152919050565b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f191601019291505056fea26469706673582212206c800f769f79d977a2d145fae8844908fb62f2b420e5c6fb3df38b03c57d3ce564736f6c63430008110033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}