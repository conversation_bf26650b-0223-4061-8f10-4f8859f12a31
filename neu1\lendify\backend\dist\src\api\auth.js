"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const User_1 = require("../models/User");
const web3_1 = require("../services/web3");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({ success: true, service: 'Auth API', message: 'Service is healthy' });
});
router.post('/nonce', (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        walletAddress: validation_1.commonSchemas.ethereumAddress
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { walletAddress } = req.body;
    const nonce = crypto_1.default.randomBytes(16).toString('hex');
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
    await User_1.User.findOneAndUpdate({ walletAddress: walletAddress.toLowerCase() }, {
        tempAuth: {
            nonce,
            expiresAt,
            attempts: 0
        }
    }, { upsert: true, new: true });
    const message = `Please sign this message to authenticate with Lendify:\n\nNonce: ${nonce}\nTimestamp: ${new Date().toISOString()}`;
    res.json({
        success: true,
        data: {
            nonce,
            message,
            expiresAt
        }
    });
}));
router.post('/verify', (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        walletAddress: validation_1.commonSchemas.ethereumAddress,
        signature: joi_1.default.string().required(),
        message: joi_1.default.string().required()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { walletAddress, signature, message } = req.body;
    const user = await User_1.User.findOne({
        walletAddress: walletAddress.toLowerCase(),
        'tempAuth.expiresAt': { $gt: new Date() }
    });
    if (!user || !user.tempAuth) {
        return res.status(401).json({
            success: false,
            error: 'Nonce expired or not found. Please request a new nonce.'
        });
    }
    if (user.tempAuth.attempts >= 3) {
        return res.status(429).json({
            success: false,
            error: 'Too many authentication attempts. Please request a new nonce.'
        });
    }
    try {
        const isValidSignature = await web3_1.web3Service.verifySignature(message, signature, walletAddress);
        if (!isValidSignature) {
            user.tempAuth.attempts += 1;
            await user.save();
            return res.status(401).json({
                success: false,
                error: 'Invalid signature'
            });
        }
        user.tempAuth = undefined;
        user.lastLogin = new Date();
        user.loginCount = (user.loginCount || 0) + 1;
        await user.save();
        const tokenPayload = {
            userId: user._id,
            address: user.walletAddress,
            role: user.role || 'user'
        };
        const accessToken = jsonwebtoken_1.default.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        const refreshToken = jsonwebtoken_1.default.sign({ userId: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });
        user.refreshTokens = user.refreshTokens || [];
        user.refreshTokens.push({
            token: refreshToken,
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            deviceInfo: req.get('User-Agent') || 'Unknown'
        });
        await user.save();
        res.json({
            success: true,
            message: 'Authentication successful',
            data: {
                user: {
                    id: user._id,
                    walletAddress: user.walletAddress,
                    username: user.username,
                    role: user.role,
                    isVerified: user.isVerified,
                    reputation: user.reputation,
                    preferences: user.preferences
                },
                tokens: {
                    accessToken,
                    refreshToken
                }
            }
        });
    }
    catch (error) {
        console.error('Signature verification error:', error);
        return res.status(500).json({
            success: false,
            error: 'Authentication failed'
        });
    }
}));
router.post('/refresh', (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        refreshToken: joi_1.default.string().required()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    try {
        const decoded = jsonwebtoken_1.default.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
        const user = await User_1.User.findById(decoded.userId);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'User not found'
            });
        }
        const tokenIndex = user.refreshTokens?.findIndex(t => t.token === refreshToken && t.expiresAt > new Date());
        if (tokenIndex === undefined || tokenIndex === -1) {
            return res.status(401).json({
                success: false,
                error: 'Invalid or expired refresh token'
            });
        }
        const newAccessToken = jsonwebtoken_1.default.sign({
            userId: user._id,
            address: user.walletAddress,
            role: user.role || 'user'
        }, process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        res.json({
            success: true,
            data: {
                accessToken: newAccessToken
            }
        });
    }
    catch (error) {
        return res.status(401).json({
            success: false,
            error: 'Invalid refresh token'
        });
    }
}));
router.post('/profile/complete', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        username: joi_1.default.string().alphanum().min(3).max(30).optional(),
        email: joi_1.default.string().email().optional(),
        bio: joi_1.default.string().max(500).optional(),
        avatar: joi_1.default.string().uri().optional(),
        preferences: joi_1.default.object({
            notifications: joi_1.default.object({
                email: joi_1.default.boolean().default(true),
                push: joi_1.default.boolean().default(true),
                sms: joi_1.default.boolean().default(false)
            }).optional(),
            privacy: joi_1.default.object({
                showProfile: joi_1.default.boolean().default(true),
                showActivity: joi_1.default.boolean().default(true),
                showNFTs: joi_1.default.boolean().default(true)
            }).optional(),
            defaultChain: validation_1.commonSchemas.chainId.optional()
        }).optional()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.userId;
    const { username, email, bio, avatar, preferences } = req.body;
    const user = await User_1.User.findById(userId);
    if (!user) {
        return res.status(404).json({
            success: false,
            error: 'User not found'
        });
    }
    if (username && username !== user.username) {
        const existingUser = await User_1.User.findOne({ username });
        if (existingUser) {
            return res.status(409).json({
                success: false,
                error: 'Username already taken'
            });
        }
    }
    if (username)
        user.username = username;
    if (email)
        user.email = email;
    if (bio)
        user.bio = bio;
    if (avatar)
        user.avatar = avatar;
    if (preferences) {
        user.preferences = { ...user.preferences, ...preferences };
    }
    user.profileCompleted = true;
    user.updatedAt = new Date();
    await user.save();
    res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
            user: {
                id: user._id,
                walletAddress: user.walletAddress,
                username: user.username,
                email: user.email,
                bio: user.bio,
                avatar: user.avatar,
                profileCompleted: user.profileCompleted,
                preferences: user.preferences
            }
        }
    });
}));
router.get('/profile', auth_1.authMiddleware.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.userId;
    const user = await User_1.User.findById(userId)
        .select('-refreshTokens -tempAuth');
    if (!user) {
        return res.status(404).json({
            success: false,
            error: 'User not found'
        });
    }
    res.json({
        success: true,
        data: { user }
    });
}));
router.patch('/preferences', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        notifications: joi_1.default.object({
            email: joi_1.default.boolean().optional(),
            push: joi_1.default.boolean().optional(),
            sms: joi_1.default.boolean().optional()
        }).optional(),
        privacy: joi_1.default.object({
            showProfile: joi_1.default.boolean().optional(),
            showActivity: joi_1.default.boolean().optional(),
            showNFTs: joi_1.default.boolean().optional()
        }).optional(),
        defaultChain: validation_1.commonSchemas.chainId.optional(),
        language: joi_1.default.string().valid('en', 'es', 'fr', 'de', 'ja', 'ko', 'zh').optional(),
        theme: joi_1.default.string().valid('light', 'dark', 'auto').optional()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.userId;
    const updates = req.body;
    const user = await User_1.User.findByIdAndUpdate(userId, {
        $set: {
            'preferences': { ...updates },
            'updatedAt': new Date()
        }
    }, { new: true, runValidators: true }).select('-refreshTokens -tempAuth');
    if (!user) {
        return res.status(404).json({
            success: false,
            error: 'User not found'
        });
    }
    res.json({
        success: true,
        message: 'Preferences updated successfully',
        data: { user }
    });
}));
router.post('/logout', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        refreshToken: joi_1.default.string().optional(),
        logoutAll: joi_1.default.boolean().default(false)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.userId;
    const { refreshToken, logoutAll } = req.body;
    const user = await User_1.User.findById(userId);
    if (!user) {
        return res.status(404).json({
            success: false,
            error: 'User not found'
        });
    }
    if (logoutAll) {
        user.refreshTokens = [];
    }
    else if (refreshToken) {
        user.refreshTokens = user.refreshTokens?.filter(t => t.token !== refreshToken) || [];
    }
    await user.save();
    res.json({
        success: true,
        message: logoutAll ? 'Logged out from all devices' : 'Logged out successfully'
    });
}));
router.get('/user/:address', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        address: validation_1.commonSchemas.ethereumAddress
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { address } = req.params;
    const user = await User_1.User.findOne({
        walletAddress: address.toLowerCase()
    }).select('walletAddress username avatar bio isVerified reputation profileCompleted createdAt');
    if (!user) {
        return res.status(404).json({
            success: false,
            error: 'User not found'
        });
    }
    res.json({
        success: true,
        data: { user }
    });
}));
exports.default = router;
