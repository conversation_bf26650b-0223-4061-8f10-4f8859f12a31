{"_format": "hh-sol-artifact-1", "contractName": "IEdgeStakingPool", "sourceName": "src/assertionStakingPool/interfaces/IEdgeStakingPool.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}], "name": "AmountExceedsBalance", "type": "error"}, {"inputs": [], "name": "EmptyEdgeId", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "actual", "type": "bytes32"}, {"internalType": "bytes32", "name": "expected", "type": "bytes32"}], "name": "IncorrectEdgeId", "type": "error"}, {"inputs": [], "name": "ZeroAmount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeWithdrawn", "type": "event"}, {"inputs": [], "name": "challenge<PERSON>anager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "endHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"internalType": "bytes", "name": "prefixProof", "type": "bytes"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "internalType": "struct CreateEdgeArgs", "name": "args", "type": "tuple"}], "name": "createEdge", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "depositBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "depositIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "edgeId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}