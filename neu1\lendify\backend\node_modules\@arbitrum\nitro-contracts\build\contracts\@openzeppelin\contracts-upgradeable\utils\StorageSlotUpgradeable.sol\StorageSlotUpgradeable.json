{"_format": "hh-sol-artifact-1", "contractName": "StorageSlotUpgradeable", "sourceName": "@openzeppelin/contracts-upgradeable/utils/StorageSlotUpgradeable.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220d8d07c04af1c17af31d86993aa924d9a413e93e736c90df40272c22b82bac16a64736f6c63430008110033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220d8d07c04af1c17af31d86993aa924d9a413e93e736c90df40272c22b82bac16a64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}