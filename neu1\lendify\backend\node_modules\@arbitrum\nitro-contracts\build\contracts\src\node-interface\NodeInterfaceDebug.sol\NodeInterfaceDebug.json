{"_format": "hh-sol-artifact-1", "contractName": "NodeInterfaceDebug", "sourceName": "src/node-interface/NodeInterfaceDebug.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "ticket", "type": "bytes32"}], "name": "getRetryable", "outputs": [{"components": [{"internalType": "uint64", "name": "timeout", "type": "uint64"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint64", "name": "tries", "type": "uint64"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "internalType": "struct NodeInterfaceDebug.RetryableInfo", "name": "retryable", "type": "tuple"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}