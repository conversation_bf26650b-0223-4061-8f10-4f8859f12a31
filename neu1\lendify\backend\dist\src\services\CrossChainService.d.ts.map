{"version": 3, "file": "CrossChainService.d.ts", "sourceRoot": "", "sources": ["../../../src/services/CrossChainService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,MAAM,WAAW,SAAS;IACxB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,QAAQ,CAAC;IACtE,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,IAAI,CAAC;IAChB,WAAW,CAAC,EAAE,IAAI,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,IAAI,EAAE;QACJ,GAAG,EAAE,MAAM,CAAC;QACZ,MAAM,EAAE,MAAM,CAAC;QACf,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH;AAED,MAAM,WAAW,aAAa;IAC5B,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,gBAAgB,EAAE,MAAM,CAAC;IACzB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,aAAa,EAAE,iBAAiB,EAAE,CAAC;IACnC,QAAQ,EAAE,GAAG,CAAC;CACf;AAED,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE;QAAE,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACzC,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,IAAI,EAAE;QAAE,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,cAAc,EAAE;QAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CAC7C;AAED,qBAAa,iBAAkB,SAAQ,YAAY;IACjD,OAAO,CAAC,eAAe,CAAqC;IAC5D,OAAO,CAAC,aAAa,CAAwC;IAC7D,OAAO,CAAC,kBAAkB,CAA6C;IACvE,OAAO,CAAC,WAAW,CAAmC;;IAQzC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAkBxC,OAAO,CAAC,yBAAyB;IAqEjC,OAAO,CAAC,uBAAuB;YA0FjB,sBAAsB;YAmBtB,wBAAwB;YAaxB,oBAAoB;IAMlC,OAAO,CAAC,eAAe;IAehB,kBAAkB,IAAI,SAAS,EAAE;IAIjC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,GAAG,SAAS;IAIpD,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,YAAY,EAAE;IAajE,cAAc,CACzB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,MAAM,EACf,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC;QACT,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE;YAAE,GAAG,EAAE,MAAM,CAAC;YAAC,MAAM,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAE,CAAC;QACrD,aAAa,EAAE,MAAM,CAAC;QACtB,KAAK,EAAE,MAAM,EAAE,CAAC;KACjB,EAAE,CAAC;YAwBU,uBAAuB;IAuBxB,SAAS,CACpB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,MAAM,EACf,SAAS,EAAE,MAAM,EACjB,UAAU,GAAE,MAAoB,GAC/B,OAAO,CAAC,iBAAiB,CAAC;YA4Cf,yBAAyB;YASzB,mBAAmB;YAgBnB,cAAc;IAaf,oBAAoB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAC;IAInF,2BAA2B,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAa9E,mBAAmB,CAC9B,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAalB,oBAAoB,CAC/B,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAuBhC,OAAO,CAAC,kBAAkB;YAMZ,cAAc;YAuBd,yBAAyB;YAsBzB,4BAA4B;IAiBnC,cAAc,IAAI;QAAE,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE;IAIhD,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;IAIlC,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;QACrD,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,OAAO,CAAC;KACpB,GAAG,IAAI,CAAC;YA0BK,cAAc;YAUd,WAAW;IAWzB,OAAO,CAAC,qBAAqB;IAIhB,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAW/E,mBAAmB,IAAI;QAC5B,iBAAiB,EAAE,MAAM,CAAC;QAC1B,qBAAqB,EAAE,MAAM,CAAC;QAC9B,kBAAkB,EAAE,MAAM,CAAC;QAC3B,kBAAkB,EAAE,MAAM,CAAC;QAC3B,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;KACrB;IAaD,OAAO,CAAC,0BAA0B;IAc3B,yBAAyB,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,iBAAiB,KAAK,IAAI,GAAG,IAAI;IAMnF,gBAAgB,IAAI;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY,CAAA;KAAE;IAI9C,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAc1G"}