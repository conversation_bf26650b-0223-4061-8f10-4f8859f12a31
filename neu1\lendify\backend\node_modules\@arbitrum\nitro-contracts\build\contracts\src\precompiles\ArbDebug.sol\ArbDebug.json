{"_format": "hh-sol-artifact-1", "contractName": "ArbDebug", "sourceName": "src/precompiles/ArbDebug.sol", "abi": [{"inputs": [{"internalType": "uint64", "name": "", "type": "uint64"}, {"internalType": "string", "name": "", "type": "string"}, {"internalType": "bool", "name": "", "type": "bool"}], "name": "Custom", "type": "error"}, {"inputs": [], "name": "Unused", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "flag", "type": "bool"}, {"indexed": true, "internalType": "bytes32", "name": "value", "type": "bytes32"}], "name": "Basic", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bool", "name": "flag", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "not", "type": "bool"}, {"indexed": true, "internalType": "bytes32", "name": "value", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "conn", "type": "address"}, {"indexed": true, "internalType": "address", "name": "caller", "type": "address"}], "name": "Mixed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bool", "name": "flag", "type": "bool"}, {"indexed": true, "internalType": "address", "name": "field", "type": "address"}, {"indexed": false, "internalType": "uint24", "name": "number", "type": "uint24"}, {"indexed": false, "internalType": "bytes32", "name": "value", "type": "bytes32"}, {"indexed": false, "internalType": "bytes", "name": "store", "type": "bytes"}], "name": "Store", "type": "event"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "number", "type": "uint64"}], "name": "customRevert", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "flag", "type": "bool"}, {"internalType": "bytes32", "name": "value", "type": "bytes32"}], "name": "events", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "eventsView", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "legacyError", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "panic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}