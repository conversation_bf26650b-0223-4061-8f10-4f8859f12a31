{"_format": "hh-sol-artifact-1", "contractName": "CrossChainEnabledAMB", "sourceName": "contracts/crosschain/amb/CrossChainEnabledAMB.sol", "abi": [{"inputs": [{"internalType": "address", "name": "bridge", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": "0x60a0604052348015600f57600080fd5b5060405160bc38038060bc833981016040819052602a91603a565b6001600160a01b03166080526068565b600060208284031215604b57600080fd5b81516001600160a01b0381168114606157600080fd5b9392505050565b608051603f607d60003960005050603f6000f3fe6080604052600080fdfea26469706673582212209884bd67993706561c6e5b4d273a774582590b63288266669c10629930cb4ad664736f6c63430008090033", "deployedBytecode": "0x6080604052600080fdfea26469706673582212209884bd67993706561c6e5b4d273a774582590b63288266669c10629930cb4ad664736f6c63430008090033", "linkReferences": {}, "deployedLinkReferences": {}}