{"_format": "hh-sol-artifact-1", "contractName": "OneStepProverMath", "sourceName": "src/osp/OneStepProverMath.sol", "abi": [{"inputs": [{"components": [{"internalType": "uint256", "name": "maxInboxMessagesRead", "type": "uint256"}, {"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "bytes32", "name": "initialWasmModuleRoot", "type": "bytes32"}], "internalType": "struct ExecutionContext", "name": "", "type": "tuple"}, {"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "startMach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "startMod", "type": "tuple"}, {"components": [{"internalType": "uint16", "name": "opcode", "type": "uint16"}, {"internalType": "uint256", "name": "argumentData", "type": "uint256"}], "internalType": "struct Instruction", "name": "inst", "type": "tuple"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "executeOneStep", "outputs": [{"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "mach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "mod", "type": "tuple"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}