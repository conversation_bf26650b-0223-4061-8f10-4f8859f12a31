{"_format": "hh-sol-artifact-1", "contractName": "ArbFunctionTable", "sourceName": "src/precompiles/ArbFunctionTable.sol", "abi": [{"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "get", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "size", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "buf", "type": "bytes"}], "name": "upload", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}