"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Web3Service = void 0;
const ethers_1 = require("ethers");
const config_1 = require("../config/config");
const NFTRentalMarketplace_json_1 = __importDefault(require("../contracts/abis/NFTRentalMarketplace.json"));
const ReputationSystem_json_1 = __importDefault(require("../contracts/abis/ReputationSystem.json"));
const NFTCollateralizedLending_json_1 = __importDefault(require("../contracts/abis/NFTCollateralizedLending.json"));
const FlashRentalLoan_json_1 = __importDefault(require("../contracts/abis/FlashRentalLoan.json"));
const DynamicPricingOracle_json_1 = __importDefault(require("../contracts/abis/DynamicPricingOracle.json"));
class Web3Service {
    constructor() {
        this.providers = new Map();
        this.signers = new Map();
        this.contracts = new Map();
        this.chainConfigs = new Map();
        this.initializeChainConfigs();
    }
    initializeChainConfigs() {
        this.chainConfigs.set(1, {
            chainId: 1,
            name: 'Ethereum Mainnet',
            rpcUrl: config_1.config.blockchain.ethereum.rpcUrl,
            currency: 'ETH',
            blockExplorerUrl: 'https://etherscan.io',
            contracts: {
                nftRentalMarketplace: config_1.config.contracts.ethereum.nftRentalMarketplace,
                reputationSystem: config_1.config.contracts.ethereum.reputationSystem,
                nftCollateralizedLending: config_1.config.contracts.ethereum.nftCollateralizedLending,
                flashRentalLoan: config_1.config.contracts.ethereum.flashRentalLoan,
                dynamicPricingOracle: config_1.config.contracts.ethereum.dynamicPricingOracle,
            }
        });
        this.chainConfigs.set(137, {
            chainId: 137,
            name: 'Polygon Mainnet',
            rpcUrl: config_1.config.blockchain.polygon.rpcUrl,
            currency: 'MATIC',
            blockExplorerUrl: 'https://polygonscan.com',
            contracts: {
                nftRentalMarketplace: config_1.config.contracts.polygon.nftRentalMarketplace,
                reputationSystem: config_1.config.contracts.polygon.reputationSystem,
                nftCollateralizedLending: config_1.config.contracts.polygon.nftCollateralizedLending,
                flashRentalLoan: config_1.config.contracts.polygon.flashRentalLoan,
                dynamicPricingOracle: config_1.config.contracts.polygon.dynamicPricingOracle,
            }
        });
        this.chainConfigs.set(42161, {
            chainId: 42161,
            name: 'Arbitrum One',
            rpcUrl: config_1.config.blockchain.arbitrum.rpcUrl,
            currency: 'ETH',
            blockExplorerUrl: 'https://arbiscan.io',
            contracts: {
                nftRentalMarketplace: config_1.config.contracts.arbitrum.nftRentalMarketplace,
                reputationSystem: config_1.config.contracts.arbitrum.reputationSystem,
                nftCollateralizedLending: config_1.config.contracts.arbitrum.nftCollateralizedLending,
                flashRentalLoan: config_1.config.contracts.arbitrum.flashRentalLoan,
                dynamicPricingOracle: config_1.config.contracts.arbitrum.dynamicPricingOracle,
            }
        });
        this.chainConfigs.set(10, {
            chainId: 10,
            name: 'Optimism',
            rpcUrl: config_1.config.blockchain.optimism.rpcUrl,
            currency: 'ETH',
            blockExplorerUrl: 'https://optimistic.etherscan.io',
            contracts: {
                nftRentalMarketplace: config_1.config.contracts.optimism.nftRentalMarketplace,
                reputationSystem: config_1.config.contracts.optimism.reputationSystem,
                nftCollateralizedLending: config_1.config.contracts.optimism.nftCollateralizedLending,
                flashRentalLoan: config_1.config.contracts.optimism.flashRentalLoan,
                dynamicPricingOracle: config_1.config.contracts.optimism.dynamicPricingOracle,
            }
        });
        this.chainConfigs.set(8453, {
            chainId: 8453,
            name: 'Base',
            rpcUrl: config_1.config.blockchain.base.rpcUrl,
            currency: 'ETH',
            blockExplorerUrl: 'https://basescan.org',
            contracts: {
                nftRentalMarketplace: config_1.config.contracts.base.nftRentalMarketplace,
                reputationSystem: config_1.config.contracts.base.reputationSystem,
                nftCollateralizedLending: config_1.config.contracts.base.nftCollateralizedLending,
                flashRentalLoan: config_1.config.contracts.base.flashRentalLoan,
                dynamicPricingOracle: config_1.config.contracts.base.dynamicPricingOracle,
            }
        });
    }
    async initialize() {
        try {
            for (const [chainId, chainConfig] of this.chainConfigs.entries()) {
                const provider = new ethers_1.JsonRpcProvider(chainConfig.rpcUrl, chainId);
                this.providers.set(chainId, provider);
                const privateKey = process.env.DEPLOYER_PRIVATE_KEY;
                if (privateKey) {
                    const signer = new ethers_1.Wallet(privateKey, provider);
                    this.signers.set(chainId, signer);
                }
                await this.initializeContractsForChain(chainId);
            }
            console.log('✅ Web3Service initialized for all supported chains');
        }
        catch (error) {
            console.error('❌ Failed to initialize Web3Service:', error);
            throw error;
        }
    }
    async initializeContractsForChain(chainId) {
        const chainConfig = this.chainConfigs.get(chainId);
        const provider = this.providers.get(chainId);
        const signer = this.signers.get(chainId);
        if (!chainConfig || !provider) {
            throw new Error(`Chain ${chainId} not configured`);
        }
        try {
            if (chainConfig.contracts.nftRentalMarketplace) {
                const marketplaceContract = new ethers_1.Contract(chainConfig.contracts.nftRentalMarketplace, NFTRentalMarketplace_json_1.default, signer || provider);
                this.contracts.set(`marketplace_${chainId}`, marketplaceContract);
            }
            if (chainConfig.contracts.reputationSystem) {
                const reputationContract = new ethers_1.Contract(chainConfig.contracts.reputationSystem, ReputationSystem_json_1.default, signer || provider);
                this.contracts.set(`reputation_${chainId}`, reputationContract);
            }
            if (chainConfig.contracts.nftCollateralizedLending) {
                const lendingContract = new ethers_1.Contract(chainConfig.contracts.nftCollateralizedLending, NFTCollateralizedLending_json_1.default, signer || provider);
                this.contracts.set(`lending_${chainId}`, lendingContract);
            }
            if (chainConfig.contracts.flashRentalLoan) {
                const flashLoanContract = new ethers_1.Contract(chainConfig.contracts.flashRentalLoan, FlashRentalLoan_json_1.default, signer || provider);
                this.contracts.set(`flashloan_${chainId}`, flashLoanContract);
            }
            if (chainConfig.contracts.dynamicPricingOracle) {
                const oracleContract = new ethers_1.Contract(chainConfig.contracts.dynamicPricingOracle, DynamicPricingOracle_json_1.default, signer || provider);
                this.contracts.set(`oracle_${chainId}`, oracleContract);
            }
            console.log(`✅ Contracts initialized for chain ${chainId} (${chainConfig.name})`);
        }
        catch (error) {
            console.error(`❌ Failed to initialize contracts for chain ${chainId}:`, error);
            throw error;
        }
    }
    getProvider(chainId) {
        return this.providers.get(chainId);
    }
    getSigner(chainId) {
        return this.signers.get(chainId);
    }
    getContract(contractType, chainId) {
        return this.contracts.get(`${contractType}_${chainId}`);
    }
    getSupportedChains() {
        return Array.from(this.chainConfigs.values());
    }
    getChainConfig(chainId) {
        return this.chainConfigs.get(chainId);
    }
    async getNFTInfo(nftContract, tokenId, chainId) {
        const provider = this.getProvider(chainId);
        if (!provider) {
            throw new Error(`Provider not found for chain ${chainId}`);
        }
        const contract = new ethers_1.Contract(nftContract, [
            "function ownerOf(uint256 tokenId) view returns (address)",
            "function getApproved(uint256 tokenId) view returns (address)",
            "function tokenURI(uint256 tokenId) view returns (string)",
        ], provider);
        try {
            const [owner, approved, tokenURI] = await Promise.all([
                contract.ownerOf(tokenId),
                contract.getApproved(tokenId),
                contract.tokenURI(tokenId)
            ]);
            return {
                tokenId,
                owner,
                approved,
                tokenURI
            };
        }
        catch (error) {
            console.error(`Error getting NFT info for ${nftContract}:${tokenId}:`, error);
            throw error;
        }
    }
    async listNFTForRent(nftContract, tokenId, pricePerDay, collateral, duration, category, chainId) {
        const contract = this.getContract('marketplace', chainId);
        const signer = this.getSigner(chainId);
        if (!contract || !signer) {
            throw new Error(`Contract or signer not found for chain ${chainId}`);
        }
        try {
            const tx = await contract.listNFTForRent(nftContract, tokenId, ethers_1.ethers.parseEther(pricePerDay), ethers_1.ethers.parseEther(collateral), duration, category);
            const receipt = await tx.wait();
            return {
                success: true,
                txHash: tx.hash,
                receipt,
                gasUsed: receipt.gasUsed,
                effectiveGasPrice: receipt.effectiveGasPrice
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Transaction failed'
            };
        }
    }
    async rentNFT(listingId, chainId, paymentAmount) {
        const contract = this.getContract('marketplace', chainId);
        const signer = this.getSigner(chainId);
        if (!contract || !signer) {
            throw new Error(`Contract or signer not found for chain ${chainId}`);
        }
        try {
            const tx = await contract.rentNFT(listingId, {
                value: ethers_1.ethers.parseEther(paymentAmount)
            });
            const receipt = await tx.wait();
            return {
                success: true,
                txHash: tx.hash,
                receipt,
                gasUsed: receipt.gasUsed,
                effectiveGasPrice: receipt.effectiveGasPrice
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Transaction failed'
            };
        }
    }
    async getRentalInfo(listingId, chainId) {
        const contract = this.getContract('marketplace', chainId);
        if (!contract) {
            throw new Error(`Marketplace contract not found for chain ${chainId}`);
        }
        try {
            const rental = await contract.getRental(listingId);
            return {
                listingId,
                nftContract: rental.nftContract,
                tokenId: Number(rental.tokenId),
                owner: rental.owner,
                user: rental.user,
                pricePerDay: rental.pricePerDay,
                collateral: rental.collateral,
                duration: Number(rental.duration),
                expires: Number(rental.expires),
                isActive: rental.isActive,
                category: rental.category
            };
        }
        catch (error) {
            console.error(`Error getting rental info for listing ${listingId}:`, error);
            return null;
        }
    }
    async requestLoan(nftContract, tokenId, loanAmount, duration, chainId) {
        const contract = this.getContract('lending', chainId);
        const signer = this.getSigner(chainId);
        if (!contract || !signer) {
            throw new Error(`Lending contract or signer not found for chain ${chainId}`);
        }
        try {
            const tx = await contract.requestLoan(nftContract, tokenId, ethers_1.ethers.parseEther(loanAmount), duration);
            const receipt = await tx.wait();
            return {
                success: true,
                txHash: tx.hash,
                receipt,
                gasUsed: receipt.gasUsed,
                effectiveGasPrice: receipt.effectiveGasPrice
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Transaction failed'
            };
        }
    }
    async fundLoan(loanId, chainId, fundAmount) {
        const contract = this.getContract('lending', chainId);
        const signer = this.getSigner(chainId);
        if (!contract || !signer) {
            throw new Error(`Lending contract or signer not found for chain ${chainId}`);
        }
        try {
            const tx = await contract.fundLoan(loanId, {
                value: ethers_1.ethers.parseEther(fundAmount)
            });
            const receipt = await tx.wait();
            return {
                success: true,
                txHash: tx.hash,
                receipt,
                gasUsed: receipt.gasUsed,
                effectiveGasPrice: receipt.effectiveGasPrice
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Transaction failed'
            };
        }
    }
    async executeFlashRental(nftContract, tokenId, rentalDuration, data, chainId) {
        const contract = this.getContract('flashloan', chainId);
        const signer = this.getSigner(chainId);
        if (!contract || !signer) {
            throw new Error(`Flash loan contract or signer not found for chain ${chainId}`);
        }
        try {
            const tx = await contract.executeFlashRental(nftContract, tokenId, rentalDuration, data);
            const receipt = await tx.wait();
            return {
                success: true,
                txHash: tx.hash,
                receipt,
                gasUsed: receipt.gasUsed,
                effectiveGasPrice: receipt.effectiveGasPrice
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Transaction failed'
            };
        }
    }
    async getUserReputation(userAddress, chainId) {
        const contract = this.getContract('reputation', chainId);
        if (!contract) {
            throw new Error(`Reputation contract not found for chain ${chainId}`);
        }
        try {
            const tokenId = await contract.userToTokenId(userAddress);
            if (tokenId === 0n) {
                return null;
            }
            const reputation = await contract.getReputation(tokenId);
            const level = await contract.getReputationLevel(tokenId);
            return {
                tokenId: tokenId.toString(),
                totalRentals: reputation.totalRentals.toString(),
                successfulRentals: reputation.successfulRentals.toString(),
                totalEarnings: ethers_1.ethers.formatEther(reputation.totalEarnings),
                disputes: reputation.disputes.toString(),
                averageRating: Number(reputation.averageRating) / 100,
                level
            };
        }
        catch (error) {
            console.error(`Error getting user reputation for ${userAddress}:`, error);
            throw error;
        }
    }
    async getDynamicPrice(nftContract, tokenId, chainId) {
        const contract = this.getContract('oracle', chainId);
        if (!contract) {
            throw new Error(`Oracle contract not found for chain ${chainId}`);
        }
        try {
            const priceData = await contract.getPriceData(nftContract, tokenId);
            return ethers_1.ethers.formatEther(priceData.currentPrice);
        }
        catch (error) {
            console.error(`Error getting dynamic price for ${nftContract}:${tokenId}:`, error);
            return null;
        }
    }
    async estimateGas(contract, methodName, params) {
        try {
            return await contract[methodName].estimateGas(...params);
        }
        catch (error) {
            console.error(`Gas estimation failed for ${methodName}:`, error);
            throw error;
        }
    }
    async waitForTransaction(txHash, chainId, confirmations = 1) {
        const provider = this.getProvider(chainId);
        if (!provider) {
            throw new Error(`Provider not found for chain ${chainId}`);
        }
        try {
            return await provider.waitForTransaction(txHash, confirmations);
        }
        catch (error) {
            console.error(`Failed to wait for transaction ${txHash}:`, error);
            throw error;
        }
    }
    async getContractEvents(contractType, chainId, eventName, fromBlock = -10000, toBlock = 'latest') {
        const contract = this.getContract(contractType, chainId);
        if (!contract) {
            throw new Error(`${contractType} contract not found for chain ${chainId}`);
        }
        try {
            const filter = contract.filters[eventName]();
            return await contract.queryFilter(filter, fromBlock, toBlock);
        }
        catch (error) {
            console.error(`Error getting events for ${contractType}.${eventName}:`, error);
            throw error;
        }
    }
    async getBlockNumber(chainId) {
        const provider = this.getProvider(chainId);
        if (!provider) {
            throw new Error(`Provider not found for chain ${chainId}`);
        }
        return await provider.getBlockNumber();
    }
    async getBalance(address, chainId) {
        const provider = this.getProvider(chainId);
        if (!provider) {
            throw new Error(`Provider not found for chain ${chainId}`);
        }
        const balance = await provider.getBalance(address);
        return ethers_1.ethers.formatEther(balance);
    }
    async getGasPrice(chainId) {
        const provider = this.getProvider(chainId);
        if (!provider) {
            throw new Error(`Provider not found for chain ${chainId}`);
        }
        const gasPrice = await provider.getFeeData();
        return ethers_1.ethers.formatUnits(gasPrice.gasPrice || 0n, 'gwei');
    }
    async healthCheck() {
        const results = {};
        for (const chainId of this.providers.keys()) {
            try {
                const provider = this.getProvider(chainId);
                if (provider) {
                    await provider.getBlockNumber();
                    results[chainId] = true;
                }
                else {
                    results[chainId] = false;
                }
            }
            catch (error) {
                results[chainId] = false;
            }
        }
        return results;
    }
}
exports.Web3Service = Web3Service;
