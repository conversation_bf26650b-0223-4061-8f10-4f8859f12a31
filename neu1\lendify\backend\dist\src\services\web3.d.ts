export declare class Web3Service {
    private providers;
    constructor();
    verifySignature(message: string, signature: string, address: string): Promise<boolean>;
    verifyNFTOwnership(contractAddress: string, tokenId: string, ownerAddress: string, chainId: number): Promise<boolean>;
    getNFTMetadata(contractAddress: string, tokenId: string, chainId: number): Promise<any>;
    getTokenBalance(tokenAddress: string, userAddress: string, chainId: number): Promise<string>;
}
export declare const web3Service: Web3Service;
//# sourceMappingURL=web3.d.ts.map