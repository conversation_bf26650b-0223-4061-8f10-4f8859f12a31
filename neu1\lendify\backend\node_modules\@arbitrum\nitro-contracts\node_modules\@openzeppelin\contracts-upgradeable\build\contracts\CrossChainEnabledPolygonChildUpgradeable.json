{"_format": "hh-sol-artifact-1", "contractName": "CrossChainEnabledPolygonChildUpgradeable", "sourceName": "contracts/crosschain/polygon/CrossChainEnabledPolygonChildUpgradeable.sol", "abi": [{"inputs": [], "name": "NotCrossChainCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "rootMessageSender", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "processMessageFromRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}