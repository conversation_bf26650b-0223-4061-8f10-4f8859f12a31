{"_format": "hh-sol-artifact-1", "contractName": "ArbWasm", "sourceName": "src/precompiles/ArbWasm.sol", "abi": [{"inputs": [{"internalType": "uint64", "name": "ageInSeconds", "type": "uint64"}], "name": "ProgramExpired", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "have", "type": "uint256"}, {"internalType": "uint256", "name": "want", "type": "uint256"}], "name": "ProgramInsufficientValue", "type": "error"}, {"inputs": [{"internalType": "uint64", "name": "ageInSeconds", "type": "uint64"}], "name": "ProgramKeepaliveTooSoon", "type": "error"}, {"inputs": [{"internalType": "uint16", "name": "version", "type": "uint16"}, {"internalType": "uint16", "name": "stylusVersion", "type": "uint16"}], "name": "ProgramNeedsUpgrade", "type": "error"}, {"inputs": [], "name": "ProgramNotActivated", "type": "error"}, {"inputs": [], "name": "ProgramNotWasm", "type": "error"}, {"inputs": [], "name": "ProgramUpToDate", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "moduleHash", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "program", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "dataFee", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "version", "type": "uint16"}], "name": "ProgramActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "dataFee", "type": "uint256"}], "name": "ProgramLifetimeExtended", "type": "event"}, {"inputs": [{"internalType": "address", "name": "program", "type": "address"}], "name": "activateProgram", "outputs": [{"internalType": "uint16", "name": "version", "type": "uint16"}, {"internalType": "uint256", "name": "dataFee", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "blockCacheSize", "outputs": [{"internalType": "uint16", "name": "count", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "codehashAsmSize", "outputs": [{"internalType": "uint32", "name": "size", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "codehashKeepalive", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "codehashVersion", "outputs": [{"internalType": "uint16", "name": "version", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "expiryDays", "outputs": [{"internalType": "uint16", "name": "_days", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "freePages", "outputs": [{"internalType": "uint16", "name": "pages", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initCostScalar", "outputs": [{"internalType": "uint64", "name": "percent", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "inkPrice", "outputs": [{"internalType": "uint32", "name": "price", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "keepaliveDays", "outputs": [{"internalType": "uint16", "name": "_days", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint32", "name": "depth", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minInitGas", "outputs": [{"internalType": "uint64", "name": "gas", "type": "uint64"}, {"internalType": "uint64", "name": "cached", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pageGas", "outputs": [{"internalType": "uint16", "name": "gas", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pageLimit", "outputs": [{"internalType": "uint16", "name": "limit", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pageRamp", "outputs": [{"internalType": "uint64", "name": "ramp", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "program", "type": "address"}], "name": "programInitGas", "outputs": [{"internalType": "uint64", "name": "gas", "type": "uint64"}, {"internalType": "uint64", "name": "gasWhenCached", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "program", "type": "address"}], "name": "programMemoryFootprint", "outputs": [{"internalType": "uint16", "name": "footprint", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "program", "type": "address"}], "name": "programTimeLeft", "outputs": [{"internalType": "uint64", "name": "_secs", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "program", "type": "address"}], "name": "programVersion", "outputs": [{"internalType": "uint16", "name": "version", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stylusVersion", "outputs": [{"internalType": "uint16", "name": "version", "type": "uint16"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}