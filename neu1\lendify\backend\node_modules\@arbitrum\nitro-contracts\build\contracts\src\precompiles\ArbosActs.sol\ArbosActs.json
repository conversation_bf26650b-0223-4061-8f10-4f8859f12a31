{"_format": "hh-sol-artifact-1", "contractName": "ArbosActs", "sourceName": "src/precompiles/ArbosActs.sol", "abi": [{"inputs": [], "name": "CallerNotArbOS", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "batchTimestamp", "type": "uint256"}, {"internalType": "address", "name": "batchPosterAddress", "type": "address"}, {"internalType": "uint64", "name": "batchNumber", "type": "uint64"}, {"internalType": "uint64", "name": "batchDataGas", "type": "uint64"}, {"internalType": "uint256", "name": "l1BaseFeeWei", "type": "uint256"}], "name": "batchPostingReport", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "l1BaseFee", "type": "uint256"}, {"internalType": "uint64", "name": "l1BlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "l2BlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "timePassed", "type": "uint64"}], "name": "startBlock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}