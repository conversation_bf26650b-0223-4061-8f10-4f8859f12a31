import mongoose, { Document, Schema } from 'mongoose';
export interface ILoan extends Document {
    _id: string;
    borrower: string;
    lender?: string;
    nft: {
        contractAddress: string;
        tokenId: string;
        chainId: number;
        nftId?: Schema.Types.ObjectId;
        estimatedValue: number;
        oraclePrice?: number;
    };
    loan: {
        principal: number;
        currency: string;
        interestRate: number;
        duration: number;
        ltv: number;
        startDate?: Date;
        dueDate?: Date;
        actualDueDate?: Date;
    };
    repayment: {
        totalAmount: number;
        amountPaid: number;
        remainingAmount: number;
        payments: Array<{
            amount: number;
            timestamp: Date;
            txHash: string;
            type: 'partial' | 'full' | 'interest_only';
        }>;
        gracePeriod: number;
        lateFee: number;
    };
    collateral: {
        isEscrowed: boolean;
        escrowContract?: string;
        escrowTxHash?: string;
        releaseCondition: 'repayment' | 'liquidation';
        liquidationThreshold: number;
        currentValue?: number;
        lastValuation?: Date;
    };
    blockchain: {
        loanContract: string;
        loanId?: number;
        requestTxHash?: string;
        fundingTxHash?: string;
        repaymentTxHash?: string;
        liquidationTxHash?: string;
        blockNumber?: number;
    };
    status: 'requested' | 'funded' | 'active' | 'repaid' | 'defaulted' | 'liquidated' | 'cancelled';
    timeline: Array<{
        status: string;
        timestamp: Date;
        txHash?: string;
        amount?: number;
        notes?: string;
    }>;
    terms: {
        autoLiquidation: boolean;
        partialRepayment: boolean;
        earlyRepayment: boolean;
        extensionAllowed: boolean;
        maxExtensions: number;
        extensionFee: number;
        penaltyRate: number;
    };
    extensions: Array<{
        originalDueDate: Date;
        newDueDate: Date;
        additionalDays: number;
        extensionFee: number;
        additionalInterest: number;
        timestamp: Date;
        txHash?: string;
        reason?: string;
    }>;
    risk: {
        creditScore?: number;
        riskLevel: 'low' | 'medium' | 'high';
        riskFactors: string[];
        volatilityScore: number;
        liquidityScore: number;
    };
    dispute?: {
        isDisputed: boolean;
        reason: string;
        initiator: 'borrower' | 'lender';
        description: string;
        evidence: Array<{
            type: 'text' | 'image' | 'document';
            content: string;
            uploadedBy: 'borrower' | 'lender';
            timestamp: Date;
        }>;
        resolution?: {
            decision: 'favor_borrower' | 'favor_lender' | 'partial_relief' | 'no_fault';
            reasoning: string;
            adjustedAmount?: number;
            newTerms?: any;
            resolvedBy: string;
            resolvedAt: Date;
        };
    };
    analytics: {
        inquiries: number;
        views: number;
        competingOffers: number;
    };
    metadata: {
        purpose?: string;
        notes?: string;
        visibility: 'public' | 'private';
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare const Loan: mongoose.Model<ILoan, {}, {}, {}, mongoose.Document<unknown, {}, ILoan, {}, {}> & ILoan & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Loan.d.ts.map