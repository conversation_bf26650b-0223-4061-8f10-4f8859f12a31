import { EventEmitter } from 'events';
import { Server as SocketIOServer } from 'socket.io';
export interface NotificationTemplate {
    id: string;
    type: 'email' | 'push' | 'sms' | 'websocket';
    name: string;
    subject?: string;
    template: string;
    variables: string[];
}
export interface NotificationPreferences {
    userId: string;
    email: boolean;
    push: boolean;
    sms: boolean;
    websocket: boolean;
    categories: {
        rental: boolean;
        lending: boolean;
        payments: boolean;
        disputes: boolean;
        marketing: boolean;
        system: boolean;
    };
}
export interface Notification {
    id: string;
    userId: string;
    type: 'rental' | 'lending' | 'payment' | 'dispute' | 'system' | 'marketing';
    priority: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    message: string;
    data?: any;
    channels: Array<'email' | 'push' | 'sms' | 'websocket'>;
    createdAt: Date;
    sentAt?: Date;
    readAt?: Date;
    status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
}
export interface EmailConfig {
    host: string;
    port: number;
    secure: boolean;
    auth: {
        user: string;
        pass: string;
    };
    from: string;
}
export interface PushConfig {
    fcmServerKey: string;
    apnsKeyId: string;
    apnsTeamId: string;
    apnsKeyFile: string;
}
export declare class NotificationService extends EventEmitter {
    private io;
    private emailTransporter?;
    private templates;
    private userPreferences;
    private notificationQueue;
    private isProcessing;
    constructor(io: SocketIOServer);
    private initializeEmailTransporter;
    private initializeTemplates;
    private startNotificationProcessor;
    sendNotification(notification: Omit<Notification, 'id' | 'createdAt' | 'status'>): Promise<boolean>;
    sendRentalNotification(type: 'rental_created' | 'rental_request' | 'rental_approved' | 'rental_expiring', userId: string, data: any, channels?: Array<'email' | 'push' | 'sms' | 'websocket'>): Promise<boolean>;
    sendLendingNotification(type: 'loan_request' | 'loan_funded' | 'loan_repaid', userId: string, data: any, channels?: Array<'email' | 'push' | 'sms' | 'websocket'>): Promise<boolean>;
    sendPaymentNotification(type: 'payment_received' | 'payment_overdue' | 'payment_failed', userId: string, data: any, channels?: Array<'email' | 'push' | 'sms' | 'websocket'>): Promise<boolean>;
    sendSystemNotification(type: 'welcome' | 'maintenance' | 'security_alert', userId: string, data: any, channels?: Array<'email' | 'push' | 'sms' | 'websocket'>): Promise<boolean>;
    private sendWebSocketNotification;
    private sendEmailNotification;
    private sendPushNotification;
    private sendSMSNotification;
    private processNotificationQueue;
    private sendToChannel;
    getUserPreferences(userId: string): Promise<NotificationPreferences>;
    updateUserPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<void>;
    sendBulkNotification(userIds: string[], notification: Omit<Notification, 'id' | 'userId' | 'createdAt' | 'status'>): Promise<void>;
    sendBroadcast(notification: Omit<Notification, 'id' | 'userId' | 'createdAt' | 'status'>, criteria?: {
        active?: boolean;
        category?: string;
    }): Promise<void>;
    sendInstantNotification(userId: string, message: string, data?: any): Promise<void>;
    broadcastToAll(message: string, data?: any): Promise<void>;
    private generateNotificationId;
    private renderTemplate;
    private renderEmailTemplate;
    private getNotificationTitle;
    private getNotificationPriority;
    private getTemplateForType;
    private shouldSendNotification;
    private sendFCMNotification;
    private storeNotification;
    private saveUserPreferences;
    private getUserEmail;
    private getUserPushTokens;
    private getUserPhoneNumber;
    private getUsersForBroadcast;
    getQueueSize(): number;
    getProcessingStatus(): boolean;
    clearQueue(): void;
}
//# sourceMappingURL=NotificationService.d.ts.map