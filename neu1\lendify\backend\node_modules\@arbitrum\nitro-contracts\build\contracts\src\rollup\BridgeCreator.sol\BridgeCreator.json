{"_format": "hh-sol-artifact-1", "contractName": "BridgeCreator", "sourceName": "src/rollup/BridgeCreator.sol", "abi": [{"inputs": [{"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "internalType": "struct BridgeCreator.BridgeTemplates", "name": "_ethBasedTemplates", "type": "tuple"}, {"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "internalType": "struct BridgeCreator.BridgeTemplates", "name": "_erc20BasedTemplates", "type": "tuple"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [], "name": "ERC20TemplatesUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [], "name": "TemplatesUpdated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "adminProxy", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}, {"internalType": "address", "name": "nativeToken", "type": "address"}, {"components": [{"internalType": "uint256", "name": "delayBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "futureBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "delaySeconds", "type": "uint256"}, {"internalType": "uint256", "name": "futureSeconds", "type": "uint256"}], "internalType": "struct ISequencerInbox.MaxTimeVariation", "name": "maxTimeVariation", "type": "tuple"}, {"components": [{"internalType": "uint64", "name": "threshold", "type": "uint64"}, {"internalType": "uint64", "name": "max", "type": "uint64"}, {"internalType": "uint64", "name": "replenishRateInBasis", "type": "uint64"}], "internalType": "struct BufferConfig", "name": "bufferConfig", "type": "tuple"}], "name": "createBridge", "outputs": [{"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "internalType": "struct BridgeCreator.BridgeContracts", "name": "", "type": "tuple"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "erc20BasedTemplates", "outputs": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ethBasedTemplates", "outputs": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "internalType": "struct BridgeCreator.BridgeTemplates", "name": "_newTemplates", "type": "tuple"}], "name": "updateERC20Templates", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "delayBufferableSequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}], "internalType": "struct BridgeCreator.BridgeTemplates", "name": "_newTemplates", "type": "tuple"}], "name": "updateTemplates", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}