import { Router, Request, Response } from 'express';
import { CrossChainService } from '../services/CrossChainService';

const router = Router();
const crossChainService = new CrossChainService();

// Initialize the service
crossChainService.initialize().catch(console.error);

// Health check
router.get('/health', (req: Request, res: Response) => {
  res.json({ success: true, service: 'Cross Chain API', message: 'Service is healthy' });
});

// Get supported chains
router.get('/chains', (req: Request, res: Response) => {
  try {
    const chains = crossChainService.getSupportedChains();
    res.json({ success: true, data: chains });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get chain status
router.get('/status', async (req: Request, res: Response) => {
  try {
    const status = crossChainService.getChainStatus();
    res.json({ success: true, data: status });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get chain metrics
router.get('/metrics/:chainId', async (req: Request, res: Response) => {
  try {
    const chainId = parseInt(req.params.chainId);
    const metrics = await crossChainService.getChainMetrics(chainId);
    res.json({ success: true, data: metrics });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get bridge quote
router.post('/quote', async (req: Request, res: Response) => {
  try {
    const { fromChain, toChain, tokenContract, tokenId, bridgeName } = req.body;

    if (!fromChain || !toChain || !tokenContract) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: fromChain, toChain, tokenContract'
      });
    }

    const quotes = await crossChainService.getBridgeQuote(
      fromChain,
      toChain,
      tokenContract,
      tokenId,
      bridgeName
    );

    res.json({ success: true, data: quotes });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Bridge NFT
router.post('/bridge/nft', async (req: Request, res: Response) => {
  try {
    const { fromChain, toChain, tokenContract, tokenId, toAddress, bridgeName } = req.body;

    if (!fromChain || !toChain || !tokenContract || !tokenId || !toAddress) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters'
      });
    }

    const transaction = await crossChainService.bridgeNFT(
      fromChain,
      toChain,
      tokenContract,
      tokenId,
      toAddress,
      bridgeName
    );

    res.json({ success: true, data: transaction });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Bridge token
router.post('/bridge/token', async (req: Request, res: Response) => {
  try {
    const { fromChain, toChain, tokenContract, amount, toAddress } = req.body;

    if (!fromChain || !toChain || !tokenContract || !amount || !toAddress) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters'
      });
    }

    // For now, use the NFT bridge function as a placeholder
    const transaction = await crossChainService.bridgeNFT(
      fromChain,
      toChain,
      tokenContract,
      '0', // Use '0' as tokenId for tokens
      toAddress,
      'layerzero'
    );

    // Update transaction type
    transaction.amount = amount;
    transaction.tokenId = undefined;

    res.json({ success: true, data: transaction });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get bridge transaction
router.get('/transaction/:transactionId', async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const transaction = await crossChainService.getBridgeTransaction(transactionId);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    res.json({ success: true, data: transaction });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get bridge history for user
router.get('/history/:userAddress', async (req: Request, res: Response) => {
  try {
    const { userAddress } = req.params;
    const { page, limit, status, fromChain, toChain } = req.query;

    const options = {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      status: status as string,
      fromChain: fromChain ? parseInt(fromChain as string) : undefined,
      toChain: toChain ? parseInt(toChain as string) : undefined
    };

    const history = await crossChainService.getBridgeTransactionsByUser(userAddress);
    res.json({ success: true, data: history });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get user NFTs for a chain
router.get('/nfts/:userAddress/:chainId', async (req: Request, res: Response) => {
  try {
    const { userAddress, chainId } = req.params;
    const numericChainId = parseInt(chainId);

    // This would typically integrate with NFT indexing services
    // For now, return mock data
    const mockNFTs = [
      {
        id: 1,
        name: 'Cosmic Warrior #1234',
        collection: 'MetaGaming Heroes',
        currentChain: numericChainId,
        availableChains: [1, 137, 56].filter(id => id !== numericChainId),
        estimatedValue: '2.5 ETH',
        utility: ['Gaming', 'Staking', 'Governance'],
        bridgeCost: '0.003 ETH',
        tokenContract: '******************************************',
        tokenId: '1234'
      },
      {
        id: 2,
        name: 'Digital Land Parcel #567',
        collection: 'Virtual Worlds',
        currentChain: numericChainId,
        availableChains: [1, 137, 43114].filter(id => id !== numericChainId),
        estimatedValue: '1.8 ETH',
        utility: ['Metaverse', 'Rental Income'],
        bridgeCost: '0.002 ETH',
        tokenContract: '******************************************',
        tokenId: '567'
      }
    ];

    res.json({ success: true, data: mockNFTs });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get bridge statistics
router.get('/statistics', (req: Request, res: Response) => {
  try {
    const stats = crossChainService.getBridgeStatistics();
    res.json({ success: true, data: stats });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Validate bridge route
router.post('/validate-route', async (req: Request, res: Response) => {
  try {
    const { fromChain, toChain } = req.body;

    if (!fromChain || !toChain) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: fromChain, toChain'
      });
    }

    const isValid = await crossChainService.validateBridgeRoute(fromChain, toChain);
    res.json({ success: true, data: { isValid } });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

export default router;