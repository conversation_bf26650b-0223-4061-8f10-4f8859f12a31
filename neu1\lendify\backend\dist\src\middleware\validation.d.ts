import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
export declare const validateRequest: (schema: {
    body?: Joi.ObjectSchema;
    query?: Joi.ObjectSchema;
    params?: Joi.ObjectSchema;
}) => (req: Request, res: Response, next: NextFunction) => void;
export declare const commonSchemas: {
    ethereumAddress: Joi.StringSchema<string>;
    objectId: Joi.StringSchema<string>;
    mongoId: Joi.StringSchema<string>;
    pagination: Joi.ObjectSchema<any>;
    chainId: Joi.NumberSchema<number>;
    tokenId: Joi.StringSchema<string>;
    price: Joi.StringSchema<string>;
    duration: Joi.NumberSchema<number>;
};
//# sourceMappingURL=validation.d.ts.map