{"_format": "hh-sol-artifact-1", "contractName": "ERC20PresetFixedSupplyUpgradeable", "sourceName": "contracts/token/ERC20/presets/ERC20PresetFixedSupplyUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}