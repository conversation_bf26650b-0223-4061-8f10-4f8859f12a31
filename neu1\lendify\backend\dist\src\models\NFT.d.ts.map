{"version": 3, "file": "NFT.d.ts", "sourceRoot": "", "sources": ["../../../src/models/NFT.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAEtD,MAAM,WAAW,IAAK,SAAQ,QAAQ;IACpC,GAAG,EAAE,MAAM,CAAC;IACZ,eAAe,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE;QACR,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,KAAK,CAAC;YAChB,SAAS,EAAE,MAAM,CAAC;YAClB,KAAK,EAAE,GAAG,CAAC;YACX,WAAW,CAAC,EAAE,MAAM,CAAC;YACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;SACnB,CAAC,CAAC;KACJ,CAAC;IACF,UAAU,EAAE;QACV,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,QAAQ,EAAE,OAAO,CAAC;KACnB,CAAC;IACF,OAAO,EAAE;QACP,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAC3B,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,YAAY,EAAE,KAAK,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC;YACd,QAAQ,EAAE,MAAM,CAAC;YACjB,SAAS,EAAE,IAAI,CAAC;YAChB,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC;SACvC,CAAC,CAAC;KACJ,CAAC;IACF,MAAM,EAAE;QACN,WAAW,EAAE,OAAO,CAAC;QACrB,cAAc,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACvC,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,aAAa,EAAE,MAAM,CAAC;QACtB,cAAc,CAAC,EAAE,IAAI,CAAC;QACtB,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC;YAChB,KAAK,EAAE,MAAM,CAAC;YACd,SAAS,EAAE;gBACT,CAAC,EAAE,MAAM,CAAC;gBACV,CAAC,EAAE,MAAM,CAAC;gBACV,CAAC,EAAE,MAAM,CAAC;gBACV,CAAC,EAAE,MAAM,CAAC;gBACV,CAAC,EAAE,MAAM,CAAC;aACX,CAAC;SACH,CAAC;KACH,CAAC;IACF,OAAO,EAAE;QACP,YAAY,EAAE,OAAO,CAAC;QACtB,WAAW,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACpC,UAAU,EAAE,MAAM,CAAC;QACnB,aAAa,EAAE,MAAM,CAAC;QACtB,YAAY,CAAC,EAAE,IAAI,CAAC;KACrB,CAAC;IACF,UAAU,CAAC,EAAE;QACX,QAAQ,EAAE,OAAO,CAAC;QAClB,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC/B,QAAQ,CAAC,EAAE,IAAI,CAAC;KACjB,CAAC;IACF,SAAS,EAAE;QACT,KAAK,EAAE,MAAM,CAAC;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC;QACrB,aAAa,EAAE,MAAM,CAAC;QACtB,YAAY,EAAE,IAAI,CAAC;KACpB,CAAC;IACF,UAAU,EAAE;QACV,aAAa,EAAE,MAAM,CAAC;QACtB,aAAa,EAAE,KAAK,CAAC;YACnB,SAAS,EAAE,MAAM,CAAC;YAClB,OAAO,EAAE,MAAM,CAAC;YAChB,eAAe,EAAE,MAAM,CAAC;YACxB,cAAc,EAAE,MAAM,CAAC;YACvB,SAAS,EAAE,IAAI,CAAC;YAChB,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,QAAQ,CAAC;SAC5C,CAAC,CAAC;KACJ,CAAC;IACF,YAAY,EAAE;QACZ,UAAU,EAAE,OAAO,CAAC;QACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;QAC5B,UAAU,CAAC,EAAE,IAAI,CAAC;KACnB,CAAC;IACF,MAAM,EAAE,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC;IACvD,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAwRD,eAAO,MAAM,GAAG;;;;OAAyC,CAAC"}