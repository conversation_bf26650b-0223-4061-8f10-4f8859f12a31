import { Application } from 'express';
import { Server as SocketIOServer } from 'socket.io';
import { Web3Service } from './services/Web3Service';
import { NotificationService } from './services/NotificationService';
import { AnalyticsService } from './services/AnalyticsService';
import { AIService } from './services/AIService';
import { CrossChainService } from './services/CrossChainService';
declare class LendifyBackendServer {
    app: Application;
    server: any;
    io: SocketIOServer;
    web3Service: Web3Service;
    notificationService: NotificationService;
    analyticsService: AnalyticsService;
    aiService: AIService;
    crossChainService: CrossChainService;
    constructor();
    private initializeServices;
    private setupMiddleware;
    private setupRoutes;
    private setupErrorHandling;
    private setupSocketIO;
    connectDatabase(): Promise<void>;
    start(): Promise<void>;
    getApp(): Application;
    getIO(): SocketIOServer;
}
declare const server: LendifyBackendServer;
export default server;
export { LendifyBackendServer };
//# sourceMappingURL=server.d.ts.map