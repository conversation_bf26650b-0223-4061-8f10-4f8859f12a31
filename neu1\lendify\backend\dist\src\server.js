"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LendifyBackendServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const auth_1 = __importDefault(require("./api/auth"));
const nft_1 = __importDefault(require("./api/nft"));
const rental_1 = __importDefault(require("./api/rental"));
const lending_1 = __importDefault(require("./api/lending"));
const flashLoan_1 = __importDefault(require("./api/flashLoan"));
const oracle_1 = __importDefault(require("./api/oracle"));
const reputation_1 = __importDefault(require("./api/reputation"));
const analytics_1 = __importDefault(require("./api/analytics"));
const crossChain_1 = __importDefault(require("./api/crossChain"));
const dao_1 = __importDefault(require("./api/dao"));
const notification_1 = __importDefault(require("./api/notification"));
const webhook_1 = __importDefault(require("./api/webhook"));
const logger_1 = require("./middleware/logger");
const Web3Service_1 = require("./services/Web3Service");
const NotificationService_1 = require("./services/NotificationService");
const AnalyticsService_1 = require("./services/AnalyticsService");
const AIService_1 = require("./services/AIService");
const CrossChainService_1 = require("./services/CrossChainService");
dotenv_1.default.config();
class LendifyBackendServer {
    constructor() {
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:3000",
                methods: ["GET", "POST"]
            }
        });
        this.initializeServices();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
        this.setupSocketIO();
    }
    initializeServices() {
        this.web3Service = new Web3Service_1.Web3Service();
        this.notificationService = new NotificationService_1.NotificationService(this.io);
        this.analyticsService = new AnalyticsService_1.AnalyticsService();
        this.aiService = new AIService_1.AIService();
        this.crossChainService = new CrossChainService_1.CrossChainService();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)({
            crossOriginResourcePolicy: { policy: "cross-origin" },
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: process.env.NODE_ENV === 'production'
                ? [process.env.FRONTEND_URL]
                : ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174'],
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Chain-ID']
        }));
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
            max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
            message: {
                error: 'Too many requests from this IP, please try again later.',
                retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        const strictLimiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: 5,
            message: {
                error: 'Too many sensitive requests from this IP, please try again later.'
            }
        });
        this.app.use('/api/', limiter);
        this.app.use(['/api/auth/login', '/api/auth/register', '/api/lending/request', '/api/flash-loan/execute'], strictLimiter);
        if (process.env.NODE_ENV === 'development') {
            this.app.use((0, morgan_1.default)('dev'));
        }
        else {
            this.app.use((0, morgan_1.default)('combined'));
        }
        this.app.use(logger_1.requestLogger);
        this.app.use(express_1.default.json({
            limit: '10mb',
            verify: (req, res, buf, encoding) => {
                if (req.originalUrl && req.originalUrl.includes('/webhook')) {
                    req.rawBody = buf;
                }
            }
        }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.set('trust proxy', 1);
        this.app.get('/health', (req, res) => {
            res.status(200).json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.env.npm_package_version || '1.0.0'
            });
        });
        this.app.get('/api', (req, res) => {
            res.json({
                name: 'Lendify Backend API',
                version: '2.0.0',
                description: 'Advanced NFT rental marketplace with DeFi integration',
                endpoints: {
                    auth: '/api/auth',
                    nft: '/api/nft',
                    rental: '/api/rental',
                    lending: '/api/lending',
                    flashLoan: '/api/flash-loan',
                    oracle: '/api/oracle',
                    reputation: '/api/reputation',
                    analytics: '/api/analytics',
                    crossChain: '/api/cross-chain',
                    dao: '/api/dao',
                    notifications: '/api/notifications',
                    webhooks: '/api/webhook'
                },
                features: [
                    'ERC-4907 NFT Rentals',
                    'NFT Collateralized Lending',
                    'Flash Loans',
                    'AI-Powered Dynamic Pricing',
                    'Cross-Chain Support',
                    'Reputation System',
                    'DAO Governance',
                    'Real-time Notifications'
                ]
            });
        });
    }
    setupRoutes() {
        this.app.use('/api/auth', auth_1.default);
        this.app.use('/api/nft', nft_1.default);
        this.app.use('/api/rental', rental_1.default);
        this.app.use('/api/lending', lending_1.default);
        this.app.use('/api/flash-loan', flashLoan_1.default);
        this.app.use('/api/oracle', oracle_1.default);
        this.app.use('/api/reputation', reputation_1.default);
        this.app.use('/api/analytics', analytics_1.default);
        this.app.use('/api/cross-chain', crossChain_1.default);
        this.app.use('/api/dao', dao_1.default);
        this.app.use('/api/notifications', notification_1.default);
        this.app.use('/api/webhook', webhook_1.default);
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Route not found',
                message: `Cannot ${req.method} ${req.originalUrl}`,
                suggestion: 'Check the API documentation for available endpoints'
            });
        });
    }
    setupErrorHandling() {
        this.app.use((err, req, res, next) => {
            console.error('Global error handler:', err);
            if (err.name === 'ValidationError') {
                return res.status(400).json({
                    error: 'Validation Error',
                    details: err.message,
                    timestamp: new Date().toISOString()
                });
            }
            if (err.name === 'MongoError' && err.code === 11000) {
                return res.status(409).json({
                    error: 'Duplicate Entry',
                    message: 'Resource already exists',
                    timestamp: new Date().toISOString()
                });
            }
            if (err.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    error: 'Invalid Token',
                    message: 'Authentication token is invalid',
                    timestamp: new Date().toISOString()
                });
            }
            if (err.name === 'TokenExpiredError') {
                return res.status(401).json({
                    error: 'Token Expired',
                    message: 'Authentication token has expired',
                    timestamp: new Date().toISOString()
                });
            }
            if (err.message && err.message.includes('execution reverted')) {
                return res.status(400).json({
                    error: 'Transaction Failed',
                    message: 'Smart contract execution failed',
                    details: err.message,
                    timestamp: new Date().toISOString()
                });
            }
            const statusCode = err.statusCode || 500;
            res.status(statusCode).json({
                error: statusCode === 500 ? 'Internal Server Error' : err.name || 'Unknown Error',
                message: statusCode === 500 && process.env.NODE_ENV === 'production'
                    ? 'Something went wrong on our end'
                    : err.message,
                ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
                timestamp: new Date().toISOString()
            });
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('Unhandled Rejection at:', promise, 'reason:', reason);
            this.server.close(() => {
                process.exit(1);
            });
        });
        process.on('uncaughtException', (error) => {
            console.error('Uncaught Exception:', error);
            this.server.close(() => {
                process.exit(1);
            });
        });
        process.on('SIGTERM', () => {
            console.log('SIGTERM received, shutting down gracefully...');
            this.server.close(() => {
                mongoose_1.default.connection.close();
                console.log('MongoDB connection closed.');
                process.exit(0);
            });
        });
        process.on('SIGINT', () => {
            console.log('SIGINT received, shutting down gracefully...');
            this.server.close(() => {
                mongoose_1.default.connection.close();
                console.log('MongoDB connection closed.');
                process.exit(0);
            });
        });
    }
    setupSocketIO() {
        this.io.use((socket, next) => {
            const token = socket.handshake.auth.token || socket.handshake.headers.authorization;
            if (!token) {
                return next(new Error('Authentication error'));
            }
            next();
        });
        this.io.on('connection', (socket) => {
            console.log(`Client connected: ${socket.id}`);
            socket.on('join-user-room', (userId) => {
                socket.join(`user:${userId}`);
                console.log(`User ${userId} joined their room`);
            });
            socket.on('monitor-nft', (data) => {
                const roomName = `nft:${data.nftContract}:${data.tokenId}`;
                socket.join(roomName);
                console.log(`Client monitoring NFT: ${data.nftContract}:${data.tokenId}`);
            });
            socket.on('monitor-rental', (rentalId) => {
                socket.join(`rental:${rentalId}`);
                console.log(`Client monitoring rental: ${rentalId}`);
            });
            socket.on('monitor-loan', (loanId) => {
                socket.join(`loan:${loanId}`);
                console.log(`Client monitoring loan: ${loanId}`);
            });
            socket.on('disconnect', (reason) => {
                console.log(`Client disconnected: ${socket.id}, reason: ${reason}`);
            });
            socket.on('error', (error) => {
                console.error(`Socket error from ${socket.id}:`, error);
            });
        });
    }
    async connectDatabase() {
        try {
            const mongoUri = process.env.MONGODB_URI;
            await mongoose_1.default.connect(mongoUri, {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
            });
            console.log('✅ Connected to MongoDB');
            mongoose_1.default.connection.on('error', (err) => {
                console.error('MongoDB connection error:', err);
            });
            mongoose_1.default.connection.on('disconnected', () => {
                console.log('MongoDB disconnected');
            });
            mongoose_1.default.connection.on('reconnected', () => {
                console.log('MongoDB reconnected');
            });
        }
        catch (error) {
            console.error('❌ MongoDB connection failed:', error);
            if (process.env.NODE_ENV === 'production') {
                process.exit(1);
            }
            else {
                console.warn('⚠️ Continuing without database in development mode');
            }
        }
    }
    async start() {
        try {
            await this.connectDatabase();
            await this.web3Service.initialize();
            console.log('✅ Web3 service initialized');
            await this.aiService.initialize();
            console.log('✅ AI service initialized');
            await this.crossChainService.initialize();
            console.log('✅ Cross-chain service initialized');
            this.analyticsService.startMetricsCollection();
            console.log('✅ Analytics service started');
            const PORT = process.env.PORT || 3001;
            this.server.listen(PORT, () => {
                console.log(`
🚀 Lendify Backend Server is running!

📍 Server Details:
   • Port: ${PORT}
   • Environment: ${process.env.NODE_ENV || 'development'}
   • API Base: http://localhost:${PORT}/api
   • Health Check: http://localhost:${PORT}/health

🔗 API Endpoints:
   • Authentication: /api/auth
   • NFT Operations: /api/nft
   • Rentals: /api/rental
   • Lending: /api/lending
   • Flash Loans: /api/flash-loan
   • Oracle: /api/oracle
   • Reputation: /api/reputation
   • Analytics: /api/analytics
   • Cross-Chain: /api/cross-chain
   • DAO: /api/dao
   • Notifications: /api/notifications
   • Webhooks: /api/webhook

🌐 WebSocket: Connected for real-time updates
💾 Database: MongoDB connected
⛓️  Blockchain: Multi-chain support enabled
🤖 AI Services: Dynamic pricing & analytics active
        `);
            });
        }
        catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }
    getApp() {
        return this.app;
    }
    getIO() {
        return this.io;
    }
}
exports.LendifyBackendServer = LendifyBackendServer;
const server = new LendifyBackendServer();
if (require.main === module) {
    server.start().catch((error) => {
        console.error('Failed to start server:', error);
        process.exit(1);
    });
}
exports.default = server;
