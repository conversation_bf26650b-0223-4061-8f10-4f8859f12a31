"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const ethers_1 = require("ethers");
class AuthMiddleware {
    constructor() {
        this.authenticate = async (req, res, next) => {
            try {
                const token = this.extractToken(req);
                if (!token) {
                    res.status(401).json({
                        error: 'Authentication required',
                        message: 'No token provided'
                    });
                    return;
                }
                const payload = jsonwebtoken_1.default.verify(token, this.jwtSecret);
                const user = await this.getUserById(payload.userId);
                if (!user) {
                    res.status(401).json({
                        error: 'Invalid token',
                        message: 'User not found'
                    });
                    return;
                }
                req.user = user;
                const chainId = req.headers['x-chain-id'];
                if (chainId) {
                    req.chainId = parseInt(chainId);
                }
                next();
            }
            catch (error) {
                if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                    res.status(401).json({
                        error: 'Token expired',
                        message: 'Please login again'
                    });
                    return;
                }
                if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                    res.status(401).json({
                        error: 'Invalid token',
                        message: 'Token is malformed'
                    });
                    return;
                }
                res.status(500).json({
                    error: 'Authentication error',
                    message: 'Internal server error'
                });
            }
        };
        this.optionalAuth = async (req, res, next) => {
            try {
                const token = this.extractToken(req);
                if (token) {
                    const payload = jsonwebtoken_1.default.verify(token, this.jwtSecret);
                    const user = await this.getUserById(payload.userId);
                    if (user) {
                        req.user = user;
                        const chainId = req.headers['x-chain-id'];
                        if (chainId) {
                            req.chainId = parseInt(chainId);
                        }
                    }
                }
                next();
            }
            catch (error) {
                next();
            }
        };
        this.requireRole = (roles) => {
            return (req, res, next) => {
                if (!req.user) {
                    res.status(401).json({
                        error: 'Authentication required',
                        message: 'Please login first'
                    });
                    return;
                }
                if (!roles.includes(req.user.role)) {
                    res.status(403).json({
                        error: 'Insufficient permissions',
                        message: `Required role: ${roles.join(' or ')}`
                    });
                    return;
                }
                next();
            };
        };
        this.requireAdmin = (req, res, next) => {
            this.requireRole(['admin'])(req, res, next);
        };
        this.requireModerator = (req, res, next) => {
            this.requireRole(['admin', 'moderator'])(req, res, next);
        };
        this.requireReputation = (minReputation) => {
            return (req, res, next) => {
                if (!req.user) {
                    res.status(401).json({
                        error: 'Authentication required',
                        message: 'Please login first'
                    });
                    return;
                }
                const userReputation = req.user.reputation || 0;
                if (userReputation < minReputation) {
                    res.status(403).json({
                        error: 'Insufficient reputation',
                        message: `Required reputation: ${minReputation}, current: ${userReputation}`
                    });
                    return;
                }
                next();
            };
        };
        this.requireEmailVerified = (req, res, next) => {
            if (!req.user) {
                res.status(401).json({
                    error: 'Authentication required',
                    message: 'Please login first'
                });
                return;
            }
            if (!req.user.isEmailVerified) {
                res.status(403).json({
                    error: 'Email verification required',
                    message: 'Please verify your email address'
                });
                return;
            }
            next();
        };
        this.requireSignature = async (req, res, next) => {
            try {
                if (!req.user) {
                    res.status(401).json({
                        error: 'Authentication required',
                        message: 'Please login first'
                    });
                    return;
                }
                const signatureData = req.body.signature;
                if (!signatureData || !signatureData.signature || !signatureData.message) {
                    res.status(400).json({
                        error: 'Signature required',
                        message: 'Please provide signature data'
                    });
                    return;
                }
                const isValidSignature = await this.verifySignature(signatureData.message, signatureData.signature, req.user.address);
                if (!isValidSignature) {
                    res.status(403).json({
                        error: 'Invalid signature',
                        message: 'Signature verification failed'
                    });
                    return;
                }
                const now = Date.now();
                const timeDiff = Math.abs(now - signatureData.timestamp);
                if (timeDiff > 5 * 60 * 1000) {
                    res.status(403).json({
                        error: 'Signature expired',
                        message: 'Please generate a new signature'
                    });
                    return;
                }
                next();
            }
            catch (error) {
                console.error('Signature verification error:', error);
                res.status(500).json({
                    error: 'Signature verification failed',
                    message: 'Internal server error'
                });
            }
        };
        this.reputationBasedRateLimit = (baseLimit) => {
            return (req, res, next) => {
                if (!req.user) {
                    res.status(401).json({
                        error: 'Authentication required',
                        message: 'Please login first'
                    });
                    return;
                }
                const reputation = req.user.reputation || 0;
                const multiplier = Math.min(2, 1 + (reputation / 1000));
                const adjustedLimit = Math.floor(baseLimit * multiplier);
                next();
            };
        };
        this.generateToken = (user) => {
            return jsonwebtoken_1.default.sign({
                userId: user.id,
                address: user.address,
                role: user.role
            }, this.jwtSecret, { expiresIn: this.jwtExpiration });
        };
        this.generateRefreshToken = (userId) => {
            return jsonwebtoken_1.default.sign({ userId, type: 'refresh' }, this.jwtSecret, { expiresIn: '7d' });
        };
        this.refreshToken = async (refreshToken) => {
            try {
                const payload = jsonwebtoken_1.default.verify(refreshToken, this.jwtSecret);
                if (payload.type !== 'refresh') {
                    return null;
                }
                const user = await this.getUserById(payload.userId);
                if (!user) {
                    return null;
                }
                return this.generateToken({
                    id: user.id,
                    address: user.address,
                    role: user.role
                });
            }
            catch (error) {
                return null;
            }
        };
        this.verifySignature = async (message, signature, expectedAddress) => {
            try {
                const recoveredAddress = ethers_1.ethers.verifyMessage(message, signature);
                return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
            }
            catch (error) {
                console.error('Signature verification error:', error);
                return false;
            }
        };
        this.authenticateWallet = async (req, res, next) => {
            try {
                const { message, signature, address } = req.body;
                if (!message || !signature || !address) {
                    res.status(400).json({
                        error: 'Missing required fields',
                        message: 'Message, signature, and address are required'
                    });
                    return;
                }
                const isValid = await this.verifySignature(message, signature, address);
                if (!isValid) {
                    res.status(401).json({
                        error: 'Invalid signature',
                        message: 'Signature verification failed'
                    });
                    return;
                }
                const messagePattern = /^Sign this message to authenticate with Lendify\nTimestamp: (\d+)\nNonce: ([a-f0-9]+)$/;
                const match = message.match(messagePattern);
                if (!match) {
                    res.status(400).json({
                        error: 'Invalid message format',
                        message: 'Message does not match required format'
                    });
                    return;
                }
                const timestamp = parseInt(match[1]);
                const nonce = match[2];
                const now = Date.now();
                if (Math.abs(now - timestamp) > 5 * 60 * 1000) {
                    res.status(400).json({
                        error: 'Message expired',
                        message: 'Please generate a new message'
                    });
                    return;
                }
                let user = await this.getUserByAddress(address);
                if (!user) {
                    user = await this.createUser(address);
                }
                await this.updateLastLogin(user.id);
                const accessToken = this.generateToken({
                    id: user.id,
                    address: user.address,
                    role: user.role
                });
                const refreshToken = this.generateRefreshToken(user.id);
                res.json({
                    success: true,
                    accessToken,
                    refreshToken,
                    user: {
                        id: user.id,
                        address: user.address,
                        role: user.role,
                        reputation: user.reputation,
                        isEmailVerified: user.isEmailVerified
                    }
                });
            }
            catch (error) {
                console.error('Wallet authentication error:', error);
                res.status(500).json({
                    error: 'Authentication failed',
                    message: 'Internal server error'
                });
            }
        };
        this.generateAuthMessage = (req, res) => {
            const timestamp = Date.now();
            const nonce = ethers_1.ethers.hexlify(ethers_1.ethers.randomBytes(16)).slice(2);
            const message = `Sign this message to authenticate with Lendify\nTimestamp: ${timestamp}\nNonce: ${nonce}`;
            res.json({
                message,
                timestamp,
                nonce
            });
        };
        this.extractToken = (req) => {
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                return authHeader.slice(7);
            }
            const tokenFromQuery = req.query.token;
            if (tokenFromQuery) {
                return tokenFromQuery;
            }
            const tokenFromCookie = req.cookies?.token;
            if (tokenFromCookie) {
                return tokenFromCookie;
            }
            return null;
        };
        this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-key';
        this.jwtExpiration = process.env.JWT_EXPIRATION || '24h';
    }
    async getUserById(userId) {
        return {
            id: userId,
            address: '0x1234567890123456789012345678901234567890',
            email: '<EMAIL>',
            role: 'user',
            reputation: 100,
            isEmailVerified: true,
            createdAt: new Date(),
            lastLogin: new Date()
        };
    }
    async getUserByAddress(address) {
        return null;
    }
    async createUser(address) {
        return {
            id: `user_${Date.now()}`,
            address: address.toLowerCase(),
            email: null,
            role: 'user',
            reputation: 0,
            isEmailVerified: false,
            createdAt: new Date(),
            lastLogin: new Date()
        };
    }
    async updateLastLogin(userId) {
        console.log(`Updated last login for user ${userId}`);
    }
}
const authMiddleware = new AuthMiddleware();
exports.authMiddleware = authMiddleware;
