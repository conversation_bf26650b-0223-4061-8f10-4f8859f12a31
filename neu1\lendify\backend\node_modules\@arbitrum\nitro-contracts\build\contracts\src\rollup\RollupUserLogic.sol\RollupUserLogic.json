{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON>ser<PERSON><PERSON><PERSON>", "sourceName": "src/rollup/RollupUserLogic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "sendRoot", "type": "bytes32"}], "name": "AssertionConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "indexed": false, "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"indexed": false, "internalType": "bytes32", "name": "afterInboxBatchAcc", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}], "name": "AssertionCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint64", "name": "challengeIndex", "type": "uint64"}, {"indexed": false, "internalType": "address", "name": "asserter", "type": "address"}, {"indexed": false, "internalType": "address", "name": "challenger", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "challengedAsser<PERSON>", "type": "uint64"}], "name": "RollupChallengeStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "machineHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "RollupInitialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "UpgradedSecondary", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserStakeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserWithdrawableFundsUpdated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "_stakerMap", "outputs": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "bytes32", "name": "latestStakedAssertion", "type": "bytes32"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "name": "addToDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "amountStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "anyTrustFastConfirmer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseStake", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "chainId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "challengeGracePeriodBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "challenge<PERSON>anager", "outputs": [{"internalType": "contract IEdgeChallengeManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "state", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "computeAssertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "confirmState", "type": "tuple"}, {"internalType": "bytes32", "name": "winningEdgeId", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "prevConfig", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "confirmAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "confirmPeriodBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "confirmState", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "fastConfirmAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "fastConfirmNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "genesisAssertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertion", "outputs": [{"components": [{"internalType": "uint64", "name": "first<PERSON><PERSON><PERSON><PERSON>lock", "type": "uint64"}, {"internalType": "uint64", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint64"}, {"internalType": "uint64", "name": "createdAtBlock", "type": "uint64"}, {"internalType": "bool", "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "enum AssertionStatus", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "config<PERSON><PERSON>", "type": "bytes32"}], "internalType": "struct AssertionNode", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertionCreationBlockForLogLookup", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getFirstChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getSecondChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "get<PERSON><PERSON>", "outputs": [{"components": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "bytes32", "name": "latestStakedAssertion", "type": "bytes32"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "internalType": "struct IRollupCore.Staker", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "stakerNum", "type": "uint64"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getValidators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "inbox", "outputs": [{"internalType": "contract IInboxBase", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_stakeToken", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "isPending", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "isStaked", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "validator", "type": "address"}], "name": "isValidator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfirmed", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "latestStakedAssertion", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserStakeEscrow", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumAssertionPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"internalType": "address", "name": "_withdrawalAddress", "type": "address"}], "name": "newStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}, {"internalType": "address", "name": "_withdrawalAddress", "type": "address"}], "name": "newStakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "newStakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "outbox", "outputs": [{"internalType": "contract IOutbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "target", "type": "uint256"}], "name": "reduceDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "removeWhitelistAfterFork", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "removeWhitelistAfterValidatorAfk", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "returnOldDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker<PERSON><PERSON><PERSON>", "type": "address"}], "name": "returnOldDepositFor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollupDeploymentBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rollupEventInbox", "outputs": [{"internalType": "contract IRollupEventInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "stakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakerCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalWithdrawableFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "state", "type": "tuple"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "validateAssertionHash", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "name": "validateConfig", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorAfkBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorWalletCreator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validator<PERSON><PERSON><PERSON>stDisabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "wasmModuleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawStakerFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "withdrawableFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "withdrawalAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}