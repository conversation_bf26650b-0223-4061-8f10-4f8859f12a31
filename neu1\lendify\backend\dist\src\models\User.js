"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const UserSchema = new mongoose_1.Schema({
    walletAddress: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    email: {
        type: String,
        sparse: true,
        lowercase: true,
        match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    username: {
        type: String,
        sparse: true,
        minlength: 3,
        maxlength: 30,
        match: /^[a-zA-Z0-9_-]+$/
    },
    bio: { type: String, maxlength: 500 },
    avatar: String,
    profileCompleted: { type: Boolean, default: false },
    isVerified: { type: Boolean, default: false },
    loginCount: { type: Number, default: 0 },
    lastLogin: Date,
    tempAuth: {
        nonce: String,
        expiresAt: Date,
        attempts: { type: Number, default: 0 }
    },
    refreshTokens: [{
            token: { type: String, required: true },
            createdAt: { type: Date, default: Date.now },
            expiresAt: { type: Date, required: true },
            deviceInfo: String
        }],
    profile: {
        displayName: { type: String, maxlength: 50 },
        bio: { type: String, maxlength: 500 },
        avatar: String,
        website: String,
        twitter: String,
        discord: String
    },
    reputation: {
        score: { type: Number, default: 0, min: 0 },
        level: { type: String, default: 'Newcomer' },
        totalRentals: { type: Number, default: 0, min: 0 },
        successfulRentals: { type: Number, default: 0, min: 0 },
        totalListings: { type: Number, default: 0, min: 0 },
        activeListings: { type: Number, default: 0, min: 0 },
        totalEarnings: { type: Number, default: 0, min: 0 },
        totalSpent: { type: Number, default: 0, min: 0 },
        disputes: { type: Number, default: 0, min: 0 },
        averageRating: { type: Number, default: 0, min: 0, max: 5 },
        totalRatings: { type: Number, default: 0, min: 0 },
        badges: [{ type: String }]
    },
    preferences: {
        notifications: {
            email: { type: Boolean, default: true },
            push: { type: Boolean, default: true },
            sms: { type: Boolean, default: false },
            categories: {
                rental: { type: Boolean, default: true },
                lending: { type: Boolean, default: true },
                payments: { type: Boolean, default: true },
                disputes: { type: Boolean, default: true },
                marketing: { type: Boolean, default: false },
                system: { type: Boolean, default: true }
            }
        },
        privacy: {
            showRealName: { type: Boolean, default: false },
            showEmail: { type: Boolean, default: false },
            showStats: { type: Boolean, default: true }
        }
    },
    verification: {
        isEmailVerified: { type: Boolean, default: false },
        isPhoneVerified: { type: Boolean, default: false },
        isKYCVerified: { type: Boolean, default: false },
        kycLevel: { type: String, enum: ['none', 'basic', 'advanced'], default: 'none' }
    },
    security: {
        twoFactorEnabled: { type: Boolean, default: false },
        loginAttempts: { type: Number, default: 0 },
        lockoutUntil: Date,
        lastLogin: { type: Date, default: Date.now },
        lastLoginIP: String
    },
    role: { type: String, enum: ['user', 'moderator', 'admin'], default: 'user' },
    status: { type: String, enum: ['active', 'suspended', 'banned'], default: 'active' }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
UserSchema.index({ walletAddress: 1 });
UserSchema.index({ email: 1 }, { sparse: true });
UserSchema.index({ username: 1 }, { sparse: true });
UserSchema.index({ 'reputation.score': -1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ status: 1, role: 1 });
UserSchema.virtual('reputation.calculatedLevel').get(function () {
    const score = this.reputation.score;
    if (score >= 10000)
        return 'Legendary';
    if (score >= 5000)
        return 'Master';
    if (score >= 2500)
        return 'Expert';
    if (score >= 1000)
        return 'Professional';
    if (score >= 500)
        return 'Intermediate';
    if (score >= 100)
        return 'Novice';
    return 'Newcomer';
});
UserSchema.methods.updateReputation = function (data) {
    if (data.rentalsCompleted) {
        this.reputation.totalRentals += data.rentalsCompleted;
        this.reputation.successfulRentals += data.rentalsCompleted;
        this.reputation.score += data.rentalsCompleted * 10;
    }
    if (data.earningsAdded) {
        this.reputation.totalEarnings += data.earningsAdded;
        this.reputation.score += Math.floor(data.earningsAdded * 100);
    }
    if (data.spentAdded) {
        this.reputation.totalSpent += data.spentAdded;
        this.reputation.score += Math.floor(data.spentAdded * 50);
    }
    if (data.rating) {
        const newTotal = (this.reputation.averageRating * this.reputation.totalRatings) + data.rating;
        this.reputation.totalRatings += 1;
        this.reputation.averageRating = newTotal / this.reputation.totalRatings;
        this.reputation.score += data.rating * 5;
    }
    if (data.dispute) {
        this.reputation.disputes += 1;
        this.reputation.score = Math.max(0, this.reputation.score - 50);
    }
    this.reputation.level = this.reputation.calculatedLevel;
};
UserSchema.methods.isLocked = function () {
    return !!(this.security.lockoutUntil && this.security.lockoutUntil > Date.now());
};
UserSchema.methods.incLoginAttempts = function () {
    if (this.security.lockoutUntil && this.security.lockoutUntil < Date.now()) {
        return this.updateOne({
            $unset: { 'security.lockoutUntil': 1, 'security.loginAttempts': 1 }
        });
    }
    const updates = { $inc: { 'security.loginAttempts': 1 } };
    if (this.security.loginAttempts + 1 >= 5 && !this.isLocked()) {
        updates.$set = { 'security.lockoutUntil': Date.now() + 2 * 60 * 60 * 1000 };
    }
    return this.updateOne(updates);
};
exports.User = mongoose_1.default.model('User', UserSchema);
