{"_format": "hh-sol-artifact-1", "contractName": "ArbWasmCache", "sourceName": "src/precompiles/ArbWasmCache.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "manager", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}, {"indexed": false, "internalType": "bool", "name": "cached", "type": "bool"}], "name": "UpdateProgramCache", "type": "event"}, {"inputs": [], "name": "allCacheManagers", "outputs": [{"internalType": "address[]", "name": "managers", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "cacheCodehash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "cacheProgram", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "codehashIsCached", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "<PERSON><PERSON>h", "type": "bytes32"}], "name": "evict<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address"}], "name": "isCacheManager", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}