{"_format": "hh-sol-artifact-1", "contractName": "ERC20Outbox", "sourceName": "src/bridge/ERC20Outbox.sol", "abi": [{"inputs": [], "name": "AlreadyInit", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "AlreadySpent", "type": "error"}, {"inputs": [], "name": "BadPostUpgradeInit", "type": "error"}, {"inputs": [], "name": "BridgeCallFailed", "type": "error"}, {"inputs": [], "name": "HadZeroInit", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "actualLength", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "MerkleProofTooLong", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}], "name": "NotRollup", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "uint256", "name": "maxIndex", "type": "uint256"}], "name": "PathNotMinimal", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "proofLength", "type": "uint256"}], "name": "ProofTooLong", "type": "error"}, {"inputs": [], "name": "RollupNotChanged", "type": "error"}, {"inputs": [], "name": "SimulationOnlyEntrypoint", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}], "name": "UnknownRoot", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "address", "name": "l2Sender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "zero", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "transactionIndex", "type": "uint256"}], "name": "OutBoxTransactionExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "outputRoot", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "l2BlockHash", "type": "bytes32"}], "name": "SendRootUpdated", "type": "event"}, {"inputs": [], "name": "OUTBOX_VERSION", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "l2Sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "l2Block", "type": "uint256"}, {"internalType": "uint256", "name": "l1Block", "type": "uint256"}, {"internalType": "uint256", "name": "l2Timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "calculateItemHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}, {"internalType": "uint256", "name": "path", "type": "uint256"}, {"internalType": "bytes32", "name": "item", "type": "bytes32"}], "name": "calculateMerkleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}, {"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "l2Sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "l2Block", "type": "uint256"}, {"internalType": "uint256", "name": "l1Block", "type": "uint256"}, {"internalType": "uint256", "name": "l2Timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeTransaction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "l2Sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "l2Block", "type": "uint256"}, {"internalType": "uint256", "name": "l1Block", "type": "uint256"}, {"internalType": "uint256", "name": "l2Timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeTransactionSimulation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IBridge", "name": "_bridge", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "isSpent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1BatchNum", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "l2ToL1Block", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1EthBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1OutputId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1Sender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1Timestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1WithdrawalAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "postUpgradeInit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "roots", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "spent", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "updateRollupAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}, {"internalType": "bytes32", "name": "l2BlockHash", "type": "bytes32"}], "name": "updateSendRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}