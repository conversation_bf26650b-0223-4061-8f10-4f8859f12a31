{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI1D,MAAM,WAAW,oBAAqB,SAAQ,OAAO;IACnD,IAAI,CAAC,EAAE;QACL,EAAE,EAAE,MAAM,CAAC;QACX,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,WAAW,CAAC;QACrC,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,eAAe,EAAE,OAAO,CAAC;QACzB,SAAS,EAAE,IAAI,CAAC;QAChB,SAAS,EAAE,IAAI,CAAC;KACjB,CAAC;IACF,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;CACb;AAED,MAAM,WAAW,4BAA4B;IAC3C,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,cAAM,cAAc;IAClB,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,aAAa,CAAS;;IAQvB,YAAY,GAAU,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC,CAyDvG;IAGK,YAAY,GAAU,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC,CAuBvG;IAGK,WAAW,GAAI,OAAO,MAAM,EAAE,MAC3B,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAmB3E;IAGK,YAAY,GAAI,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAExF;IAGK,gBAAgB,GAAI,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAE5F;IAGK,iBAAiB,GAAI,eAAe,MAAM,MACvC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAoB3E;IAGK,oBAAoB,GAAI,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAkBhG;IAGK,gBAAgB,GAAU,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC,CAsD3G;IAGK,wBAAwB,GAAI,WAAW,MAAM,MAC1C,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI,CAkB3E;IAGK,aAAa,GAAI,MAAM;QAAE,EAAE,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,KAAG,MAAM,CAUlF;IAGK,oBAAoB,GAAI,QAAQ,MAAM,KAAG,MAAM,CAMpD;IAGK,YAAY,GAAU,cAAc,MAAM,KAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAqBxE;IAGK,eAAe,GAAU,SAAS,MAAM,EAAE,WAAW,MAAM,EAAE,iBAAiB,MAAM,KAAG,OAAO,CAAC,OAAO,CAAC,CAQ5G;IAGK,kBAAkB,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC,CAmFhG;IAGK,mBAAmB,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,KAAG,IAAI,CAW9D;IAGF,OAAO,CAAC,YAAY,CAoBlB;YAGY,WAAW;YAcX,gBAAgB;YAKhB,UAAU;YAcV,eAAe;CAI9B;AAGD,QAAA,MAAM,cAAc,gBAAuB,CAAC;AAG5C,OAAO,EAAE,cAAc,EAAE,CAAC;AAE1B,MAAM,WAAW,uBAAuB;IACtC,YAAY,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9F,YAAY,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9F,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IACzG,YAAY,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IACrF,gBAAgB,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IACzF,iBAAiB,EAAE,CAAC,aAAa,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IACrH,oBAAoB,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IAC7F,gBAAgB,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAClG,wBAAwB,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC;IACxH,aAAa,EAAE,CAAC,IAAI,EAAE;QAAE,EAAE,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,KAAK,MAAM,CAAC;IAC/E,oBAAoB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;IACjD,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAC/D,eAAe,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IACnG,kBAAkB,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACvF,mBAAmB,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,KAAK,IAAI,CAAC;CAC5D"}