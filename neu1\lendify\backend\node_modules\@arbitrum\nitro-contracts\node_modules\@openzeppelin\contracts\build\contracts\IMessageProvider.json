{"_format": "hh-sol-artifact-1", "contractName": "IMessageProvider", "sourceName": "contracts/vendor/arbitrum/IMessageProvider.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "InboxMessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}], "name": "InboxMessageDeliveredFromOrigin", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}