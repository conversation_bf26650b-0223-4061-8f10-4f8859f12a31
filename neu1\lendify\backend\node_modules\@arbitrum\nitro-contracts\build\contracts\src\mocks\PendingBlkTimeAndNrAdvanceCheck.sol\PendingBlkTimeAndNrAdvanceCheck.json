{"_format": "hh-sol-artifact-1", "contractName": "PendingBlkTimeAndNrAdvanceCheck", "sourceName": "src/mocks/PendingBlkTimeAndNrAdvanceCheck.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "bytes32", "name": "expected", "type": "bytes32"}], "name": "checkArbBlockHashReturnsLatest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "isAdvancing", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}