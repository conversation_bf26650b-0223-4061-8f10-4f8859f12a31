{"version": 3, "file": "Loan.d.ts", "sourceRoot": "", "sources": ["../../../src/models/Loan.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAEtD,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,GAAG,EAAE;QACH,eAAe,EAAE,MAAM,CAAC;QACxB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC9B,cAAc,EAAE,MAAM,CAAC;QACvB,WAAW,CAAC,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,IAAI,EAAE;QACJ,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,YAAY,EAAE,MAAM,CAAC;QACrB,QAAQ,EAAE,MAAM,CAAC;QACjB,GAAG,EAAE,MAAM,CAAC;QACZ,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,aAAa,CAAC,EAAE,IAAI,CAAC;KACtB,CAAC;IACF,SAAS,EAAE;QACT,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,MAAM,CAAC;QACnB,eAAe,EAAE,MAAM,CAAC;QACxB,QAAQ,EAAE,KAAK,CAAC;YACd,MAAM,EAAE,MAAM,CAAC;YACf,SAAS,EAAE,IAAI,CAAC;YAChB,MAAM,EAAE,MAAM,CAAC;YACf,IAAI,EAAE,SAAS,GAAG,MAAM,GAAG,eAAe,CAAC;SAC5C,CAAC,CAAC;QACH,WAAW,EAAE,MAAM,CAAC;QACpB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,UAAU,EAAE;QACV,UAAU,EAAE,OAAO,CAAC;QACpB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,gBAAgB,EAAE,WAAW,GAAG,aAAa,CAAC;QAC9C,oBAAoB,EAAE,MAAM,CAAC;QAC7B,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,aAAa,CAAC,EAAE,IAAI,CAAC;KACtB,CAAC;IACF,UAAU,EAAE;QACV,YAAY,EAAE,MAAM,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAC3B,WAAW,CAAC,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,MAAM,EAAE,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,WAAW,CAAC;IAChG,QAAQ,EAAE,KAAK,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,IAAI,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC,CAAC;IACH,KAAK,EAAE;QACL,eAAe,EAAE,OAAO,CAAC;QACzB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,cAAc,EAAE,OAAO,CAAC;QACxB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,aAAa,EAAE,MAAM,CAAC;QACtB,YAAY,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,UAAU,EAAE,KAAK,CAAC;QAChB,eAAe,EAAE,IAAI,CAAC;QACtB,UAAU,EAAE,IAAI,CAAC;QACjB,cAAc,EAAE,MAAM,CAAC;QACvB,YAAY,EAAE,MAAM,CAAC;QACrB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,SAAS,EAAE,IAAI,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,CAAC,CAAC;IACH,IAAI,EAAE;QACJ,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;QACrC,WAAW,EAAE,MAAM,EAAE,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC;QACxB,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,OAAO,CAAC,EAAE;QACR,UAAU,EAAE,OAAO,CAAC;QACpB,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,UAAU,GAAG,QAAQ,CAAC;QACjC,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,KAAK,CAAC;YACd,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,UAAU,CAAC;YACpC,OAAO,EAAE,MAAM,CAAC;YAChB,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC;YAClC,SAAS,EAAE,IAAI,CAAC;SACjB,CAAC,CAAC;QACH,UAAU,CAAC,EAAE;YACX,QAAQ,EAAE,gBAAgB,GAAG,cAAc,GAAG,gBAAgB,GAAG,UAAU,CAAC;YAC5E,SAAS,EAAE,MAAM,CAAC;YAClB,cAAc,CAAC,EAAE,MAAM,CAAC;YACxB,QAAQ,CAAC,EAAE,GAAG,CAAC;YACf,UAAU,EAAE,MAAM,CAAC;YACnB,UAAU,EAAE,IAAI,CAAC;SAClB,CAAC;KACH,CAAC;IACF,SAAS,EAAE;QACT,SAAS,EAAE,MAAM,CAAC;QAClB,KAAK,EAAE,MAAM,CAAC;QACd,eAAe,EAAE,MAAM,CAAC;KACzB,CAAC;IACF,QAAQ,EAAE;QACR,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,QAAQ,GAAG,SAAS,CAAC;KAClC,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AA6aD,eAAO,MAAM,IAAI;;;;OAA4C,CAAC"}