{"_format": "hh-sol-artifact-1", "contractName": "OneStepProver0", "sourceName": "src/osp/OneStepProver0.sol", "abi": [{"inputs": [{"components": [{"internalType": "uint256", "name": "maxInboxMessagesRead", "type": "uint256"}, {"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "bytes32", "name": "initialWasmModuleRoot", "type": "bytes32"}], "internalType": "struct ExecutionContext", "name": "", "type": "tuple"}, {"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "startMach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "startMod", "type": "tuple"}, {"components": [{"internalType": "uint16", "name": "opcode", "type": "uint16"}, {"internalType": "uint256", "name": "argumentData", "type": "uint256"}], "internalType": "struct Instruction", "name": "inst", "type": "tuple"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "executeOneStep", "outputs": [{"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "mach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "mod", "type": "tuple"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}