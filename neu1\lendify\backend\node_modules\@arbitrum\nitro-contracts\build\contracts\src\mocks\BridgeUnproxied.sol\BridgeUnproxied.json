{"_format": "hh-sol-artifact-1", "contractName": "BridgeUnproxied", "sourceName": "src/mocks/BridgeUnproxied.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "stored", "type": "uint256"}, {"internalType": "uint256", "name": "received", "type": "uint256"}], "name": "BadSequencerMessageNumber", "type": "error"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "InvalidOutboxSet", "type": "error"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "NotContract", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotDelayedInbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotOutbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "NotRollupOrOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotSequencerInbox", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "BridgeCallTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "InboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageIndex", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "beforeInboxAcc", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "kind", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "baseFeeL1", "type": "uint256"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "MessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "OutboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rollup", "type": "address"}], "name": "RollupUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxUpdated", "type": "event"}, {"inputs": [], "name": "DUMMYVAR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptFundsFromOldBridge", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "activeOutbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedDelayedInboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}], "name": "allowedDelayedInboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedOutboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "allowedOutboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "delayedInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "delayedMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "enqueueDelayedMessage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "afterDelayedMessagesRead", "type": "uint256"}, {"internalType": "uint256", "name": "prevMessageCount", "type": "uint256"}, {"internalType": "uint256", "name": "newMessageCount", "type": "uint256"}], "name": "enqueueSequencerMessage", "outputs": [{"internalType": "uint256", "name": "seqMessageIndex", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "delayedAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "acc", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "rollup_", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sequencerInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerReportedSubMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newMsgCount", "type": "uint256"}], "name": "setSequencerReportedSubMessageCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "submitBatchSpendingReport", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "_rollup", "type": "address"}], "name": "updateRollupAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}