{"_format": "hh-sol-artifact-1", "contractName": "ERC2771ContextUpgradeable", "sourceName": "contracts/metatx/ERC2771ContextUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}