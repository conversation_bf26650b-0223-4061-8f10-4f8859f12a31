{"_format": "hh-sol-artifact-1", "contractName": "IEdgeStakingPoolCreator", "sourceName": "src/assertionStakingPool/interfaces/IEdgeStakingPoolCreator.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "NewEdgeStakingPoolCreated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "createPool", "outputs": [{"internalType": "contract IEdgeStakingPool", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "getPool", "outputs": [{"internalType": "contract IEdgeStakingPool", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}