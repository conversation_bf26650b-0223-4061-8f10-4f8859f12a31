{"_format": "hh-sol-artifact-1", "contractName": "ERC1820ImplementerUpgradeable", "sourceName": "contracts/utils/introspection/ERC1820ImplementerUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "interfaceHash", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "canImplementInterfaceForAddress", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b50610116806100206000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c8063249cb3fa14602d575b600080fd5b603c603836600460a6565b604e565b60405190815260200160405180910390f35b60008281526001602090815260408083206001600160a01b038516845290915281205460ff16607d576000609f565b7fa2ef4600d742022d532d4747cb3547474667d6f13804902513b2ec01c848f4b45b9392505050565b6000806040838503121560b857600080fd5b8235915060208301356001600160a01b038116811460d557600080fd5b80915050925092905056fea264697066735822122039eb065111c36314227f93b9816e677110ed3d081f28cbaf894e0fd444dd882364736f6c63430008090033", "deployedBytecode": "0x6080604052348015600f57600080fd5b506004361060285760003560e01c8063249cb3fa14602d575b600080fd5b603c603836600460a6565b604e565b60405190815260200160405180910390f35b60008281526001602090815260408083206001600160a01b038516845290915281205460ff16607d576000609f565b7fa2ef4600d742022d532d4747cb3547474667d6f13804902513b2ec01c848f4b45b9392505050565b6000806040838503121560b857600080fd5b8235915060208301356001600160a01b038116811460d557600080fd5b80915050925092905056fea264697066735822122039eb065111c36314227f93b9816e677110ed3d081f28cbaf894e0fd444dd882364736f6c63430008090033", "linkReferences": {}, "deployedLinkReferences": {}}