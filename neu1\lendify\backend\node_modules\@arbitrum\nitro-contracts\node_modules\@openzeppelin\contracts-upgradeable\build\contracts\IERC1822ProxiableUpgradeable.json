{"_format": "hh-sol-artifact-1", "contractName": "IERC1822ProxiableUpgradeable", "sourceName": "contracts/interfaces/draft-IERC1822Upgradeable.sol", "abi": [{"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}