{"_format": "hh-sol-artifact-1", "contractName": "IAMBUpgradeable", "sourceName": "contracts/vendor/amb/IAMBUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "executor", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "messageId", "type": "bytes32"}, {"indexed": false, "internalType": "bool", "name": "status", "type": "bool"}], "name": "AffirmationCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "executor", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "messageId", "type": "bytes32"}, {"indexed": false, "internalType": "bool", "name": "status", "type": "bool"}], "name": "RelayedMessage", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "messageId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes", "name": "encodedData", "type": "bytes"}], "name": "UserRequestForAffirmation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "messageId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes", "name": "encodedData", "type": "bytes"}], "name": "UserRequestForSignature", "type": "event"}, {"inputs": [], "name": "destinationChainId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_messageId", "type": "bytes32"}], "name": "failedMessageDataHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_messageId", "type": "bytes32"}], "name": "failedMessageReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_messageId", "type": "bytes32"}], "name": "failedMessageSender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxGasPerTx", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_messageId", "type": "bytes32"}], "name": "messageCallStatus", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "messageId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "messageSender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "messageSourceChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "uint256", "name": "_gas", "type": "uint256"}], "name": "requireToConfirmMessage", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "uint256", "name": "_gas", "type": "uint256"}], "name": "requireToPassMessage", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sourceChainId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "transactionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}