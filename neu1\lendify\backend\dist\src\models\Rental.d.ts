import mongoose, { Document, Schema } from 'mongoose';
export interface IRental extends Document {
    _id: string;
    nft: {
        contractAddress: string;
        tokenId: string;
        chainId: number;
        nftId?: Schema.Types.ObjectId;
    };
    owner: string;
    renter?: string;
    pricing: {
        pricePerDay: number;
        currency: string;
        totalPrice: number;
        collateral: number;
        platformFee: number;
        netAmount: number;
    };
    terms: {
        duration: number;
        startDate?: Date;
        endDate?: Date;
        autoRenewal: boolean;
        cancellationPolicy: 'flexible' | 'moderate' | 'strict';
        termsAndConditions?: string;
    };
    blockchain: {
        listingTxHash?: string;
        rentalTxHash?: string;
        completionTxHash?: string;
        blockNumber?: number;
        contractAddress: string;
        listingId?: number;
    };
    payment: {
        method: 'crypto' | 'fiat';
        status: 'pending' | 'completed' | 'failed' | 'refunded';
        paymentDate?: Date;
        refundDate?: Date;
        paymentTxHash?: string;
        refundTxHash?: string;
    };
    status: 'draft' | 'listed' | 'rented' | 'active' | 'completed' | 'expired' | 'cancelled' | 'disputed';
    timeline: Array<{
        status: string;
        timestamp: Date;
        txHash?: string;
        notes?: string;
    }>;
    extensions: Array<{
        originalEndDate: Date;
        newEndDate: Date;
        additionalDays: number;
        additionalCost: number;
        timestamp: Date;
        txHash?: string;
    }>;
    ratings: {
        renterRating?: {
            score: number;
            review?: string;
            timestamp: Date;
        };
        ownerRating?: {
            score: number;
            review?: string;
            timestamp: Date;
        };
    };
    dispute?: {
        isDisputed: boolean;
        reason: string;
        initiator: 'owner' | 'renter';
        description: string;
        evidence: Array<{
            type: 'text' | 'image' | 'document';
            content: string;
            uploadedBy: 'owner' | 'renter';
            timestamp: Date;
        }>;
        resolution?: {
            decision: 'favor_owner' | 'favor_renter' | 'split' | 'no_fault';
            reasoning: string;
            compensationOwner?: number;
            compensationRenter?: number;
            resolvedBy: string;
            resolvedAt: Date;
        };
    };
    metadata: {
        category?: string;
        tags: string[];
        notes?: string;
        visibility: 'public' | 'private' | 'unlisted';
    };
    analytics: {
        views: number;
        inquiries: number;
        favorited: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare const Rental: mongoose.Model<IRental, {}, {}, {}, mongoose.Document<unknown, {}, IRental, {}, {}> & IRental & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Rental.d.ts.map