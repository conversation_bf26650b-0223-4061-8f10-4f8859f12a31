{"_format": "hh-sol-artifact-1", "contractName": "DoubleEndedQueueUpgradeable", "sourceName": "contracts/utils/structs/DoubleEndedQueueUpgradeable.sol", "abi": [{"inputs": [], "name": "Empty", "type": "error"}, {"inputs": [], "name": "OutOfBounds", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122076a621640f756d66f7010916439d85a2f76c7dcebb2065bca1ae6d0e3142193a64736f6c63430008090033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122076a621640f756d66f7010916439d85a2f76c7dcebb2065bca1ae6d0e3142193a64736f6c63430008090033", "linkReferences": {}, "deployedLinkReferences": {}}