{"_format": "hh-sol-artifact-1", "contractName": "ERC777PresetFixedSupply", "sourceName": "contracts/token/ERC777/presets/ERC777PresetFixedSupply.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address[]", "name": "defaultOperators", "type": "address[]"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "AuthorizedOperator", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "Burned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "Minted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "RevokedOperator", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "<PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "authorizeOperator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "defaultOperators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "granularity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "isOperatorFor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "operatorBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "operatorSend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "revokeOperator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "send", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}