{"_format": "hh-sol-artifact-1", "contractName": "SimpleOneStepProofEntry", "sourceName": "src/mocks/SimpleOneStepProofEntry.sol", "abi": [{"inputs": [], "name": "STEPS_PER_BATCH", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "execState", "type": "tuple"}], "name": "getMachineHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}], "name": "getStartMachineHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "maxInboxMessagesRead", "type": "uint256"}, {"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "bytes32", "name": "initialWasmModuleRoot", "type": "bytes32"}], "internalType": "struct ExecutionContext", "name": "execCtx", "type": "tuple"}, {"internalType": "uint256", "name": "step", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeHash", "type": "bytes32"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "proveOneStep", "outputs": [{"internalType": "bytes32", "name": "afterHash", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}