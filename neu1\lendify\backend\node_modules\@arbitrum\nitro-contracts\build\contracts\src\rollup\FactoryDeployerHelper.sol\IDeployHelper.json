{"_format": "hh-sol-artifact-1", "contractName": "IDeployHelper", "sourceName": "src/rollup/FactoryDeployerHelper.sol", "abi": [{"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "uint256", "name": "maxFeePer<PERSON>as", "type": "uint256"}], "name": "getDeploymentTotalCost", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_inbox", "type": "address"}, {"internalType": "address", "name": "_nativeToken", "type": "address"}, {"internalType": "uint256", "name": "_maxFeePerGas", "type": "uint256"}], "name": "perform", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}