{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "sourceName": "src/rollup/RollupAdminLogic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "anyTrustFastConfirmer", "type": "address"}], "name": "AnyTrustFastConfirmerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "sendRoot", "type": "bytes32"}], "name": "AssertionConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "indexed": false, "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"indexed": false, "internalType": "bytes32", "name": "afterInboxBatchAcc", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}], "name": "AssertionCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "AssertionForceConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "As<PERSON><PERSON><PERSON><PERSON><PERSON>Created", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newBaseStake", "type": "uint256"}], "name": "BaseStakeSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}], "name": "ChallengeManagerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "newConfirmPeriod", "type": "uint64"}], "name": "ConfirmPeriodBlocksSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "DelayedInboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}], "name": "InboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newLoserStakerEscrow", "type": "address"}], "name": "LoserStakeEscrowSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinimumAssertionPeriodSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "outbox", "type": "address"}], "name": "OldOutboxRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "outbox", "type": "address"}], "name": "OutboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint64", "name": "challengeIndex", "type": "uint64"}, {"indexed": false, "internalType": "address", "name": "asserter", "type": "address"}, {"indexed": false, "internalType": "address", "name": "challenger", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "challengedAsser<PERSON>", "type": "uint64"}], "name": "RollupChallengeStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "machineHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "RollupInitialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "staker", "type": "address[]"}], "name": "StakersForceRefunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "UpgradedSecondary", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserStakeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserWithdrawableFundsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "ValidatorAfkBlocksSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "_validator<PERSON><PERSON><PERSON>stDisabled", "type": "bool"}], "name": "ValidatorWhitelistDisabledSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "validators", "type": "address[]"}, {"indexed": false, "internalType": "bool[]", "name": "enabled", "type": "bool[]"}], "name": "ValidatorsSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "newWasmModuleRoot", "type": "bytes32"}], "name": "WasmModuleRootSet", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "_stakerMap", "outputs": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "bytes32", "name": "latestStakedAssertion", "type": "bytes32"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "amountStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "anyTrustFastConfirmer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseStake", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "chainId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "challengeGracePeriodBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "challenge<PERSON>anager", "outputs": [{"internalType": "contract IEdgeChallengeManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "confirmPeriodBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "confirmState", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "forceConfirmAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "forceCreateAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "staker", "type": "address[]"}], "name": "forceRefundStaker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "genesisAssertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertion", "outputs": [{"components": [{"internalType": "uint64", "name": "first<PERSON><PERSON><PERSON><PERSON>lock", "type": "uint64"}, {"internalType": "uint64", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint64"}, {"internalType": "uint64", "name": "createdAtBlock", "type": "uint64"}, {"internalType": "bool", "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "enum AssertionStatus", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "config<PERSON><PERSON>", "type": "bytes32"}], "internalType": "struct AssertionNode", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertionCreationBlockForLogLookup", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getFirstChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getSecondChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "get<PERSON><PERSON>", "outputs": [{"components": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "bytes32", "name": "latestStakedAssertion", "type": "bytes32"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "internalType": "struct IRollupCore.Staker", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "stakerNum", "type": "uint64"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getValidators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "inbox", "outputs": [{"internalType": "contract IInboxBase", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "address", "name": "stakeToken", "type": "address"}, {"internalType": "uint256", "name": "baseStake", "type": "uint256"}, {"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "loserStakeEscrow", "type": "address"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "string", "name": "chainConfig", "type": "string"}, {"internalType": "uint256", "name": "minimumAssertionPeriod", "type": "uint256"}, {"internalType": "uint64", "name": "validatorAfkBlocks", "type": "uint64"}, {"internalType": "uint256[]", "name": "miniStakeValues", "type": "uint256[]"}, {"components": [{"internalType": "uint256", "name": "delayBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "futureBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "delaySeconds", "type": "uint256"}, {"internalType": "uint256", "name": "futureSeconds", "type": "uint256"}], "internalType": "struct ISequencerInbox.MaxTimeVariation", "name": "sequencerInboxMaxTimeVariation", "type": "tuple"}, {"internalType": "uint256", "name": "layerZeroBlockEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroBigStepEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroSmallStepEdgeHeight", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "genesisAssertionState", "type": "tuple"}, {"internalType": "uint256", "name": "genesisInboxCount", "type": "uint256"}, {"internalType": "address", "name": "anyTrustFastConfirmer", "type": "address"}, {"internalType": "uint8", "name": "numBigStepLevel", "type": "uint8"}, {"internalType": "uint64", "name": "challengeGracePeriodBlocks", "type": "uint64"}, {"components": [{"internalType": "uint64", "name": "threshold", "type": "uint64"}, {"internalType": "uint64", "name": "max", "type": "uint64"}, {"internalType": "uint64", "name": "replenishRateInBasis", "type": "uint64"}], "internalType": "struct BufferConfig", "name": "bufferConfig", "type": "tuple"}], "internalType": "struct Config", "name": "config", "type": "tuple"}, {"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IEdgeChallengeManager", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "address", "name": "rollupAdminLogic", "type": "address"}, {"internalType": "contract IRollupUser", "name": "rollupUserLogic", "type": "address"}, {"internalType": "address", "name": "validatorWalletCreator", "type": "address"}], "internalType": "struct ContractDependencies", "name": "connectedContracts", "type": "tuple"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "isPending", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "isStaked", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "validator", "type": "address"}], "name": "isValidator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfirmed", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "latestStakedAssertion", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserStakeEscrow", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumAssertionPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "outbox", "outputs": [{"internalType": "contract IOutbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_outbox", "type": "address"}], "name": "removeOldOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "resume", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollupDeploymentBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rollupEventInbox", "outputs": [{"internalType": "contract IRollupEventInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_anyTrustFastConfirmer", "type": "address"}], "name": "setAnyTrustFastConfirmer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseStake", "type": "uint256"}], "name": "setBaseStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_challenge<PERSON>anager", "type": "address"}], "name": "setChallengeManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newConfirmPeriod", "type": "uint64"}], "name": "setConfirmPeriodBlocks", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_inbox", "type": "address"}, {"internalType": "bool", "name": "_enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IInboxBase", "name": "newInbox", "type": "address"}], "name": "setInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newLoserStakerEscrow", "type": "address"}], "name": "setLoserStakeEscrow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "setMinimumAssertionPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOutbox", "name": "_outbox", "type": "address"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_validator", "type": "address[]"}, {"internalType": "bool[]", "name": "_val", "type": "bool[]"}], "name": "setValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newAfkBlocks", "type": "uint64"}], "name": "setValidatorAfkBlocks", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_validator<PERSON><PERSON><PERSON>stDisabled", "type": "bool"}], "name": "setValidator<PERSON><PERSON><PERSON><PERSON>Disabled", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "newWasmModuleRoot", "type": "bytes32"}], "name": "setWasmModuleRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakerCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalWithdrawableFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeSecondaryTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeSecondaryToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "state", "type": "tuple"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "validateAssertionHash", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "name": "validateConfig", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorAfkBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorWalletCreator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validator<PERSON><PERSON><PERSON>stDisabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "wasmModuleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "withdrawableFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "withdrawalAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}