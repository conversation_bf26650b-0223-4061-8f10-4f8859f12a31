{"_format": "hh-sol-artifact-1", "contractName": "OneStepProverMemory", "sourceName": "src/osp/OneStepProverMemory.sol", "abi": [{"inputs": [{"components": [{"internalType": "uint256", "name": "maxInboxMessagesRead", "type": "uint256"}, {"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "bytes32", "name": "initialWasmModuleRoot", "type": "bytes32"}], "internalType": "struct ExecutionContext", "name": "", "type": "tuple"}, {"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "startMach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "startMod", "type": "tuple"}, {"components": [{"internalType": "uint16", "name": "opcode", "type": "uint16"}, {"internalType": "uint256", "name": "argumentData", "type": "uint256"}], "internalType": "struct Instruction", "name": "inst", "type": "tuple"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "executeOneStep", "outputs": [{"components": [{"internalType": "enum MachineStatus", "name": "status", "type": "uint8"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "valueStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "valueMultiStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value[]", "name": "inner", "type": "tuple[]"}], "internalType": "struct ValueArray", "name": "proved", "type": "tuple"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct ValueStack", "name": "internalStack", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "enum ValueType", "name": "valueType", "type": "uint8"}, {"internalType": "uint256", "name": "contents", "type": "uint256"}], "internalType": "struct Value", "name": "returnPc", "type": "tuple"}, {"internalType": "bytes32", "name": "localsMerkleRoot", "type": "bytes32"}, {"internalType": "uint32", "name": "callerModule", "type": "uint32"}, {"internalType": "uint32", "name": "callerModuleInternals", "type": "uint32"}], "internalType": "struct St<PERSON>e[]", "name": "proved", "type": "tuple[]"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct StackFrameWindow", "name": "frameStack", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "inactiveStackHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "remainingHash", "type": "bytes32"}], "internalType": "struct MultiStack", "name": "frameMultiStack", "type": "tuple"}, {"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "uint32", "name": "moduleIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionIdx", "type": "uint32"}, {"internalType": "uint32", "name": "functionPc", "type": "uint32"}, {"internalType": "bytes32", "name": "recoveryPc", "type": "bytes32"}, {"internalType": "bytes32", "name": "modulesRoot", "type": "bytes32"}], "internalType": "struct Machine", "name": "mach", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "globalsMerkleRoot", "type": "bytes32"}, {"components": [{"internalType": "uint64", "name": "size", "type": "uint64"}, {"internalType": "uint64", "name": "maxSize", "type": "uint64"}, {"internalType": "bytes32", "name": "merkleRoot", "type": "bytes32"}], "internalType": "struct ModuleMemory", "name": "moduleMemory", "type": "tuple"}, {"internalType": "bytes32", "name": "tablesMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "functionsMerkleRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "extraHash", "type": "bytes32"}, {"internalType": "uint32", "name": "internalsOffset", "type": "uint32"}], "internalType": "struct Module", "name": "mod", "type": "tuple"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}