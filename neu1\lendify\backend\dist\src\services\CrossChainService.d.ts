import { EventEmitter } from 'events';
export interface ChainInfo {
    chainId: number;
    name: string;
    symbol: string;
    rpcUrl: string;
    blockExplorerUrl: string;
    logoUrl: string;
    isTestnet: boolean;
    bridges: string[];
    supportedTokens: string[];
}
export interface BridgeTransaction {
    id: string;
    fromChain: number;
    toChain: number;
    fromAddress: string;
    toAddress: string;
    tokenContract: string;
    tokenId: string;
    amount: string;
    status: 'pending' | 'confirmed' | 'bridging' | 'completed' | 'failed';
    txHash?: string;
    bridgeTxHash?: string;
    createdAt: Date;
    completedAt?: Date;
    estimatedTime: number;
    fees: {
        gas: string;
        bridge: string;
        total: string;
    };
}
export interface CrossChainNFT {
    originalChain: number;
    currentChain: number;
    originalContract: string;
    wrappedContract?: string;
    tokenId: string;
    isBridged: boolean;
    bridgeHistory: BridgeTransaction[];
    metadata: any;
}
export interface BridgeConfig {
    name: string;
    contracts: {
        [chainId: number]: string;
    };
    supportedChains: number[];
    fees: {
        [chainId: number]: string;
    };
    estimatedTimes: {
        [route: string]: number;
    };
}
export declare class CrossChainService extends EventEmitter {
    private supportedChains;
    private bridgeConfigs;
    private bridgeTransactions;
    private chainStatus;
    constructor();
    initialize(): Promise<void>;
    private initializeSupportedChains;
    private initializeBridgeConfigs;
    private checkChainConnectivity;
    private initializeBridgeServices;
    private testBridgeConnection;
    private startMonitoring;
    getSupportedChains(): ChainInfo[];
    getChainInfo(chainId: number): ChainInfo | undefined;
    getAvailableBridges(fromChain: number, toChain: number): BridgeConfig[];
    getBridgeQuote(fromChain: number, toChain: number, tokenContract: string, tokenId: string, bridgeName?: string): Promise<{
        bridge: string;
        fees: {
            gas: string;
            bridge: string;
            total: string;
        };
        estimatedTime: number;
        route: string[];
    }[]>;
    private getBridgeQuoteForBridge;
    bridgeNFT(fromChain: number, toChain: number, tokenContract: string, tokenId: string, toAddress: string, bridgeName?: string): Promise<BridgeTransaction>;
    private initiateBridgeTransaction;
    private calculateBridgeFees;
    private estimateGasFee;
    getBridgeTransaction(transactionId: string): Promise<BridgeTransaction | undefined>;
    getBridgeTransactionsByUser(userAddress: string): Promise<BridgeTransaction[]>;
    getNFTBridgeHistory(tokenContract: string, tokenId: string): Promise<BridgeTransaction[]>;
    getCrossChainNFTInfo(tokenContract: string, tokenId: string): Promise<CrossChainNFT | null>;
    private getWrappedContract;
    private getNFTMetadata;
    private monitorBridgeTransactions;
    private checkBridgeTransactionStatus;
    getChainStatus(): {
        [chainId: number]: boolean;
    };
    isChainHealthy(chainId: number): boolean;
    getChainMetrics(chainId: number): Promise<{
        latency: number;
        blockHeight: number;
        gasPrice: string;
        isHealthy: boolean;
    } | null>;
    private getBlockNumber;
    private getGasPrice;
    private generateTransactionId;
    validateBridgeRoute(fromChain: number, toChain: number): Promise<boolean>;
    getBridgeStatistics(): {
        totalTransactions: number;
        completedTransactions: number;
        activeTransactions: number;
        failedTransactions: number;
        totalVolume: string;
        averageTime: number;
    };
    private calculateAverageBridgeTime;
    onBridgeTransactionUpdate(callback: (transaction: BridgeTransaction) => void): void;
    getBridgeConfigs(): {
        [name: string]: BridgeConfig;
    };
    estimateBridgeTime(fromChain: number, toChain: number, bridgeName?: string): Promise<number>;
}
//# sourceMappingURL=CrossChainService.d.ts.map