{"_format": "hh-sol-artifact-1", "contractName": "DoubleEndedQueue", "sourceName": "contracts/utils/structs/DoubleEndedQueue.sol", "abi": [{"inputs": [], "name": "Empty", "type": "error"}, {"inputs": [], "name": "OutOfBounds", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220a1f883749a60323cd05a4f6a4b0582f16eb5d58287457ceb8942b07688a344f264736f6c63430008090033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220a1f883749a60323cd05a4f6a4b0582f16eb5d58287457ceb8942b07688a344f264736f6c63430008090033", "linkReferences": {}, "deployedLinkReferences": {}}