{"_format": "hh-sol-artifact-1", "contractName": "IOldRollupAdmin", "sourceName": "src/rollup/BOLDUpgradeAction.sol", "abi": [{"inputs": [{"internalType": "address[]", "name": "stacker", "type": "address[]"}], "name": "forceRefundStaker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "resume", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}