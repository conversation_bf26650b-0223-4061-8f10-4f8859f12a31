{"_format": "hh-sol-artifact-1", "contractName": "EdgeChallengeManagerLib", "sourceName": "src/challengeV2/libraries/EdgeChallengeManagerLib.sol", "abi": [{"inputs": [], "name": "UNRIVALED", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x60c7610039600b82828239805160001a60731461002c57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c80633de356c3146038575b600080fd5b607f6040517f554e524956414c4544000000000000000000000000000000000000000000000060208201526029016040516020818303038152906040528051906020012081565b60405190815260200160405180910390f3fea2646970667358221220f816297240f958f2eaa83b71d7ec55f02124956c3710fd8205dc16fb583fe6ad64736f6c63430008110033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c80633de356c3146038575b600080fd5b607f6040517f554e524956414c4544000000000000000000000000000000000000000000000060208201526029016040516020818303038152906040528051906020012081565b60405190815260200160405180910390f3fea2646970667358221220f816297240f958f2eaa83b71d7ec55f02124956c3710fd8205dc16fb583fe6ad64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}