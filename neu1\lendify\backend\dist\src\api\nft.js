"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const NFT_1 = require("../models/NFT");
const User_1 = require("../models/User");
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({ success: true, service: 'NFT API', message: 'Service is healthy' });
});
router.get('/:chainId/:contractAddress/:tokenId', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        chainId: validation_1.commonSchemas.chainId,
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, contractAddress, tokenId } = req.params;
    const nft = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, parseInt(chainId));
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found'
        });
    }
    await nft.updateAnalytics({ views: 1 });
    res.json({
        success: true,
        data: nft
    });
}));
router.get('/available', (0, validation_1.validateRequest)({
    query: joi_1.default.object({
        chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).optional(),
        category: joi_1.default.string().optional(),
        minPrice: joi_1.default.number().min(0).optional(),
        maxPrice: joi_1.default.number().min(0).optional(),
        sortBy: joi_1.default.string().valid('price', 'rating', 'trending', 'recent').default('recent'),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, category, minPrice, maxPrice, sortBy, sortOrder, page, limit } = req.query;
    const filters = {};
    if (chainId)
        filters.chainId = chainId;
    if (category)
        filters['metadata.category'] = category;
    if (minPrice !== undefined || maxPrice !== undefined) {
        filters['rental.avgDailyPrice'] = {};
        if (minPrice !== undefined)
            filters['rental.avgDailyPrice'].$gte = minPrice;
        if (maxPrice !== undefined)
            filters['rental.avgDailyPrice'].$lte = maxPrice;
    }
    const sortOptions = {};
    switch (sortBy) {
        case 'price':
            sortOptions['rental.avgDailyPrice'] = sortOrder === 'asc' ? 1 : -1;
            break;
        case 'rating':
            sortOptions['rental.ratings.average'] = sortOrder === 'asc' ? 1 : -1;
            break;
        case 'trending':
            sortOptions['analytics.trendingScore'] = sortOrder === 'asc' ? 1 : -1;
            break;
        default:
            sortOptions.createdAt = sortOrder === 'asc' ? 1 : -1;
    }
    const skip = (page - 1) * limit;
    const [nfts, total] = await Promise.all([
        NFT_1.NFT.findAvailableForRent(filters)
            .sort(sortOptions)
            .skip(skip)
            .limit(limit)
            .populate('rental.currentListing'),
        NFT_1.NFT.countDocuments({ 'rental.isAvailable': true, status: 'active', ...filters })
    ]);
    res.json({
        success: true,
        data: {
            nfts,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));
router.get('/trending', (0, validation_1.validateRequest)({
    query: joi_1.default.object({
        chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).optional(),
        timeframe: joi_1.default.string().valid('1h', '24h', '7d', '30d').default('24h'),
        limit: joi_1.default.number().integer().min(1).max(50).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, timeframe, limit } = req.query;
    const filters = {};
    if (chainId)
        filters.chainId = chainId;
    const nfts = await NFT_1.NFT.findTrending(limit)
        .find(filters)
        .populate('rental.currentListing');
    res.json({
        success: true,
        data: nfts
    });
}));
router.get('/search', (0, validation_1.validateRequest)({
    query: joi_1.default.object({
        q: joi_1.default.string().required().min(1),
        chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).optional(),
        category: joi_1.default.string().optional(),
        availableOnly: joi_1.default.boolean().default(false),
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(50).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { q, chainId, category, availableOnly, page, limit } = req.query;
    const filters = {
        $text: { $search: q },
        status: 'active'
    };
    if (chainId)
        filters.chainId = chainId;
    if (category)
        filters['metadata.category'] = category;
    if (availableOnly)
        filters['rental.isAvailable'] = true;
    const skip = (page - 1) * limit;
    const [nfts, total] = await Promise.all([
        NFT_1.NFT.find(filters)
            .sort({ score: { $meta: 'textScore' } })
            .skip(skip)
            .limit(limit)
            .populate('rental.currentListing'),
        NFT_1.NFT.countDocuments(filters)
    ]);
    res.json({
        success: true,
        data: {
            nfts,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));
router.get('/owner/:ownerAddress', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        ownerAddress: validation_1.commonSchemas.ethereumAddress
    }),
    query: joi_1.default.object({
        chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).optional(),
        status: joi_1.default.string().valid('active', 'rented', 'listed').optional(),
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { ownerAddress } = req.params;
    const { chainId, status, page, limit } = req.query;
    const filters = {
        owner: ownerAddress.toLowerCase(),
        status: 'active'
    };
    if (chainId)
        filters.chainId = chainId;
    if (status === 'rented')
        filters.currentUser = { $ne: null };
    if (status === 'listed')
        filters['rental.isAvailable'] = true;
    const skip = (page - 1) * limit;
    const [nfts, total] = await Promise.all([
        NFT_1.NFT.find(filters)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .populate('rental.currentListing'),
        NFT_1.NFT.countDocuments(filters)
    ]);
    res.json({
        success: true,
        data: {
            nfts,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));
router.post('/', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId,
        chainId: validation_1.commonSchemas.chainId,
        metadata: joi_1.default.object({
            name: joi_1.default.string().optional(),
            description: joi_1.default.string().optional(),
            image: joi_1.default.string().uri().optional(),
            animationUrl: joi_1.default.string().uri().optional(),
            externalUrl: joi_1.default.string().uri().optional(),
            attributes: joi_1.default.array().items(joi_1.default.object({
                traitType: joi_1.default.string().required(),
                value: joi_1.default.any().required(),
                displayType: joi_1.default.string().optional(),
                maxValue: joi_1.default.number().optional()
            })).default([])
        }).required(),
        collection: joi_1.default.object({
            name: joi_1.default.string().optional(),
            symbol: joi_1.default.string().optional(),
            floorPrice: joi_1.default.number().min(0).optional(),
            totalSupply: joi_1.default.number().min(0).optional(),
            verified: joi_1.default.boolean().default(false)
        }).optional(),
        estimatedValue: joi_1.default.number().min(0).required()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { contractAddress, tokenId, chainId, metadata, collection, estimatedValue } = req.body;
    const owner = req.user.address;
    const existingNFT = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, chainId);
    if (existingNFT) {
        return res.status(409).json({
            success: false,
            error: 'NFT already registered'
        });
    }
    const nft = new NFT_1.NFT({
        contractAddress,
        tokenId,
        chainId,
        owner,
        metadata,
        collection,
        pricing: {
            estimatedValue,
            priceHistory: [{
                    price: estimatedValue,
                    currency: 'ETH',
                    timestamp: new Date(),
                    source: 'listing'
                }]
        },
        crossChain: {
            originalChain: chainId,
            bridgeHistory: []
        },
        analytics: {
            views: 0,
            favorites: 0,
            shares: 0,
            demandScore: 0,
            rarityScore: 0,
            utilityScore: 0,
            trendingScore: 0,
            lastAnalyzed: new Date()
        }
    });
    await nft.save();
    await User_1.User.findOneAndUpdate({ walletAddress: owner }, { $inc: { 'reputation.totalListings': 1 } });
    res.status(201).json({
        success: true,
        message: 'NFT registered successfully',
        data: nft
    });
}));
router.put('/:chainId/:contractAddress/:tokenId', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        chainId: validation_1.commonSchemas.chainId,
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId
    }),
    body: joi_1.default.object({
        metadata: joi_1.default.object({
            name: joi_1.default.string().optional(),
            description: joi_1.default.string().optional(),
            image: joi_1.default.string().uri().optional(),
            animationUrl: joi_1.default.string().uri().optional(),
            externalUrl: joi_1.default.string().uri().optional(),
            attributes: joi_1.default.array().items(joi_1.default.object({
                traitType: joi_1.default.string().required(),
                value: joi_1.default.any().required(),
                displayType: joi_1.default.string().optional(),
                maxValue: joi_1.default.number().optional()
            })).optional()
        }).optional(),
        collection: joi_1.default.object({
            name: joi_1.default.string().optional(),
            symbol: joi_1.default.string().optional(),
            verified: joi_1.default.boolean().optional()
        }).optional(),
        estimatedValue: joi_1.default.number().min(0).optional()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, contractAddress, tokenId } = req.params;
    const { metadata, collection, estimatedValue } = req.body;
    const userAddress = req.user.address;
    const nft = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, parseInt(chainId));
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found'
        });
    }
    if (nft.owner !== userAddress && req.user.role !== 'admin') {
        return res.status(403).json({
            success: false,
            error: 'Not authorized to update this NFT'
        });
    }
    if (metadata) {
        nft.metadata = { ...nft.metadata, ...metadata };
    }
    if (collection) {
        nft.collection = { ...nft.collection, ...collection };
    }
    if (estimatedValue !== undefined) {
        nft.pricing.estimatedValue = estimatedValue;
        nft.addPriceHistory(estimatedValue, 'ETH', 'listing');
    }
    await nft.save();
    res.json({
        success: true,
        message: 'NFT updated successfully',
        data: nft
    });
}));
router.post('/:chainId/:contractAddress/:tokenId/favorite', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        chainId: validation_1.commonSchemas.chainId,
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, contractAddress, tokenId } = req.params;
    const nft = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, parseInt(chainId));
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found'
        });
    }
    await nft.updateAnalytics({ favorites: 1 });
    res.json({
        success: true,
        message: 'NFT added to favorites'
    });
}));
router.post('/:chainId/:contractAddress/:tokenId/rate', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        chainId: validation_1.commonSchemas.chainId,
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId
    }),
    body: joi_1.default.object({
        rating: joi_1.default.number().integer().min(1).max(5).required(),
        review: joi_1.default.string().max(1000).optional()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, contractAddress, tokenId } = req.params;
    const { rating } = req.body;
    const nft = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, parseInt(chainId));
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found'
        });
    }
    await nft.updateRating(rating);
    res.json({
        success: true,
        message: 'Rating submitted successfully',
        data: {
            newAverage: nft.rental.ratings.average,
            totalRatings: nft.rental.ratings.count
        }
    });
}));
router.get('/:chainId/:contractAddress/:tokenId/analytics', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        chainId: validation_1.commonSchemas.chainId,
        contractAddress: validation_1.commonSchemas.ethereumAddress,
        tokenId: validation_1.commonSchemas.tokenId
    }),
    query: joi_1.default.object({
        timeframe: joi_1.default.string().valid('7d', '30d', '90d').default('30d')
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, contractAddress, tokenId } = req.params;
    const { timeframe } = req.query;
    const nft = await NFT_1.NFT.findByIdentifier(contractAddress, tokenId, parseInt(chainId));
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found'
        });
    }
    const endDate = new Date();
    const startDate = new Date();
    switch (timeframe) {
        case '7d':
            startDate.setDate(endDate.getDate() - 7);
            break;
        case '30d':
            startDate.setDate(endDate.getDate() - 30);
            break;
        case '90d':
            startDate.setDate(endDate.getDate() - 90);
            break;
    }
    const priceHistory = nft.pricing.priceHistory.filter(p => p.timestamp >= startDate && p.timestamp <= endDate);
    res.json({
        success: true,
        data: {
            basic: {
                views: nft.analytics.views,
                favorites: nft.analytics.favorites,
                shares: nft.analytics.shares,
                demandScore: nft.analytics.demandScore,
                rarityScore: nft.analytics.rarityScore,
                utilityScore: nft.analytics.utilityScore,
                trendingScore: nft.analytics.trendingScore
            },
            rental: {
                totalRentals: nft.rental.totalRentals,
                successfulRentals: nft.rental.successfulRentals,
                totalRevenue: nft.rental.totalRevenue,
                avgDailyPrice: nft.rental.avgDailyPrice,
                avgRentalDuration: nft.rental.avgRentalDuration,
                ratings: nft.rental.ratings
            },
            pricing: {
                currentValue: nft.pricing.estimatedValue,
                lastSalePrice: nft.pricing.lastSalePrice,
                floorPrice: nft.pricing.currentFloorPrice,
                priceHistory: priceHistory
            },
            timeframe: {
                start: startDate,
                end: endDate,
                period: timeframe
            }
        }
    });
}));
exports.default = router;
