{"_format": "hh-sol-artifact-1", "contractName": "Simple", "sourceName": "src/mocks/Simple.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "count", "type": "uint64"}], "name": "CounterEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "expected", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "have", "type": "uint256"}], "name": "LogAndIncrementCalled", "type": "event"}, {"anonymous": false, "inputs": [], "name": "NullEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": false, "internalType": "address", "name": "redeemer", "type": "address"}], "name": "RedeemedEvent", "type": "event"}, {"inputs": [], "name": "checkBlockHashes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "useTopLevel", "type": "bool"}, {"internalType": "bool", "name": "directCase", "type": "bool"}, {"internalType": "bool", "name": "staticCase", "type": "bool"}, {"internalType": "bool", "name": "delegateCase", "type": "bool"}, {"internalType": "bool", "name": "callcodeCase", "type": "bool"}, {"internalType": "bool", "name": "callCase", "type": "bool"}], "name": "checkCalls", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "bytes", "name": "input", "type": "bytes"}], "name": "checkGasUsed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "useTopLevel", "type": "bool"}, {"internalType": "bool", "name": "expected", "type": "bool"}], "name": "checkIsTopLevelOrWasAliased", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "counter", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "difficulty", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emitNullEvent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBlockDifficulty", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "increment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "incrementEmit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "incrementRedeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "expected", "type": "uint256"}], "name": "logAndIncrement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "noop", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "pleaseRevert", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "bytes", "name": "batchData", "type": "bytes"}, {"internalType": "uint256", "name": "numberToPost", "type": "uint256"}], "name": "postManyBatches", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "storeDifficulty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}