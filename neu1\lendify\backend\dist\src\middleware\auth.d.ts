import { Request, Response, NextFunction } from 'express';
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        address: string;
        email?: string;
        role: 'user' | 'admin' | 'moderator';
        reputation?: number;
        isEmailVerified: boolean;
        createdAt: Date;
        lastLogin: Date;
    };
    chainId?: number;
}
export interface JWTPayload {
    userId: string;
    address: string;
    role: string;
    iat: number;
    exp: number;
}
export interface SignatureVerificationPayload {
    address: string;
    message: string;
    signature: string;
    timestamp: number;
}
declare class AuthMiddleware {
    private jwtSecret;
    private jwtExpiration;
    constructor();
    authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    optionalAuth: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireRole: (roles: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireModerator: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireReputation: (minReputation: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireEmailVerified: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireSignature: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    reputationBasedRateLimit: (baseLimit: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    generateToken: (user: {
        id: string;
        address: string;
        role: string;
    }) => string;
    generateRefreshToken: (userId: string) => string;
    refreshToken: (refreshToken: string) => Promise<string | null>;
    verifySignature: (message: string, signature: string, expectedAddress: string) => Promise<boolean>;
    authenticateWallet: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    generateAuthMessage: (req: Request, res: Response) => void;
    private extractToken;
    private getUserById;
    private getUserByAddress;
    private createUser;
    private updateLastLogin;
}
declare const authMiddleware: AuthMiddleware;
export { authMiddleware };
export interface AuthenticatedMiddleware {
    authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    optionalAuth: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireRole: (roles: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireModerator: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireReputation: (minReputation: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireEmailVerified: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireSignature: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    reputationBasedRateLimit: (baseLimit: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    generateToken: (user: {
        id: string;
        address: string;
        role: string;
    }) => string;
    generateRefreshToken: (userId: string) => string;
    refreshToken: (refreshToken: string) => Promise<string | null>;
    verifySignature: (message: string, signature: string, expectedAddress: string) => Promise<boolean>;
    authenticateWallet: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    generateAuthMessage: (req: Request, res: Response) => void;
}
//# sourceMappingURL=auth.d.ts.map