{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceName": "src/precompiles/ArbOwner.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "method", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "OwnerActs", "type": "event"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address"}], "name": "addWasmCacheManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAllChainOwners", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getInfraFeeAccount", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNetworkFeeAccount", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxWeiToRelease", "type": "uint256"}], "name": "releaseL1PricerSurplusFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "ownerToRemove", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address"}], "name": "removeWasmCacheManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newVersion", "type": "uint64"}, {"internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "scheduleArbOSUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "cap", "type": "uint64"}], "name": "setAmortizedCostCapBips", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "level", "type": "uint64"}], "name": "setBrotliCompressionLevel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "chainConfig", "type": "string"}], "name": "setChainConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newInfraFeeAccount", "type": "address"}], "name": "setInfraFeeAccount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "price", "type": "uint32"}], "name": "setInkPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "inertia", "type": "uint64"}], "name": "setL1BaseFeeEstimateInertia", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "pricePerUnit", "type": "uint256"}], "name": "setL1PricePerUnit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "equilibrationUnits", "type": "uint256"}], "name": "setL1PricingEquilibrationUnits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "inertia", "type": "uint64"}], "name": "setL1PricingInertia", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "weiPerUnit", "type": "uint64"}], "name": "setL1PricingRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "setL1PricingRewardRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "priceInWei", "type": "uint256"}], "name": "setL2BaseFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "sec", "type": "uint64"}], "name": "setL2GasBacklogTolerance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "sec", "type": "uint64"}], "name": "setL2GasPricingInertia", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "limit", "type": "uint64"}], "name": "setMaxTxGasLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "priceInWei", "type": "uint256"}], "name": "setMinimumL2BaseFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newNetworkFeeAccount", "type": "address"}], "name": "setNetworkFeeAccount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "int64", "name": "cost", "type": "int64"}], "name": "setPerBatchGasCharge", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "limit", "type": "uint64"}], "name": "setSpeedLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "count", "type": "uint16"}], "name": "setWasmBlockCacheSize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "_days", "type": "uint16"}], "name": "setWasmExpiryDays", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "pages", "type": "uint16"}], "name": "setWasmFreePages", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "percent", "type": "uint64"}], "name": "setWasmInitCostScalar", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "_days", "type": "uint16"}], "name": "setWasmKeepaliveDays", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "depth", "type": "uint32"}], "name": "setWasmMaxStackDepth", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "gas", "type": "uint8"}, {"internalType": "uint16", "name": "cached", "type": "uint16"}], "name": "setWasmMinInitGas", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "gas", "type": "uint16"}], "name": "setWasmPageGas", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "limit", "type": "uint16"}], "name": "setWasmPageLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}