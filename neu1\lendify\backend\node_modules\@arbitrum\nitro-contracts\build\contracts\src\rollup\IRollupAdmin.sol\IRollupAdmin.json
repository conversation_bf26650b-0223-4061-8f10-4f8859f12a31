{"_format": "hh-sol-artifact-1", "contractName": "IRollupAdmin", "sourceName": "src/rollup/IRollupAdmin.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "anyTrustFastConfirmer", "type": "address"}], "name": "AnyTrustFastConfirmerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "AssertionForceConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "As<PERSON><PERSON><PERSON><PERSON><PERSON>Created", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newBaseStake", "type": "uint256"}], "name": "BaseStakeSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}], "name": "ChallengeManagerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "newConfirmPeriod", "type": "uint64"}], "name": "ConfirmPeriodBlocksSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "DelayedInboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}], "name": "InboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newLoserStakerEscrow", "type": "address"}], "name": "LoserStakeEscrowSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinimumAssertionPeriodSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "outbox", "type": "address"}], "name": "OldOutboxRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "outbox", "type": "address"}], "name": "OutboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "staker", "type": "address[]"}], "name": "StakersForceRefunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "ValidatorAfkBlocksSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "_validator<PERSON><PERSON><PERSON>stDisabled", "type": "bool"}], "name": "ValidatorWhitelistDisabledSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "validators", "type": "address[]"}, {"indexed": false, "internalType": "bool[]", "name": "enabled", "type": "bool[]"}], "name": "ValidatorsSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "newWasmModuleRoot", "type": "bytes32"}], "name": "WasmModuleRootSet", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "confirmState", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "forceConfirmAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "forceCreateAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "stacker", "type": "address[]"}], "name": "forceRefundStaker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "address", "name": "stakeToken", "type": "address"}, {"internalType": "uint256", "name": "baseStake", "type": "uint256"}, {"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "loserStakeEscrow", "type": "address"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "string", "name": "chainConfig", "type": "string"}, {"internalType": "uint256", "name": "minimumAssertionPeriod", "type": "uint256"}, {"internalType": "uint64", "name": "validatorAfkBlocks", "type": "uint64"}, {"internalType": "uint256[]", "name": "miniStakeValues", "type": "uint256[]"}, {"components": [{"internalType": "uint256", "name": "delayBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "futureBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "delaySeconds", "type": "uint256"}, {"internalType": "uint256", "name": "futureSeconds", "type": "uint256"}], "internalType": "struct ISequencerInbox.MaxTimeVariation", "name": "sequencerInboxMaxTimeVariation", "type": "tuple"}, {"internalType": "uint256", "name": "layerZeroBlockEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroBigStepEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroSmallStepEdgeHeight", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "genesisAssertionState", "type": "tuple"}, {"internalType": "uint256", "name": "genesisInboxCount", "type": "uint256"}, {"internalType": "address", "name": "anyTrustFastConfirmer", "type": "address"}, {"internalType": "uint8", "name": "numBigStepLevel", "type": "uint8"}, {"internalType": "uint64", "name": "challengeGracePeriodBlocks", "type": "uint64"}, {"components": [{"internalType": "uint64", "name": "threshold", "type": "uint64"}, {"internalType": "uint64", "name": "max", "type": "uint64"}, {"internalType": "uint64", "name": "replenishRateInBasis", "type": "uint64"}], "internalType": "struct BufferConfig", "name": "bufferConfig", "type": "tuple"}], "internalType": "struct Config", "name": "config", "type": "tuple"}, {"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IEdgeChallengeManager", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "address", "name": "rollupAdminLogic", "type": "address"}, {"internalType": "contract IRollupUser", "name": "rollupUserLogic", "type": "address"}, {"internalType": "address", "name": "validatorWalletCreator", "type": "address"}], "internalType": "struct ContractDependencies", "name": "connectedContracts", "type": "tuple"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_outbox", "type": "address"}], "name": "removeOldOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "resume", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_anyTrustFastConfirmer", "type": "address"}], "name": "setAnyTrustFastConfirmer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseStake", "type": "uint256"}], "name": "setBaseStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_challenge<PERSON>anager", "type": "address"}], "name": "setChallengeManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newConfirmPeriod", "type": "uint64"}], "name": "setConfirmPeriodBlocks", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_inbox", "type": "address"}, {"internalType": "bool", "name": "_enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newLoserStakerEscrow", "type": "address"}], "name": "setLoserStakeEscrow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "setMinimumAssertionPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOutbox", "name": "_outbox", "type": "address"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_validator", "type": "address[]"}, {"internalType": "bool[]", "name": "_val", "type": "bool[]"}], "name": "setValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newAfkBlocks", "type": "uint64"}], "name": "setValidatorAfkBlocks", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_validator<PERSON><PERSON><PERSON>stDisabled", "type": "bool"}], "name": "setValidator<PERSON><PERSON><PERSON><PERSON>Disabled", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "newWasmModuleRoot", "type": "bytes32"}], "name": "setWasmModuleRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}