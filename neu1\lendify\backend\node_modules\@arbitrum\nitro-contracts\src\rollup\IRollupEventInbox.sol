// Copyright 2021-2022, Offchain Labs, Inc.
// For license information, see https://github.com/OffchainLabs/nitro-contracts/blob/main/LICENSE
// SPDX-License-Identifier: BUSL-1.1

pragma solidity ^0.8.0;

import "../bridge/IBridge.sol";

interface IRollupEventInbox {
    function bridge() external view returns (IBridge);

    function initialize(
        IBridge _bridge
    ) external;

    function rollup() external view returns (address);

    function updateRollupAddress() external;

    function rollupInitialized(uint256 chainId, string calldata chainConfig) external;
}
