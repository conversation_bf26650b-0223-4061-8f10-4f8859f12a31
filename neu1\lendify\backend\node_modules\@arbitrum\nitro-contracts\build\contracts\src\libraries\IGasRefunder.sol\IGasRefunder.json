{"_format": "hh-sol-artifact-1", "contractName": "IGasRefunder", "sourceName": "src/libraries/IGasRefunder.sol", "abi": [{"inputs": [{"internalType": "address payable", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "gasUsed", "type": "uint256"}, {"internalType": "uint256", "name": "calldataSize", "type": "uint256"}], "name": "onGasSpent", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}