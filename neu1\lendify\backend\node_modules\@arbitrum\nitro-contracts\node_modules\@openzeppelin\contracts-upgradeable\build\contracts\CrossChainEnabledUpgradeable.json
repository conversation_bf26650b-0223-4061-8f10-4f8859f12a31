{"_format": "hh-sol-artifact-1", "contractName": "CrossChainEnabledUpgradeable", "sourceName": "contracts/crosschain/CrossChainEnabledUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}