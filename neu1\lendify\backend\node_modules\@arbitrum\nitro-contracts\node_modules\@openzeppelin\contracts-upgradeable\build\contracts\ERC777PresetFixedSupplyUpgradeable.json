{"_format": "hh-sol-artifact-1", "contractName": "ERC777PresetFixedSupplyUpgradeable", "sourceName": "contracts/token/ERC777/presets/ERC777PresetFixedSupplyUpgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "AuthorizedOperator", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "Burned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "Minted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "RevokedOperator", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "<PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "authorizeOperator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "defaultOperators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "granularity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address[]", "name": "defaultOperators", "type": "address[]"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "tokenHolder", "type": "address"}], "name": "isOperatorFor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "operatorBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "operatorSend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "revokeOperator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "send", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}