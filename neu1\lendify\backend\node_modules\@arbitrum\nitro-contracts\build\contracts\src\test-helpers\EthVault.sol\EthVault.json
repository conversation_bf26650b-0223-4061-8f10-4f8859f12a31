{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON>", "sourceName": "src/test-helpers/EthVault.sol", "abi": [{"inputs": [], "name": "justRevert", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_version", "type": "uint256"}], "name": "setVersion", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x60806040526000805534801561001457600080fd5b50610127806100246000396000f3fe60806040526004361060305760003560e01c8063408def1e14603557806350b23fd214604757806354fd4d5014604d575b600080fd5b6045604036600460d9565b600055565b005b60456073565b348015605857600080fd5b50606160005481565b60405190815260200160405180910390f35b6040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152600360248201527f6279650000000000000000000000000000000000000000000000000000000000604482015260640160405180910390fd5b60006020828403121560ea57600080fd5b503591905056fea2646970667358221220efa9b40c963ccf2c04d3f702f303527d76ac06ab3b8dd50a651b8e47ce2e0a7164736f6c63430008110033", "deployedBytecode": "0x60806040526004361060305760003560e01c8063408def1e14603557806350b23fd214604757806354fd4d5014604d575b600080fd5b6045604036600460d9565b600055565b005b60456073565b348015605857600080fd5b50606160005481565b60405190815260200160405180910390f35b6040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152600360248201527f6279650000000000000000000000000000000000000000000000000000000000604482015260640160405180910390fd5b60006020828403121560ea57600080fd5b503591905056fea2646970667358221220efa9b40c963ccf2c04d3f702f303527d76ac06ab3b8dd50a651b8e47ce2e0a7164736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}