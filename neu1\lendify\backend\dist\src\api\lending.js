"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const Loan_1 = require("../models/Loan");
const NFT_1 = require("../models/NFT");
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({ success: true, service: 'Lending API', message: 'Service is healthy' });
});
router.post('/request', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        collateralNFTs: joi_1.default.array().items(joi_1.default.object({
            contractAddress: validation_1.commonSchemas.ethereumAddress,
            tokenId: validation_1.commonSchemas.tokenId,
            chainId: validation_1.commonSchemas.chainId,
            estimatedValue: joi_1.default.number().positive().required()
        })).min(1).required(),
        loanAmount: joi_1.default.number().positive().required(),
        currency: joi_1.default.string().valid('ETH', 'MATIC', 'USDC', 'DAI', 'USDT').required(),
        interestRate: joi_1.default.number().positive().max(100).required(),
        duration: joi_1.default.number().integer().min(1).max(365).required(),
        purpose: joi_1.default.string().max(500).required(),
        repaymentSchedule: joi_1.default.string().valid('lump_sum', 'monthly', 'weekly').default('lump_sum'),
        autoLiquidation: joi_1.default.boolean().default(true)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const borrowerAddress = req.user.address;
    const { collateralNFTs, loanAmount, currency, interestRate, duration, purpose, repaymentSchedule, autoLiquidation } = req.body;
    const nftVerifications = await Promise.all(collateralNFTs.map(async (collateral) => {
        const nft = await NFT_1.NFT.findByIdentifier(collateral.contractAddress, collateral.tokenId, collateral.chainId);
        if (!nft) {
            throw new Error(`NFT not found: ${collateral.contractAddress}:${collateral.tokenId}`);
        }
        if (nft.owner.toLowerCase() !== borrowerAddress.toLowerCase()) {
            throw new Error(`You do not own NFT: ${collateral.contractAddress}:${collateral.tokenId}`);
        }
        return { nft, collateral };
    }));
    const totalCollateralValue = collateralNFTs.reduce((sum, collateral) => sum + collateral.estimatedValue, 0);
    const ltvRatio = (loanAmount / totalCollateralValue) * 100;
    if (ltvRatio > 80) {
        return res.status(400).json({
            success: false,
            error: 'Loan amount exceeds maximum 80% LTV ratio'
        });
    }
    const totalInterest = (loanAmount * interestRate * duration) / (365 * 100);
    const totalRepayment = loanAmount + totalInterest;
    const loan = new Loan_1.Loan({
        borrower: borrowerAddress,
        collateral: collateralNFTs.map((c, index) => ({
            nft: nftVerifications[index].nft._id,
            contractAddress: c.contractAddress,
            tokenId: c.tokenId,
            chainId: c.chainId,
            estimatedValue: c.estimatedValue,
            lockedAt: new Date(),
            status: 'locked'
        })),
        terms: {
            principal: loanAmount,
            currency,
            interestRate,
            duration,
            totalRepayment,
            repaymentSchedule,
            ltvRatio
        },
        purpose,
        settings: {
            autoLiquidation,
            gracePeriod: 7
        },
        status: 'requested'
    });
    await loan.save();
    res.status(201).json({
        success: true,
        message: 'Loan request created successfully',
        data: { loan }
    });
}));
router.get('/requests', (0, validation_1.validateRequest)({
    query: joi_1.default.object({
        currency: joi_1.default.string().valid('ETH', 'MATIC', 'USDC', 'DAI', 'USDT').optional(),
        minAmount: joi_1.default.number().positive().optional(),
        maxAmount: joi_1.default.number().positive().optional(),
        maxLTV: joi_1.default.number().positive().max(100).optional(),
        sortBy: joi_1.default.string().valid('amount', 'ltv', 'interest', 'duration', 'recent').default('recent'),
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(50).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currency, minAmount, maxAmount, maxLTV, sortBy, page, limit } = req.query;
    const filters = { status: 'requested' };
    if (currency)
        filters['terms.currency'] = currency;
    if (maxLTV)
        filters['terms.ltvRatio'] = { $lte: maxLTV };
    if (minAmount || maxAmount) {
        filters['terms.principal'] = {};
        if (minAmount)
            filters['terms.principal'].$gte = minAmount;
        if (maxAmount)
            filters['terms.principal'].$lte = maxAmount;
    }
    const sortOptions = {};
    switch (sortBy) {
        case 'amount':
            sortOptions['terms.principal'] = -1;
            break;
        case 'ltv':
            sortOptions['terms.ltvRatio'] = 1;
            break;
        case 'interest':
            sortOptions['terms.interestRate'] = -1;
            break;
        default:
            sortOptions.createdAt = -1;
    }
    const skip = (page - 1) * limit;
    const [loans, total] = await Promise.all([
        Loan_1.Loan.find(filters)
            .sort(sortOptions)
            .skip(skip)
            .limit(limit)
            .populate('collateral.nft'),
        Loan_1.Loan.countDocuments(filters)
    ]);
    res.json({
        success: true,
        data: {
            loanRequests: loans,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) }
        }
    });
}));
router.get('/:loanId', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        loanId: validation_1.commonSchemas.mongoId
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { loanId } = req.params;
    const loan = await Loan_1.Loan.findById(loanId)
        .populate('collateral.nft');
    if (!loan) {
        return res.status(404).json({
            success: false,
            error: 'Loan not found'
        });
    }
    res.json({
        success: true,
        data: { loan }
    });
}));
exports.default = router;
