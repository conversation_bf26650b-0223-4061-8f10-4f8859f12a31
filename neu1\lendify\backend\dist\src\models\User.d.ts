import mongoose, { Document } from 'mongoose';
export interface IUser extends Document {
    _id: string;
    walletAddress: string;
    email?: string;
    username?: string;
    profile: {
        displayName?: string;
        bio?: string;
        avatar?: string;
        website?: string;
        twitter?: string;
        discord?: string;
    };
    reputation: {
        score: number;
        level: string;
        totalRentals: number;
        successfulRentals: number;
        totalListings: number;
        activeListings: number;
        totalEarnings: number;
        totalSpent: number;
        disputes: number;
        averageRating: number;
        totalRatings: number;
        badges: string[];
    };
    preferences: {
        notifications: {
            email: boolean;
            push: boolean;
            sms: boolean;
            categories: {
                rental: boolean;
                lending: boolean;
                payments: boolean;
                disputes: boolean;
                marketing: boolean;
                system: boolean;
            };
        };
        privacy: {
            showRealName: boolean;
            showEmail: boolean;
            showStats: boolean;
        };
    };
    verification: {
        isEmailVerified: boolean;
        isPhoneVerified: boolean;
        isKYCVerified: boolean;
        kycLevel: 'none' | 'basic' | 'advanced';
    };
    security: {
        twoFactorEnabled: boolean;
        loginAttempts: number;
        lockoutUntil?: Date;
        lastLogin: Date;
        lastLoginIP: string;
    };
    role: 'user' | 'moderator' | 'admin';
    status: 'active' | 'suspended' | 'banned';
    createdAt: Date;
    updatedAt: Date;
}
export declare const User: mongoose.Model<IUser, {}, {}, {}, mongoose.Document<unknown, {}, IUser, {}, {}> & IUser & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=User.d.ts.map