{"_format": "hh-sol-artifact-1", "contractName": "CrossChainEnabledPolygonChild", "sourceName": "contracts/crosschain/polygon/CrossChainEnabledPolygonChild.sol", "abi": [{"inputs": [], "name": "NotCrossChainCall", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "rootMessageSender", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "processMessageFromRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}