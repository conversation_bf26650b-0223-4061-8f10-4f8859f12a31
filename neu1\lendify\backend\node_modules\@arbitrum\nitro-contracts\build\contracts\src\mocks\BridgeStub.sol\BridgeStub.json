{"_format": "hh-sol-artifact-1", "contractName": "BridgeStub", "sourceName": "src/mocks/BridgeStub.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "stored", "type": "uint256"}, {"internalType": "uint256", "name": "received", "type": "uint256"}], "name": "BadSequencerMessageNumber", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "BridgeCallTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "InboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageIndex", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "beforeInboxAcc", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "kind", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "baseFeeL1", "type": "uint256"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "MessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "OutboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rollup", "type": "address"}], "name": "RollupUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxUpdated", "type": "event"}, {"inputs": [], "name": "acceptFundsFromOldBridge", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "activeOutbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedDelayedInboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}], "name": "allowedDelayedInboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedOutboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "allowedOutboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "delayedInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "delayedMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "enqueueDelayedMessage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "afterDelayedMessagesRead", "type": "uint256"}, {"internalType": "uint256", "name": "prevMessageCount", "type": "uint256"}, {"internalType": "uint256", "name": "newMessageCount", "type": "uint256"}], "name": "enqueueSequencerMessage", "outputs": [{"internalType": "uint256", "name": "seqMessageIndex", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "delayedAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "acc", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "nativeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nativeTokenDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sequencerInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerReportedSubMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "bool", "name": "", "type": "bool"}], "name": "setOutbox", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "batchPoster", "type": "address"}, {"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}], "name": "submitBatchSpendingReport", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "name": "updateRollupAddress", "outputs": [], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x60806040526004361061018a5760003560e01c8063ab5d8943116100d6578063d5719dc21161007f578063e77145f411610059578063e77145f414610223578063eca067ad14610487578063ee35f3271461049c57600080fd5b8063d5719dc214610427578063e1758bd814610447578063e76f5c8d1461046757600080fd5b8063c4d66de8116100b0578063c4d66de8146102d1578063cb23bcb5146103f7578063cee3d7281461040c57600080fd5b8063ab5d894314610357578063ad48cb5e14610377578063ae60bd13146103bb57600080fd5b80637a88b10711610138578063919cc70611610112578063919cc706146102d1578063945e1147146102f15780639e5d4c491461032957600080fd5b80637a88b1071461025b57806386598a561461027e5780638db5993b146102be57600080fd5b806347fb24c51161016957806347fb24c5146102035780634f61f850146102255780635fca4a161461024557600080fd5b806284120c1461018f57806316bf5579146101b3578063413b35bd146101d3575b600080fd5b34801561019b57600080fd5b506005545b6040519081526020015b60405180910390f35b3480156101bf57600080fd5b506101a06101ce366004610c28565b6104bc565b3480156101df57600080fd5b506101f36101ee366004610c59565b6104dd565b60405190151581526020016101aa565b34801561020f57600080fd5b5061022361021e366004610c7d565b61052d565b005b34801561023157600080fd5b50610223610240366004610c59565b61077b565b34801561025157600080fd5b506101a060075481565b34801561026757600080fd5b506101a0610276366004610cbb565b600092915050565b34801561028a57600080fd5b5061029e610299366004610ce7565b6107dc565b6040805194855260208501939093529183015260608201526080016101aa565b6101a06102cc366004610d19565b61092c565b3480156102dd57600080fd5b506102236102ec366004610c59565b6109a4565b3480156102fd57600080fd5b5061031161030c366004610c28565b6109ec565b6040516001600160a01b0390911681526020016101aa565b34801561033557600080fd5b50610349610344366004610d60565b610a16565b6040516101aa929190610e0d565b34801561036357600080fd5b50600354610311906001600160a01b031681565b34801561038357600080fd5b506008546103a99074010000000000000000000000000000000000000000900460ff1681565b60405160ff90911681526020016101aa565b3480156103c757600080fd5b506101f36103d6366004610c59565b6001600160a01b031660009081526020819052604090206001015460ff1690565b34801561040357600080fd5b506103116104dd565b34801561041857600080fd5b506102236102ec366004610c7d565b34801561043357600080fd5b506101a0610442366004610c28565b610ab2565b34801561045357600080fd5b50600854610311906001600160a01b031681565b34801561047357600080fd5b50610311610482366004610c28565b610ac2565b34801561049357600080fd5b506004546101a0565b3480156104a857600080fd5b50600654610311906001600160a01b031681565b600581815481106104cc57600080fd5b600091825260209091200154905081565b60405162461bcd60e51b815260206004820152600f60248201527f4e4f545f494d504c454d454e544544000000000000000000000000000000000060448201526000906064015b60405180910390fd5b6001600160a01b03821660008181526020818152604091829020600181015492518515158152909360ff90931692917f6675ce8882cb71637de5903a193d218cc0544be9c0650cb83e0955f6aa2bf521910160405180910390a2821515811515036105985750505050565b82156106315760408051808201825260018054825260208083018281526001600160a01b0389166000818152928390529482209351845551928201805460ff1916931515939093179092558054808201825591527fb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf601805473ffffffffffffffffffffffffffffffffffffffff19169091179055610775565b60018054610640908290610e49565b8154811061065057610650610e6a565b6000918252602090912001548254600180546001600160a01b0390931692909190811061067f5761067f610e6a565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b03160217905550816000015460008060018560000154815481106106cc576106cc610e6a565b60009182526020808320909101546001600160a01b03168352820192909252604001902055600180548061070257610702610e80565b6000828152602080822083017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff908101805473ffffffffffffffffffffffffffffffffffffffff191690559092019092556001600160a01b038616825281905260408120908155600101805460ff191690555b50505050565b6006805473ffffffffffffffffffffffffffffffffffffffff19166001600160a01b0383169081179091556040519081527f8c1e6003ed33ca6748d4ad3dd4ecc949065c89dceb31fdf546a5289202763c6a9060200160405180910390a150565b60008060008085600754141580156107f357508515155b8015610800575060075415155b15610845576007546040517fe2051feb000000000000000000000000000000000000000000000000000000008152600481019190915260248101879052604401610524565b600785905560055493508315610883576005805461086590600190610e49565b8154811061087557610875610e6a565b906000526020600020015492505b86156108b4576004610896600189610e49565b815481106108a6576108a6610e6a565b906000526020600020015491505b60408051602081018590529081018990526060810183905260800160408051601f198184030181529190528051602090910120600580546001810182556000919091527f036b6384b5eca791c62761152d0c79bb0604c104a5fb6f4eb0703f3154bb3db0018190559398929750909550919350915050565b3360009081526020819052604081206001015460ff1661098e5760405162461bcd60e51b815260206004820152600e60248201527f4e4f545f46524f4d5f494e424f580000000000000000000000000000000000006044820152606401610524565b61099c848443424887610ad2565b949350505050565b60405162461bcd60e51b815260206004820152600f60248201527f4e4f545f494d504c454d454e54454400000000000000000000000000000000006044820152606401610524565b600281815481106109fc57600080fd5b6000918252602090912001546001600160a01b0316905081565b60006060610a5b868686868080601f016020809104026020016040519081016040528093929190818152602001838380828437600092019190915250610b8d92505050565b60405191935091506001600160a01b0387169033907f2d9d115ef3e4a606d698913b1eae831a3cdfe20d9a83d48007b0526749c3d46690610aa190899089908990610e96565b60405180910390a394509492505050565b600481815481106104cc57600080fd5b600181815481106109fc57600080fd5b60045460408051600060208083018290526021830182905260358301829052603d8301829052604583018290526065830182905260858084018790528451808503909101815260a59093019093528151919092012090919060008215610b5d576004610b3f600185610e49565b81548110610b4f57610b4f610e6a565b906000526020600020015490505b6004610b698284610bf9565b81546001810183556000928352602090922090910155509098975050505050505050565b60006060846001600160a01b03168484604051610baa9190610ecc565b60006040518083038185875af1925050503d8060008114610be7576040519150601f19603f3d011682016040523d82523d6000602084013e610bec565b606091505b5090969095509350505050565b604080516020808201859052818301849052825180830384018152606090920190925280519101205b92915050565b600060208284031215610c3a57600080fd5b5035919050565b6001600160a01b0381168114610c5657600080fd5b50565b600060208284031215610c6b57600080fd5b8135610c7681610c41565b9392505050565b60008060408385031215610c9057600080fd5b8235610c9b81610c41565b915060208301358015158114610cb057600080fd5b809150509250929050565b60008060408385031215610cce57600080fd5b8235610cd981610c41565b946020939093013593505050565b60008060008060808587031215610cfd57600080fd5b5050823594602084013594506040840135936060013592509050565b600080600060608486031215610d2e57600080fd5b833560ff81168114610d3f57600080fd5b92506020840135610d4f81610c41565b929592945050506040919091013590565b60008060008060608587031215610d7657600080fd5b8435610d8181610c41565b935060208501359250604085013567ffffffffffffffff80821115610da557600080fd5b818701915087601f830112610db957600080fd5b813581811115610dc857600080fd5b886020828501011115610dda57600080fd5b95989497505060200194505050565b60005b83811015610e04578181015183820152602001610dec565b50506000910152565b82151581526040602082015260008251806040840152610e34816060850160208701610de9565b601f01601f1916919091016060019392505050565b81810381811115610c2257634e487b7160e01b600052601160045260246000fd5b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052603160045260246000fd5b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f1916010192915050565b60008251610ede818460208701610de9565b919091019291505056fea2646970667358221220c3c393bb28fa27a5b2613ed54bdc493d71c9012e4f39157d21a4db2df437852564736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}