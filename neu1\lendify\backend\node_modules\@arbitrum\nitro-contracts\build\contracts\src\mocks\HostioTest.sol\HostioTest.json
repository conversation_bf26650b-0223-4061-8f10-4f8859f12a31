{"_format": "hh-sol-artifact-1", "contractName": "HostioTest", "sourceName": "src/mocks/HostioTest.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "accountBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "accountCode", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "accountCodeSize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "blockBasefee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "blockCoinbase", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "blockGasLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "blockNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "blockTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "chainid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "contractAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "int8", "name": "n", "type": "int8"}, {"internalType": "bytes32", "name": "t1", "type": "bytes32"}, {"internalType": "bytes32", "name": "t2", "type": "bytes32"}, {"internalType": "bytes32", "name": "t3", "type": "bytes32"}, {"internalType": "bytes32", "name": "t4", "type": "bytes32"}], "name": "emitLog", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "evmGasLeft", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "evmInkLeft", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "exitEarly", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "preimage", "type": "bytes"}], "name": "keccak", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}, {"internalType": "uint256", "name": "c", "type": "uint256"}], "name": "mathAddMod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "mathDiv", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "mathMod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}, {"internalType": "uint256", "name": "c", "type": "uint256"}], "name": "mathMulMod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "mathPow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "msgSender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "msgValue", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "returnDataSize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}], "name": "transientLoadBytes32", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}, {"internalType": "bytes32", "name": "value", "type": "bytes32"}], "name": "transientStoreBytes32", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "txGasPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "txInkPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "txOrigin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}