{"name": "@arbitrum/nitro-contracts", "version": "3.0.0", "description": "Layer 2 precompiles and rollup for Arbitrum Nitro", "author": "Offchain Labs, Inc.", "license": "BUSL-1.1", "repository": {"type": "git", "url": "git+https://github.com/offchainlabs/nitro-contracts.git"}, "files": ["src/", "build/contracts/src", "build/contracts/@openzeppelin", "out/yul/Reader4844.yul/Reader4844.json"], "bugs": {"url": "https://github.com/offchainlabs/nitro-contracts/issues"}, "scripts": {"audit:ci": "audit-ci --config ./audit-ci.jsonc", "audit:fix": "yarn-audit-fix", "prepublishOnly": "hardhat clean && forge clean && hardhat compile && yarn build:forge:yul", "coverage": "forge coverage --report lcov --ir-minimum && lcov --remove lcov.info 'node_modules/*' 'test/*' 'script/*' 'src/test-helpers/*' 'challenge/*' --ignore-errors unused -o lcov.info && genhtml lcov.info --branch-coverage --output-dir coverage", "build:all": "yarn build && yarn build:forge", "build": "hardhat compile", "build:forge:sol": "forge build --skip *.yul", "build:forge:yul": "FOUNDRY_PROFILE=yul forge build --skip *.sol", "build:forge": "yarn build:forge:sol && yarn build:forge:yul", "contract:size": "hardhat size-contracts", "lint:test": "eslint ./test", "solhint": "solhint -f table src/**/*.sol", "prettier:solidity": "prettier --write src/**/*.sol", "format": "forge fmt && prettier './**/*.{js,json,md,ts,yml}' --write && yarn run lint:test --fix", "build:0.6": "INTERFACE_TESTER_SOLC_VERSION=0.6.9 yarn run build", "build:0.7": "INTERFACE_TESTER_SOLC_VERSION=0.7.0 yarn run build", "test": "DISABLE_GAS_REPORTER=true hardhat --network hardhat test test/contract/*.spec.ts", "test:4844": "DISABLE_GAS_REPORTER=true hardhat --network hardhat test test/contract/*.spec.4844.ts", "test:compatibility": "yarn run build:0.6 && yarn run build:0.7", "test:storage": "./test/storage/test.bash", "test:signatures": "./test/signatures/test-sigs.bash", "test:e2e": "hardhat test test/e2e/*.ts", "test:upgrade": "./scripts/testUpgrade.bash", "test:foundry": "forge test --gas-limit 10000000000", "test:update": "yarn run test:signatures || yarn run test:storage", "metadatahash": "yarn build:all && hardhat run scripts/printMetadataHashes.ts", "upload-4bytes": "forge build && find ./out -type f -name \"*.json\" -exec cast upload-signature {} + | grep -v Duplicated:", "postinstall": "patch-package", "deploy-factory": "hardhat run scripts/deployment.ts", "deploy-eth-rollup": "hardhat run scripts/createEthRollup.ts", "deploy-erc20-rollup": "hardhat run scripts/createERC20Rollup.ts", "create-rollup-testnode": "hardhat run scripts/local-deployment/deployCreatorAndCreateRollup.ts", "script:bold-prepare": "hardhat run ./scripts/prepareBoldUpgrade.ts", "script:bold-populate-lookup": "hardhat run ./scripts/populateLookup.ts", "script:bold-local-execute": "hardhat run ./scripts/executeBoldUpgrade.ts", "deploy-cachemanager-testnode": "hardhat run scripts/local-deployment/deployCacheManager.ts"}, "dependencies": {"@offchainlabs/upgrade-executor": "1.1.0-beta.0", "@openzeppelin/contracts": "4.7.3", "@openzeppelin/contracts-upgradeable": "4.7.3", "patch-package": "^6.4.7", "solady": "0.0.182"}, "private": false, "devDependencies": {"@arbitrum/sdk": "^3.4.1", "@ethersproject/providers": "^5.7.2", "@nomicfoundation/hardhat-verify": "^2.0.9", "@nomiclabs/hardhat-ethers": "npm:hardhat-deploy-ethers@^0.3.0-beta.13", "@nomiclabs/hardhat-waffle": "^2.0.1", "@tovarishfin/hardhat-yul": "^3.0.5", "@typechain/ethers-v5": "^10.0.0", "@typechain/hardhat": "^6.0.0", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "@types/node": "^17.0.5", "@typescript-eslint/eslint-plugin": "^5.14.0", "@typescript-eslint/eslint-plugin-tslint": "^5.27.1", "@typescript-eslint/parser": "^5.14.0", "audit-ci": "^6.6.1", "chai": "^4.3.4", "dotenv": "^16.3.1", "eslint": "^8.23.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-mocha": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "ethereum-waffle": "^4.0.10", "ethers": "^5.5.4", "hardhat": "^2.17.2", "hardhat-contract-sizer": "^2.10.0", "hardhat-deploy": "^0.11.37", "hardhat-gas-reporter": "^1.0.9", "hardhat-ignore-warnings": "^0.2.9", "postinstall-postinstall": "^2.1.0", "prettier": "^2.5.1", "prettier-plugin-solidity": "^1.0.0-beta.19", "solhint": "^3.3.7", "solhint-plugin-prettier": "^0.0.5", "solidity-coverage": "^0.8.4", "ts-node": "^10.4.0", "tslint": "^6.1.3", "typechain": "^8.3.2", "typescript": "^4.5.4", "yarn-audit-fix": "^10.0.7"}}