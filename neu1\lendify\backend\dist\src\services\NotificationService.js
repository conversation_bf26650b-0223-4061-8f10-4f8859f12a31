"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const events_1 = require("events");
const nodemailer_1 = __importDefault(require("nodemailer"));
const axios_1 = __importDefault(require("axios"));
class NotificationService extends events_1.EventEmitter {
    constructor(io) {
        super();
        this.templates = new Map();
        this.userPreferences = new Map();
        this.notificationQueue = [];
        this.isProcessing = false;
        this.io = io;
        this.initializeEmailTransporter();
        this.initializeTemplates();
        this.startNotificationProcessor();
    }
    initializeEmailTransporter() {
        try {
            const emailConfig = {
                host: process.env.SMTP_HOST || 'smtp.gmail.com',
                port: parseInt(process.env.SMTP_PORT || '587'),
                secure: process.env.SMTP_SECURE === 'true',
                auth: {
                    user: process.env.SMTP_USER || '',
                    pass: process.env.SMTP_PASS || ''
                },
                from: process.env.SMTP_FROM || '<EMAIL>'
            };
            this.emailTransporter = nodemailer_1.default.createTransport(emailConfig);
            console.log('✅ Email transporter initialized');
        }
        catch (error) {
            console.warn('⚠️ Failed to initialize email transporter:', error);
        }
    }
    initializeTemplates() {
        this.templates.set('rental_created', {
            id: 'rental_created',
            type: 'websocket',
            name: 'Rental Created',
            template: 'Your NFT {{nftName}} has been listed for rent at {{price}} ETH/day',
            variables: ['nftName', 'price', 'duration']
        });
        this.templates.set('rental_request', {
            id: 'rental_request',
            type: 'email',
            name: 'Rental Request',
            subject: 'New rental request for your NFT',
            template: `
        <h2>New Rental Request</h2>
        <p>Someone wants to rent your NFT <strong>{{nftName}}</strong></p>
        <p>Rental Details:</p>
        <ul>
          <li>Duration: {{duration}} days</li>
          <li>Total Price: {{totalPrice}} ETH</li>
          <li>Collateral: {{collateral}} ETH</li>
        </ul>
        <p>Click <a href="{{approveLink}}">here</a> to approve the rental.</p>
      `,
            variables: ['nftName', 'duration', 'totalPrice', 'collateral', 'approveLink']
        });
        this.templates.set('rental_approved', {
            id: 'rental_approved',
            type: 'push',
            name: 'Rental Approved',
            template: 'Your rental request for {{nftName}} has been approved! 🎉',
            variables: ['nftName']
        });
        this.templates.set('rental_expiring', {
            id: 'rental_expiring',
            type: 'email',
            name: 'Rental Expiring',
            subject: 'Your NFT rental is expiring soon',
            template: `
        <h2>Rental Expiring Soon</h2>
        <p>Your rental for <strong>{{nftName}}</strong> expires in {{timeLeft}}.</p>
        <p>Would you like to extend your rental?</p>
        <a href="{{extendLink}}" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Extend Rental</a>
      `,
            variables: ['nftName', 'timeLeft', 'extendLink']
        });
        this.templates.set('loan_request', {
            id: 'loan_request',
            type: 'email',
            name: 'Loan Request',
            subject: 'New loan request against your NFT',
            template: `
        <h2>Loan Request</h2>
        <p>Someone wants to borrow against your NFT <strong>{{nftName}}</strong></p>
        <p>Loan Details:</p>
        <ul>
          <li>Requested Amount: {{loanAmount}} ETH</li>
          <li>Duration: {{duration}} days</li>
          <li>Interest Rate: {{interestRate}}%</li>
          <li>Collateral Value: {{collateralValue}} ETH</li>
        </ul>
      `,
            variables: ['nftName', 'loanAmount', 'duration', 'interestRate', 'collateralValue']
        });
        this.templates.set('loan_funded', {
            id: 'loan_funded',
            type: 'websocket',
            name: 'Loan Funded',
            template: 'Your loan of {{loanAmount}} ETH has been funded! 💰',
            variables: ['loanAmount']
        });
        this.templates.set('payment_received', {
            id: 'payment_received',
            type: 'push',
            name: 'Payment Received',
            template: 'Payment of {{amount}} ETH received for {{nftName}} rental',
            variables: ['amount', 'nftName']
        });
        this.templates.set('payment_overdue', {
            id: 'payment_overdue',
            type: 'email',
            name: 'Payment Overdue',
            subject: 'Payment overdue - Action required',
            template: `
        <h2>Payment Overdue</h2>
        <p>Your payment for the rental of <strong>{{nftName}}</strong> is overdue.</p>
        <p>Amount Due: {{amountDue}} ETH</p>
        <p>Days Overdue: {{daysOverdue}}</p>
        <p>Please make the payment to avoid penalties.</p>
      `,
            variables: ['nftName', 'amountDue', 'daysOverdue']
        });
        this.templates.set('welcome', {
            id: 'welcome',
            type: 'email',
            name: 'Welcome',
            subject: 'Welcome to Lendify! 🎉',
            template: `
        <h2>Welcome to Lendify!</h2>
        <p>Hi {{username}},</p>
        <p>Welcome to the future of NFT rentals! We're excited to have you on board.</p>
        <p>Get started by:</p>
        <ul>
          <li>Listing your first NFT for rent</li>
          <li>Exploring available NFTs to rent</li>
          <li>Building your reputation</li>
        </ul>
        <p>Happy renting!</p>
      `,
            variables: ['username']
        });
        console.log(`✅ Initialized ${this.templates.size} notification templates`);
    }
    startNotificationProcessor() {
        setInterval(() => {
            this.processNotificationQueue();
        }, 5000);
        console.log('✅ Notification processor started');
    }
    async sendNotification(notification) {
        const fullNotification = {
            id: this.generateNotificationId(),
            createdAt: new Date(),
            status: 'pending',
            ...notification
        };
        const preferences = await this.getUserPreferences(notification.userId);
        if (!this.shouldSendNotification(fullNotification, preferences)) {
            console.log(`Notification skipped due to user preferences: ${fullNotification.id}`);
            return false;
        }
        this.notificationQueue.push(fullNotification);
        this.emit('notificationQueued', fullNotification);
        return true;
    }
    async sendRentalNotification(type, userId, data, channels = ['websocket', 'push']) {
        const template = this.templates.get(type);
        if (!template) {
            console.error(`Template not found: ${type}`);
            return false;
        }
        const message = this.renderTemplate(template.template, data);
        const title = this.getNotificationTitle(type, data);
        return await this.sendNotification({
            userId,
            type: 'rental',
            priority: this.getNotificationPriority(type),
            title,
            message,
            data,
            channels
        });
    }
    async sendLendingNotification(type, userId, data, channels = ['email', 'websocket']) {
        const title = this.getNotificationTitle(type, data);
        const message = this.renderTemplate(this.getTemplateForType(type), data);
        return await this.sendNotification({
            userId,
            type: 'lending',
            priority: this.getNotificationPriority(type),
            title,
            message,
            data,
            channels
        });
    }
    async sendPaymentNotification(type, userId, data, channels = ['push', 'email']) {
        const title = this.getNotificationTitle(type, data);
        const message = this.renderTemplate(this.getTemplateForType(type), data);
        return await this.sendNotification({
            userId,
            type: 'payment',
            priority: type === 'payment_overdue' ? 'high' : 'medium',
            title,
            message,
            data,
            channels
        });
    }
    async sendSystemNotification(type, userId, data, channels = ['email']) {
        const title = this.getNotificationTitle(type, data);
        const message = this.renderTemplate(this.getTemplateForType(type), data);
        return await this.sendNotification({
            userId,
            type: 'system',
            priority: type === 'security_alert' ? 'critical' : 'medium',
            title,
            message,
            data,
            channels
        });
    }
    async sendWebSocketNotification(notification) {
        try {
            const room = `user:${notification.userId}`;
            this.io.to(room).emit('notification', {
                id: notification.id,
                type: notification.type,
                priority: notification.priority,
                title: notification.title,
                message: notification.message,
                data: notification.data,
                timestamp: notification.createdAt
            });
            console.log(`WebSocket notification sent to user ${notification.userId}`);
            return true;
        }
        catch (error) {
            console.error('Failed to send WebSocket notification:', error);
            return false;
        }
    }
    async sendEmailNotification(notification) {
        if (!this.emailTransporter) {
            console.error('Email transporter not configured');
            return false;
        }
        try {
            const template = this.templates.get(notification.type);
            const subject = template?.subject ? this.renderTemplate(template.subject, notification.data) : notification.title;
            const mailOptions = {
                from: process.env.SMTP_FROM,
                to: await this.getUserEmail(notification.userId),
                subject,
                html: this.renderEmailTemplate(notification)
            };
            const result = await this.emailTransporter.sendMail(mailOptions);
            console.log(`Email notification sent: ${result.messageId}`);
            return true;
        }
        catch (error) {
            console.error('Failed to send email notification:', error);
            return false;
        }
    }
    async sendPushNotification(notification) {
        try {
            const pushTokens = await this.getUserPushTokens(notification.userId);
            if (pushTokens.length === 0) {
                console.log(`No push tokens found for user ${notification.userId}`);
                return false;
            }
            if (process.env.FCM_SERVER_KEY) {
                const payload = {
                    notification: {
                        title: notification.title,
                        body: notification.message,
                        icon: '/icons/notification.png'
                    },
                    data: notification.data ? JSON.stringify(notification.data) : '{}'
                };
                for (const token of pushTokens) {
                    await this.sendFCMNotification(token, payload);
                }
            }
            console.log(`Push notification sent to ${pushTokens.length} devices`);
            return true;
        }
        catch (error) {
            console.error('Failed to send push notification:', error);
            return false;
        }
    }
    async sendSMSNotification(notification) {
        try {
            const phoneNumber = await this.getUserPhoneNumber(notification.userId);
            if (!phoneNumber) {
                console.log(`No phone number found for user ${notification.userId}`);
                return false;
            }
            if (process.env.TWILIO_SID && process.env.TWILIO_TOKEN) {
                const message = `${notification.title}\n${notification.message}`;
                const response = await axios_1.default.post(`https://api.twilio.com/2010-04-01/Accounts/${process.env.TWILIO_SID}/Messages.json`, new URLSearchParams({
                    From: process.env.TWILIO_PHONE,
                    To: phoneNumber,
                    Body: message
                }), {
                    auth: {
                        username: process.env.TWILIO_SID,
                        password: process.env.TWILIO_TOKEN
                    }
                });
                console.log(`SMS notification sent: ${response.data.sid}`);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('Failed to send SMS notification:', error);
            return false;
        }
    }
    async processNotificationQueue() {
        if (this.isProcessing || this.notificationQueue.length === 0) {
            return;
        }
        this.isProcessing = true;
        try {
            const notification = this.notificationQueue.shift();
            if (!notification)
                return;
            notification.status = 'sent';
            notification.sentAt = new Date();
            const results = await Promise.all(notification.channels.map(channel => this.sendToChannel(notification, channel)));
            const success = results.some(result => result);
            notification.status = success ? 'delivered' : 'failed';
            this.emit('notificationProcessed', notification);
            await this.storeNotification(notification);
        }
        catch (error) {
            console.error('Error processing notification:', error);
        }
        finally {
            this.isProcessing = false;
        }
    }
    async sendToChannel(notification, channel) {
        switch (channel) {
            case 'websocket':
                return await this.sendWebSocketNotification(notification);
            case 'email':
                return await this.sendEmailNotification(notification);
            case 'push':
                return await this.sendPushNotification(notification);
            case 'sms':
                return await this.sendSMSNotification(notification);
            default:
                console.error(`Unknown notification channel: ${channel}`);
                return false;
        }
    }
    async getUserPreferences(userId) {
        let preferences = this.userPreferences.get(userId);
        if (!preferences) {
            preferences = {
                userId,
                email: true,
                push: true,
                sms: false,
                websocket: true,
                categories: {
                    rental: true,
                    lending: true,
                    payments: true,
                    disputes: true,
                    marketing: false,
                    system: true
                }
            };
            this.userPreferences.set(userId, preferences);
        }
        return preferences;
    }
    async updateUserPreferences(userId, preferences) {
        const current = await this.getUserPreferences(userId);
        const updated = { ...current, ...preferences };
        this.userPreferences.set(userId, updated);
        await this.saveUserPreferences(updated);
        this.emit('preferencesUpdated', { userId, preferences: updated });
    }
    async sendBulkNotification(userIds, notification) {
        const promises = userIds.map(userId => this.sendNotification({ ...notification, userId }));
        await Promise.all(promises);
        console.log(`Bulk notification sent to ${userIds.length} users`);
    }
    async sendBroadcast(notification, criteria) {
        const userIds = await this.getUsersForBroadcast(criteria);
        await this.sendBulkNotification(userIds, notification);
    }
    async sendInstantNotification(userId, message, data) {
        const room = `user:${userId}`;
        this.io.to(room).emit('instant-notification', {
            message,
            data,
            timestamp: new Date()
        });
    }
    async broadcastToAll(message, data) {
        this.io.emit('broadcast', {
            message,
            data,
            timestamp: new Date()
        });
    }
    generateNotificationId() {
        return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    renderTemplate(template, data) {
        let rendered = template;
        Object.keys(data || {}).forEach(key => {
            const placeholder = new RegExp(`{{${key}}}`, 'g');
            rendered = rendered.replace(placeholder, data[key] || '');
        });
        return rendered;
    }
    renderEmailTemplate(notification) {
        const template = this.templates.get(notification.type);
        if (template && template.type === 'email') {
            return this.renderTemplate(template.template, notification.data);
        }
        return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>${notification.title}</h2>
        <p>${notification.message}</p>
        <hr>
        <p style="color: #666; font-size: 12px;">
          This is an automated message from Lendify. Please do not reply.
        </p>
      </div>
    `;
    }
    getNotificationTitle(type, data) {
        const titles = {
            rental_created: 'NFT Listed for Rent',
            rental_request: 'New Rental Request',
            rental_approved: 'Rental Approved',
            rental_expiring: 'Rental Expiring Soon',
            loan_request: 'New Loan Request',
            loan_funded: 'Loan Funded',
            payment_received: 'Payment Received',
            payment_overdue: 'Payment Overdue',
            welcome: 'Welcome to Lendify!'
        };
        return titles[type] || 'Notification';
    }
    getNotificationPriority(type) {
        const priorities = {
            rental_created: 'low',
            rental_request: 'medium',
            rental_approved: 'high',
            rental_expiring: 'medium',
            loan_request: 'medium',
            loan_funded: 'high',
            payment_received: 'medium',
            payment_overdue: 'high',
            welcome: 'low'
        };
        return priorities[type] || 'medium';
    }
    getTemplateForType(type) {
        const templates = {
            loan_request: 'New loan request for {{loanAmount}} ETH against your NFT {{nftName}}',
            loan_funded: 'Your loan has been funded with {{loanAmount}} ETH',
            payment_received: 'Payment of {{amount}} ETH received',
            payment_overdue: 'Payment overdue: {{amountDue}} ETH for {{nftName}}',
            welcome: 'Welcome to Lendify, {{username}}!'
        };
        return templates[type] || 'You have a new notification';
    }
    shouldSendNotification(notification, preferences) {
        if (!preferences.categories[notification.type]) {
            return false;
        }
        return notification.channels.some(channel => {
            switch (channel) {
                case 'email': return preferences.email;
                case 'push': return preferences.push;
                case 'sms': return preferences.sms;
                case 'websocket': return preferences.websocket;
                default: return false;
            }
        });
    }
    async sendFCMNotification(token, payload) {
        const response = await axios_1.default.post('https://fcm.googleapis.com/fcm/send', {
            to: token,
            ...payload
        }, {
            headers: {
                'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        console.log(`FCM notification sent: ${response.data.message_id || 'success'}`);
    }
    async storeNotification(notification) {
        console.log(`Storing notification ${notification.id} in database`);
    }
    async saveUserPreferences(preferences) {
        console.log(`Saving preferences for user ${preferences.userId}`);
    }
    async getUserEmail(userId) {
        return '<EMAIL>';
    }
    async getUserPushTokens(userId) {
        return [];
    }
    async getUserPhoneNumber(userId) {
        return null;
    }
    async getUsersForBroadcast(criteria) {
        return [];
    }
    getQueueSize() {
        return this.notificationQueue.length;
    }
    getProcessingStatus() {
        return this.isProcessing;
    }
    clearQueue() {
        this.notificationQueue = [];
    }
}
exports.NotificationService = NotificationService;
