export declare const config: {
    server: {
        port: number;
        host: string;
        nodeEnv: string;
        frontendUrl: string;
    };
    database: {
        mongoUri: string;
        maxPoolSize: number;
        serverSelectionTimeoutMS: number;
        socketTimeoutMS: number;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    blockchain: {
        ethereum: {
            rpcUrl: string;
            chainId: number;
            explorerUrl: string;
        };
        polygon: {
            rpcUrl: string;
            chainId: number;
            explorerUrl: string;
        };
        arbitrum: {
            rpcUrl: string;
            chainId: number;
            explorerUrl: string;
        };
        optimism: {
            rpcUrl: string;
            chainId: number;
            explorerUrl: string;
        };
        base: {
            rpcUrl: string;
            chainId: number;
            explorerUrl: string;
        };
    };
    contracts: {
        ethereum: {
            nftRentalMarketplace: string;
            reputationSystem: string;
            nftCollateralizedLending: string;
            flashRentalLoan: string;
            dynamicPricingOracle: string;
        };
        polygon: {
            nftRentalMarketplace: string;
            reputationSystem: string;
            nftCollateralizedLending: string;
            flashRentalLoan: string;
            dynamicPricingOracle: string;
        };
        arbitrum: {
            nftRentalMarketplace: string;
            reputationSystem: string;
            nftCollateralizedLending: string;
            flashRentalLoan: string;
            dynamicPricingOracle: string;
        };
        optimism: {
            nftRentalMarketplace: string;
            reputationSystem: string;
            nftCollateralizedLending: string;
            flashRentalLoan: string;
            dynamicPricingOracle: string;
        };
        base: {
            nftRentalMarketplace: string;
            reputationSystem: string;
            nftCollateralizedLending: string;
            flashRentalLoan: string;
            dynamicPricingOracle: string;
        };
    };
    ai: {
        openai: {
            apiKey: string;
            model: string;
            maxTokens: number;
        };
        huggingface: {
            apiKey: string;
            modelUrl: string;
        };
    };
    apis: {
        chainlink: {
            apiKey: string;
        };
        alchemy: {
            apiKey: string;
        };
        infura: {
            projectId: string;
            projectSecret: string;
        };
        opensea: {
            apiKey: string;
        };
    };
    notifications: {
        smtp: {
            host: string;
            port: number;
            secure: boolean;
            user: string;
            pass: string;
            from: string;
        };
        twilio: {
            sid: string;
            token: string;
            phone: string;
        };
        fcm: {
            serverKey: string;
        };
    };
    rateLimit: {
        windowMs: number;
        maxRequests: number;
        skipSuccessfulRequests: boolean;
    };
    upload: {
        maxFileSize: number;
        allowedTypes: string[];
    };
    security: {
        bcryptRounds: number;
        sessionSecret: string;
        corsOrigins: string[];
    };
    analytics: {
        googleAnalytics: {
            trackingId: string;
        };
        mixpanel: {
            token: string;
        };
    };
    cache: {
        redis: {
            host: string;
            port: number;
            password: string;
            db: number;
        };
        ttl: number;
    };
    logging: {
        level: string;
        file: string;
        maxSize: string;
        maxFiles: number;
    };
    features: {
        enableAI: boolean;
        enableCrossChain: boolean;
        enableAnalytics: boolean;
        enableNotifications: boolean;
        maintenanceMode: boolean;
    };
};
export default config;
//# sourceMappingURL=config.d.ts.map