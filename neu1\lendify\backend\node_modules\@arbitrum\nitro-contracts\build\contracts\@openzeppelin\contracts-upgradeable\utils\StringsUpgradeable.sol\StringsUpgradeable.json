{"_format": "hh-sol-artifact-1", "contractName": "StringsUpgradeable", "sourceName": "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212201dad96dbeba3abbc68e72f9ba595a092158e950ccd0fbe10367ab1b4d28b9db564736f6c63430008110033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212201dad96dbeba3abbc68e72f9ba595a092158e950ccd0fbe10367ab1b4d28b9db564736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}