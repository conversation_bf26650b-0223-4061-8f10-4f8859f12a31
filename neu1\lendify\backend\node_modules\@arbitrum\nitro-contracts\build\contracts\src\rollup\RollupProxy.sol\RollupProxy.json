{"_format": "hh-sol-artifact-1", "contractName": "RollupProxy", "sourceName": "src/rollup/RollupProxy.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "UpgradedSecondary", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [{"components": [{"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "address", "name": "stakeToken", "type": "address"}, {"internalType": "uint256", "name": "baseStake", "type": "uint256"}, {"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "loserStakeEscrow", "type": "address"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "string", "name": "chainConfig", "type": "string"}, {"internalType": "uint256", "name": "minimumAssertionPeriod", "type": "uint256"}, {"internalType": "uint64", "name": "validatorAfkBlocks", "type": "uint64"}, {"internalType": "uint256[]", "name": "miniStakeValues", "type": "uint256[]"}, {"components": [{"internalType": "uint256", "name": "delayBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "futureBlocks", "type": "uint256"}, {"internalType": "uint256", "name": "delaySeconds", "type": "uint256"}, {"internalType": "uint256", "name": "futureSeconds", "type": "uint256"}], "internalType": "struct ISequencerInbox.MaxTimeVariation", "name": "sequencerInboxMaxTimeVariation", "type": "tuple"}, {"internalType": "uint256", "name": "layerZeroBlockEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroBigStepEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroSmallStepEdgeHeight", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "genesisAssertionState", "type": "tuple"}, {"internalType": "uint256", "name": "genesisInboxCount", "type": "uint256"}, {"internalType": "address", "name": "anyTrustFastConfirmer", "type": "address"}, {"internalType": "uint8", "name": "numBigStepLevel", "type": "uint8"}, {"internalType": "uint64", "name": "challengeGracePeriodBlocks", "type": "uint64"}, {"components": [{"internalType": "uint64", "name": "threshold", "type": "uint64"}, {"internalType": "uint64", "name": "max", "type": "uint64"}, {"internalType": "uint64", "name": "replenishRateInBasis", "type": "uint64"}], "internalType": "struct BufferConfig", "name": "bufferConfig", "type": "tuple"}], "internalType": "struct Config", "name": "config", "type": "tuple"}, {"components": [{"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "sequencerInbox", "type": "address"}, {"internalType": "contract IInboxBase", "name": "inbox", "type": "address"}, {"internalType": "contract IOutbox", "name": "outbox", "type": "address"}, {"internalType": "contract IRollupEventInbox", "name": "rollupEventInbox", "type": "address"}, {"internalType": "contract IEdgeChallengeManager", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "address", "name": "rollupAdminLogic", "type": "address"}, {"internalType": "contract IRollupUser", "name": "rollupUserLogic", "type": "address"}, {"internalType": "address", "name": "validatorWalletCreator", "type": "address"}], "internalType": "struct ContractDependencies", "name": "connectedContracts", "type": "tuple"}], "name": "initializeProxy", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x608060405234801561001057600080fd5b50611433806100206000396000f3fe6080604052600436106100225760003560e01c8063adfef6ac1461003957610031565b366100315761002f610059565b005b61002f610059565b34801561004557600080fd5b5061002f610054366004610d95565b61006b565b6100696100646101bd565b6102a1565b565b60006100756102ca565b6001600160a01b031614801561009b575060006100906102fd565b6001600160a01b0316145b80156100b7575060006100ac610325565b6001600160a01b0316145b156101b5576101b18160c0015183836040516024016100d7929190611158565b60408051601f19818403018152918152602080830180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff167f0ee5ef0c0000000000000000000000000000000000000000000000000000000017905260e08601519087015191516001600160a01b0390921660248301529060440160408051601f198184030181529190526020810180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff167fc4d66de800000000000000000000000000000000000000000000000000000000179052608087015161034d565b5050565b6101b1610059565b600060043610156102155760405162461bcd60e51b815260206004820152600b60248201527f4e4f5f46554e435f53494700000000000000000000000000000000000000000060448201526064015b60405180910390fd5b6000336102206102ca565b6001600160a01b03160361023b576102366102fd565b610243565b610243610325565b90506001600160a01b0381163b61029c5760405162461bcd60e51b815260206004820152601360248201527f5441524745545f4e4f545f434f4e545241435400000000000000000000000000604482015260640161020c565b919050565b3660008037600080366000845af43d6000803e8080156102c0573d6000f35b3d6000fd5b505050565b60007fb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d61035b546001600160a01b0316919050565b60007f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc6102ee565b60007f2b1dbce74324248c222f0ec2d5ed7bd323cfc425b336f0253c5ccfda7265546d6102ee565b61037860017fb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6104611370565b7fb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103146103a6576103a6611391565b6103d160017f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbd611370565b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc146103ff576103ff611391565b61042a60017f2b1dbce74324248c222f0ec2d5ed7bd323cfc425b336f0253c5ccfda7265546e611370565b7f2b1dbce74324248c222f0ec2d5ed7bd323cfc425b336f0253c5ccfda7265546d1461045857610458611391565b61046181610480565b61046d858560006104d7565b61047983836000610502565b5050505050565b7f7e644d79422f17c01e4894b5f4f588d331ebfa28653d42ae832dc59e38c9798f6104a96102ca565b604080516001600160a01b03928316815291841660208301520160405180910390a16104d48161050b565b50565b6104e0836105e3565b6000825111806104ed5750805b156102c5576104fc8383610623565b50505050565b6104e083610651565b6001600160a01b0381166105875760405162461bcd60e51b815260206004820152602660248201527f455243313936373a206e65772061646d696e20697320746865207a65726f206160448201527f6464726573730000000000000000000000000000000000000000000000000000606482015260840161020c565b807fb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d61035b80547fffffffffffffffffffffffff0000000000000000000000000000000000000000166001600160a01b039290921691909117905550565b6105ec81610691565b6040516001600160a01b038216907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b90600090a250565b606061064883836040518060600160405280602781526020016113d760279139610735565b90505b92915050565b61065a8161082b565b6040516001600160a01b038216907ff7eed2a7fabbf1bec8d55ed5e785cc76622376dde5df4ff15470551e030b813490600090a250565b6001600160a01b0381163b61070e5760405162461bcd60e51b815260206004820152602d60248201527f455243313936373a206e657720696d706c656d656e746174696f6e206973206e60448201527f6f74206120636f6e747261637400000000000000000000000000000000000000606482015260840161020c565b807f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc6105aa565b60606001600160a01b0384163b6107b45760405162461bcd60e51b815260206004820152602660248201527f416464726573733a2064656c65676174652063616c6c20746f206e6f6e2d636f60448201527f6e74726163740000000000000000000000000000000000000000000000000000606482015260840161020c565b600080856001600160a01b0316856040516107cf91906113a7565b600060405180830381855af49150503d806000811461080a576040519150601f19603f3d011682016040523d82523d6000602084013e61080f565b606091505b509150915061081f8282866108cf565b925050505b9392505050565b6001600160a01b0381163b6108a85760405162461bcd60e51b815260206004820152603760248201527f455243313936373a206e6577207365636f6e6461727920696d706c656d656e7460448201527f6174696f6e206973206e6f74206120636f6e7472616374000000000000000000606482015260840161020c565b807f2b1dbce74324248c222f0ec2d5ed7bd323cfc425b336f0253c5ccfda7265546d6105aa565b606083156108de575081610824565b8251156108ee5782518084602001fd5b8160405162461bcd60e51b815260040161020c91906113c3565b634e487b7160e01b600052604160045260246000fd5b6040516060810167ffffffffffffffff8111828210171561094157610941610908565b60405290565b6040805190810167ffffffffffffffff8111828210171561094157610941610908565b604051610120810167ffffffffffffffff8111828210171561094157610941610908565b6040516102a0810167ffffffffffffffff8111828210171561094157610941610908565b604051601f8201601f1916810167ffffffffffffffff811182821017156109db576109db610908565b604052919050565b803567ffffffffffffffff8116811461029c57600080fd5b80356001600160a01b038116811461029c57600080fd5b600082601f830112610a2357600080fd5b813567ffffffffffffffff811115610a3d57610a3d610908565b610a506020601f19601f840116016109b2565b818152846020838601011115610a6557600080fd5b816020850160208301376000918101602001919091529392505050565b600082601f830112610a9357600080fd5b8135602067ffffffffffffffff821115610aaf57610aaf610908565b8160051b610abe8282016109b2565b9283528481018201928281019087851115610ad857600080fd5b83870192505b84831015610af757823582529183019190830190610ade565b979650505050505050565b600060808284031215610b1457600080fd5b6040516080810181811067ffffffffffffffff82111715610b3757610b37610908565b8060405250809150823581526020830135602082015260408301356040820152606083013560608201525092915050565b600082601f830112610b7957600080fd5b610b81610947565b806040840185811115610b9357600080fd5b845b81811015610bb457610ba6816109e3565b845260209384019301610b95565b509095945050505050565b80356003811061029c57600080fd5b600081830360c0811215610be157600080fd5b610be961091e565b91506080811215610bf957600080fd5b50610c02610947565b83601f840112610c1157600080fd5b610c19610947565b806040850186811115610c2b57600080fd5b855b81811015610c45578035845260209384019301610c2d565b50818452610c538782610b68565b60208501525050508152610c6960808301610bbf565b602082015260a0820135604082015292915050565b803560ff8116811461029c57600080fd5b600060608284031215610ca157600080fd5b610ca961091e565b9050610cb4826109e3565b8152610cc2602083016109e3565b6020820152610cd3604083016109e3565b604082015292915050565b60006101208284031215610cf157600080fd5b610cf961096a565b9050610d04826109fb565b8152610d12602083016109fb565b6020820152610d23604083016109fb565b6040820152610d34606083016109fb565b6060820152610d45608083016109fb565b6080820152610d5660a083016109fb565b60a0820152610d6760c083016109fb565b60c0820152610d7860e083016109fb565b60e0820152610100610d8b8184016109fb565b9082015292915050565b600080610140808486031215610daa57600080fd5b833567ffffffffffffffff80821115610dc257600080fd5b908501906103e08288031215610dd757600080fd5b610ddf61098e565b610de8836109e3565b8152610df6602084016109fb565b60208201526040830135604082015260608301356060820152610e1b608084016109fb565b6080820152610e2c60a084016109fb565b60a082015260c083013560c082015260e083013582811115610e4d57600080fd5b610e5989828601610a12565b60e0830152506101008381013590820152610120610e788185016109e3565b908201528284013582811115610e8d57600080fd5b610e9989828601610a82565b85830152506101609350610eaf88858501610b02565b848201526101e09350838301356101808201526102009150818301356101a0820152610220808401356101c0830152610240610eed8a828701610bce565b8684015261030085013584840152610f0861032086016109fb565b82840152610f196103408601610c7e565b9083015250610f2b61036084016109e3565b610260820152610f3f886103808501610c8f565b61028082015280955050505050610f598460208501610cde565b90509250929050565b60005b83811015610f7d578181015183820152602001610f65565b50506000910152565b60008151808452610f9e816020860160208601610f62565b601f01601f19169290920160200192915050565b600081518084526020808501945080840160005b83811015610fe257815187529582019590820190600101610fc6565b509495945050505050565b6003811061100b57634e487b7160e01b600052602160045260246000fd5b9052565b805180518360005b6002811015611036578251825260209283019290910190600101611017565b505050602090810151906040840160005b600281101561106e57835167ffffffffffffffff1682529282019290820190600101611047565b505082015190506110826080840182610fed565b506040015160a09190910152565b6001600160a01b038082511683528060208301511660208401528060408301511660408401525060608101516110d160608401826001600160a01b03169052565b5060808101516110ec60808401826001600160a01b03169052565b5060a081015161110760a08401826001600160a01b03169052565b5060c081015161112260c08401826001600160a01b03169052565b5060e081015161113d60e08401826001600160a01b03169052565b50610100818101516001600160a01b038116848301526104fc565b6000610140808352611176818401865167ffffffffffffffff169052565b60208501516001600160a01b0381166101608501525060408501516101808181860152606087015191506101a08281870152608088015192506101c06111c6818801856001600160a01b03169052565b60a089015193506101e06111e4818901866001600160a01b03169052565b60c08a0151945061020085818a015260e08b015195506102206103e0818b01526112126105208b0188610f86565b96506101008c015161024081818d01526101208e01519150610260611242818e018467ffffffffffffffff169052565b8a8f01519a5061028092507ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec08d8b0301838e01526112808a8c610fb2565b9a506101608f015199506112b96102a08e018b805182526020810151602083015260408101516040830152606081015160608301525050565b888f01516103208e0152878f01516103408e0152868f01516103608e0152858f015199506112eb6103808e018b61100f565b938e01516104408d0152918d01516001600160a01b03166104608c0152908c015160ff166104808b0152908b015167ffffffffffffffff9081166104a08b0152908b0151805182166104c08b0152602081015182166104e08b015260408101519091166105008a015294506113609350505050565b5090506108246020830184611090565b8181038181111561064b57634e487b7160e01b600052601160045260246000fd5b634e487b7160e01b600052600160045260246000fd5b600082516113b9818460208701610f62565b9190910192915050565b6020815260006106486020830184610f8656fe416464726573733a206c6f772d6c6576656c2064656c65676174652063616c6c206661696c6564a2646970667358221220c50f3bd10cc10c9d6a7e3c77a603ea076a398bfabf2598ec9e1d71302b8c13b364736f6c63430008110033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}