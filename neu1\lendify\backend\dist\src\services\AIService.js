"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const axios_1 = __importDefault(require("axios"));
const events_1 = require("events");
class AIService extends events_1.EventEmitter {
    constructor() {
        super();
        this.analysisCache = new Map();
        this.isInitialized = false;
        this.openaiApiKey = process.env.OPENAI_API_KEY || '';
        this.huggingfaceApiKey = process.env.HUGGINGFACE_API_KEY || '';
    }
    async initialize() {
        try {
            if (this.openaiApiKey) {
                await this.testOpenAIConnection();
            }
            if (this.huggingfaceApiKey) {
                await this.testHuggingFaceConnection();
            }
            this.isInitialized = true;
            console.log('✅ AI Service initialized successfully');
        }
        catch (error) {
            console.warn('⚠️ AI Service initialization failed, running with limited functionality:', error);
            this.isInitialized = false;
        }
    }
    async testOpenAIConnection() {
        try {
            const response = await axios_1.default.post('https://api.openai.com/v1/chat/completions', {
                model: 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: 'Test connection' }],
                max_tokens: 5
            }, {
                headers: {
                    'Authorization': `Bearer ${this.openaiApiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            if (response.status === 200) {
                console.log('✅ OpenAI API connection successful');
            }
        }
        catch (error) {
            if (error.response?.status === 429) {
                console.log('✅ OpenAI API connection successful (rate limited)');
            }
            else {
                throw new Error(`OpenAI API connection failed: ${error.message}`);
            }
        }
    }
    async testHuggingFaceConnection() {
        try {
            const response = await axios_1.default.post('https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium', { inputs: 'test' }, {
                headers: {
                    'Authorization': `Bearer ${this.huggingfaceApiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            console.log('✅ HuggingFace API connection successful');
        }
        catch (error) {
            if (error.response?.status === 503) {
                console.log('✅ HuggingFace API connection successful (model loading)');
            }
            else {
                throw new Error(`HuggingFace API connection failed: ${error.message}`);
            }
        }
    }
    async analyzeDynamicPricing(nftContract, tokenId, metadata, marketData, rentalHistory) {
        const cacheKey = `pricing_${nftContract}_${tokenId}`;
        const cached = this.analysisCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 300000) {
            return cached.data;
        }
        try {
            const rarityScore = this.calculateRarityScore(metadata);
            const demandScore = this.calculateDemandScore(rentalHistory, marketData);
            const utilityScore = this.calculateUtilityScore(metadata);
            const marketSentiment = this.calculateMarketSentiment(marketData);
            const seasonality = this.calculateSeasonality();
            const basePrice = this.calculateBasePrice(marketData, rentalHistory);
            let predictedPrice = basePrice;
            let confidence = 0.5;
            let reasoning = [];
            if (this.isInitialized && this.openaiApiKey) {
                const aiAnalysis = await this.getAIPricingAnalysis({
                    metadata,
                    marketData,
                    rentalHistory,
                    rarityScore,
                    demandScore,
                    utilityScore
                });
                predictedPrice = aiAnalysis.predictedPrice;
                confidence = aiAnalysis.confidence;
                reasoning = aiAnalysis.reasoning;
            }
            else {
                const multiplier = this.calculatePriceMultiplier(rarityScore, demandScore, utilityScore, marketSentiment, seasonality);
                predictedPrice = basePrice * multiplier;
                confidence = this.calculateConfidence(rarityScore, demandScore, utilityScore);
                reasoning = this.generateFallbackReasoning(rarityScore, demandScore, utilityScore);
            }
            const result = {
                currentPrice: basePrice,
                predictedPrice,
                confidence,
                trend: predictedPrice > basePrice * 1.05 ? 'up' : predictedPrice < basePrice * 0.95 ? 'down' : 'stable',
                factors: {
                    demandScore,
                    rarityScore,
                    utilityScore,
                    marketSentiment,
                    seasonality
                },
                timeframe: '7d',
                reasoning
            };
            this.analysisCache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
            this.emit('pricePrediction', { nftContract, tokenId, prediction: result });
            return result;
        }
        catch (error) {
            console.error(`Error in dynamic pricing analysis for ${nftContract}:${tokenId}:`, error);
            throw error;
        }
    }
    async getAIPricingAnalysis(data) {
        const prompt = `
    Analyze this NFT for rental pricing:

    Collection: ${data.metadata.collection || 'Unknown'}
    Attributes: ${JSON.stringify(data.metadata.attributes || [])}
    
    Market Data:
    - Floor Price: ${data.marketData.floorPrice} ETH
    - 24h Volume: ${data.marketData.volume24h} ETH
    - 24h Sales: ${data.marketData.sales24h}
    - Average Sale Price: ${data.marketData.avgSalePrice} ETH
    
    Rental History:
    - Total Rentals: ${data.rentalHistory.rentalCount}
    - Average Daily Price: ${data.rentalHistory.avgDailyPrice} ETH
    - Average Rating: ${data.rentalHistory.avgRating}/5
    - Demand Score: ${data.rentalHistory.demandScore}
    
    Scores:
    - Rarity Score: ${data.rarityScore}
    - Demand Score: ${data.demandScore}
    - Utility Score: ${data.utilityScore}

    Provide a rental price prediction with confidence level and reasoning. 
    Consider market trends, rarity, demand, and utility.
    
    Respond in JSON format:
    {
      "predictedPrice": number,
      "confidence": number (0-1),
      "reasoning": ["reason1", "reason2", "reason3"]
    }
    `;
        try {
            const response = await axios_1.default.post('https://api.openai.com/v1/chat/completions', {
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert NFT analyst specializing in rental pricing. Provide accurate, data-driven pricing predictions.'
                    },
                    { role: 'user', content: prompt }
                ],
                max_tokens: 500,
                temperature: 0.1
            }, {
                headers: {
                    'Authorization': `Bearer ${this.openaiApiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const content = response.data.choices[0].message.content;
            return JSON.parse(content);
        }
        catch (error) {
            console.error('Error getting AI pricing analysis:', error);
            throw error;
        }
    }
    async generateUserRecommendations(userAddress, userHistory, preferences, marketData) {
        try {
            const recommendations = [];
            const behaviorPattern = this.analyzeUserBehavior(userHistory);
            const rentRecommendations = await this.generateRentRecommendations(behaviorPattern, marketData);
            const lendRecommendations = await this.generateLendRecommendations(userAddress, behaviorPattern, marketData);
            recommendations.push(...rentRecommendations, ...lendRecommendations);
            return recommendations
                .sort((a, b) => b.score - a.score)
                .slice(0, 10);
        }
        catch (error) {
            console.error('Error generating user recommendations:', error);
            return [];
        }
    }
    async analyzeMarketTrends(collections, timeframe = '7d') {
        try {
            const trends = {};
            for (const collection of collections) {
                const analysis = await this.analyzeCollectionTrends(collection, timeframe);
                trends[collection] = analysis;
            }
            return {
                timeframe,
                timestamp: new Date(),
                collections: trends,
                overallSentiment: this.calculateOverallMarketSentiment(Object.values(trends))
            };
        }
        catch (error) {
            console.error('Error analyzing market trends:', error);
            throw error;
        }
    }
    async assessRisk(nftContract, tokenId, transactionType, amount) {
        try {
            const riskFactors = {
                marketVolatility: await this.calculateMarketVolatility(nftContract),
                liquidityRisk: await this.calculateLiquidityRisk(nftContract),
                contractRisk: await this.assessSmartContractRisk(nftContract),
                userRisk: await this.calculateUserRisk(),
                priceRisk: await this.calculatePriceRisk(nftContract, tokenId, amount)
            };
            const overallRisk = this.calculateOverallRisk(riskFactors);
            return {
                overallRisk,
                riskLevel: overallRisk < 0.3 ? 'low' : overallRisk < 0.7 ? 'medium' : 'high',
                factors: riskFactors,
                recommendations: this.generateRiskRecommendations(riskFactors, transactionType),
                timestamp: new Date()
            };
        }
        catch (error) {
            console.error('Error in risk assessment:', error);
            throw error;
        }
    }
    calculateRarityScore(metadata) {
        if (!metadata.attributes || metadata.attributes.length === 0)
            return 0.5;
        const attributeCount = metadata.attributes.length;
        const uniquenessScore = attributeCount > 0 ? Math.min(attributeCount / 10, 1) : 0;
        return Math.min(uniquenessScore + (metadata.rarity || 0) / 100, 1);
    }
    calculateDemandScore(rentalHistory, marketData) {
        const rentalFrequency = rentalHistory.rentalCount > 0 ? Math.min(rentalHistory.rentalCount / 50, 1) : 0;
        const ratingScore = rentalHistory.avgRating > 0 ? rentalHistory.avgRating / 5 : 0;
        const volumeScore = marketData.sales24h > 0 ? Math.min(marketData.sales24h / 100, 1) : 0;
        return (rentalFrequency + ratingScore + volumeScore) / 3;
    }
    calculateUtilityScore(metadata) {
        const gameRelatedKeywords = ['game', 'gaming', 'play', 'character', 'weapon', 'land', 'avatar'];
        const description = (metadata.description || '').toLowerCase();
        const name = (metadata.name || '').toLowerCase();
        let utilityScore = 0;
        gameRelatedKeywords.forEach(keyword => {
            if (description.includes(keyword) || name.includes(keyword)) {
                utilityScore += 0.2;
            }
        });
        if (metadata.attributes) {
            const utilityAttributes = metadata.attributes.filter(attr => ['power', 'level', 'rarity', 'strength', 'magic', 'ability'].some(keyword => attr.trait_type.toLowerCase().includes(keyword)));
            utilityScore += Math.min(utilityAttributes.length * 0.1, 0.5);
        }
        return Math.min(utilityScore, 1);
    }
    calculateMarketSentiment(marketData) {
        const volumeChange = marketData.volumeChange24h || 0;
        const priceChange = marketData.floorPriceChange24h || 0;
        const volumeSentiment = Math.max(-1, Math.min(1, volumeChange / 100));
        const priceSentiment = Math.max(-1, Math.min(1, priceChange / 100));
        return (volumeSentiment + priceSentiment + 2) / 4;
    }
    calculateSeasonality() {
        const month = new Date().getMonth();
        const seasonalityFactors = [0.8, 0.7, 0.9, 1.0, 1.1, 1.0, 0.9, 0.8, 1.0, 1.1, 1.2, 1.3];
        return seasonalityFactors[month] || 1.0;
    }
    calculateBasePrice(marketData, rentalHistory) {
        if (rentalHistory.avgDailyPrice > 0) {
            return rentalHistory.avgDailyPrice;
        }
        return marketData.floorPrice * 0.01;
    }
    calculatePriceMultiplier(...scores) {
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        return 0.5 + (avgScore * 1.5);
    }
    calculateConfidence(...scores) {
        const variance = this.calculateVariance(scores);
        return Math.max(0.1, 1 - variance);
    }
    calculateVariance(numbers) {
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        return variance;
    }
    generateFallbackReasoning(rarityScore, demandScore, utilityScore) {
        const reasoning = [];
        if (rarityScore > 0.7)
            reasoning.push('High rarity score indicates premium value');
        if (demandScore > 0.7)
            reasoning.push('Strong rental demand based on historical data');
        if (utilityScore > 0.5)
            reasoning.push('Utility features may drive rental interest');
        if (rarityScore < 0.3)
            reasoning.push('Common attributes may limit pricing power');
        return reasoning;
    }
    analyzeUserBehavior(userHistory) {
        return {
            preferredCategories: [],
            avgRentalDuration: 0,
            riskTolerance: 'medium',
            budget: { min: 0, max: 1 }
        };
    }
    async generateRentRecommendations(behaviorPattern, marketData) {
        return [];
    }
    async generateLendRecommendations(userAddress, behaviorPattern, marketData) {
        return [];
    }
    async analyzeCollectionTrends(collection, timeframe) {
        return {
            trend: 'stable',
            confidence: 0.5,
            factors: []
        };
    }
    calculateOverallMarketSentiment(analyses) {
        return 'neutral';
    }
    async calculateMarketVolatility(nftContract) {
        return 0.5;
    }
    async calculateLiquidityRisk(nftContract) {
        return 0.3;
    }
    async assessSmartContractRisk(nftContract) {
        return 0.2;
    }
    async calculateUserRisk() {
        return 0.1;
    }
    async calculatePriceRisk(nftContract, tokenId, amount) {
        return 0.4;
    }
    calculateOverallRisk(riskFactors) {
        const weights = { marketVolatility: 0.3, liquidityRisk: 0.25, contractRisk: 0.2, userRisk: 0.15, priceRisk: 0.1 };
        return Object.entries(riskFactors).reduce((total, [factor, value]) => total + value * weights[factor], 0);
    }
    generateRiskRecommendations(riskFactors, transactionType) {
        const recommendations = [];
        if (riskFactors.marketVolatility > 0.7) {
            recommendations.push('Consider waiting for more stable market conditions');
        }
        if (riskFactors.liquidityRisk > 0.5) {
            recommendations.push('Ensure you can afford to hold the position for extended periods');
        }
        return recommendations;
    }
    clearCache() {
        this.analysisCache.clear();
    }
    getCacheStats() {
        return {
            size: this.analysisCache.size,
            keys: Array.from(this.analysisCache.keys())
        };
    }
}
exports.AIService = AIService;
