{"_format": "hh-sol-artifact-1", "contractName": "PullPayment", "sourceName": "contracts/security/PullPayment.sol", "abi": [{"inputs": [{"internalType": "address", "name": "dest", "type": "address"}], "name": "payments", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "payee", "type": "address"}], "name": "withdrawPayments", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}