{"_format": "hh-sol-artifact-1", "contractName": "IOneStepProofEntry", "sourceName": "src/osp/IOneStepProofEntry.sol", "abi": [{"inputs": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}], "internalType": "struct ExecutionState", "name": "execState", "type": "tuple"}], "name": "getMachineHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "globalStateHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}], "name": "getStartMachineHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "maxInboxMessagesRead", "type": "uint256"}, {"internalType": "contract IBridge", "name": "bridge", "type": "address"}, {"internalType": "bytes32", "name": "initialWasmModuleRoot", "type": "bytes32"}], "internalType": "struct ExecutionContext", "name": "execCtx", "type": "tuple"}, {"internalType": "uint256", "name": "machineStep", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeHash", "type": "bytes32"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "name": "proveOneStep", "outputs": [{"internalType": "bytes32", "name": "afterHash", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}