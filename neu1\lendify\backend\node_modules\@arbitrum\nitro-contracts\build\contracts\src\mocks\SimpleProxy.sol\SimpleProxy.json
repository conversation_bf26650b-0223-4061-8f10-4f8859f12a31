{"_format": "hh-sol-artifact-1", "contractName": "SimpleProxy", "sourceName": "src/mocks/SimpleProxy.sol", "abi": [{"inputs": [{"internalType": "address", "name": "impl_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"stateMutability": "payable", "type": "fallback"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x60a060405234801561001057600080fd5b5060405161011d38038061011d83398101604081905261002f91610040565b6001600160a01b0316608052610070565b60006020828403121561005257600080fd5b81516001600160a01b038116811461006957600080fd5b9392505050565b608051609561008860003960006017015260956000f3fe608060405236601057600e6013565b005b600e5b603a7f0000000000000000000000000000000000000000000000000000000000000000603c565b565b3660008037600080366000845af43d6000803e808015605a573d6000f35b3d6000fdfea2646970667358221220fd97eb7ab561ce9de75836f84b9a83564b7717b6ab82a9c6dbfb2cadebc7610b64736f6c63430008110033", "deployedBytecode": "0x608060405236601057600e6013565b005b600e5b603a7f0000000000000000000000000000000000000000000000000000000000000000603c565b565b3660008037600080366000845af43d6000803e808015605a573d6000f35b3d6000fdfea2646970667358221220fd97eb7ab561ce9de75836f84b9a83564b7717b6ab82a9c6dbfb2cadebc7610b64736f6c63430008110033", "linkReferences": {}, "deployedLinkReferences": {}}