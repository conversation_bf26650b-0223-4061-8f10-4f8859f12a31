{"_format": "hh-sol-artifact-1", "contractName": "IOutbox", "sourceName": "contracts/vendor/arbitrum/IOutbox.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "destAddr", "type": "address"}, {"indexed": true, "internalType": "address", "name": "l2Sender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "outboxEntryIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "transactionIndex", "type": "uint256"}], "name": "OutBoxTransactionExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "batchNum", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "outboxEntryIndex", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "outputRoot", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "numInBatch", "type": "uint256"}], "name": "OutboxEntryCreated", "type": "event"}, {"inputs": [], "name": "l2ToL1BatchNum", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1Block", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1EthBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1OutputId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1Sender", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2ToL1Timestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "batchNum", "type": "uint256"}], "name": "outboxEntryExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "sendsData", "type": "bytes"}, {"internalType": "uint256[]", "name": "sendLengths", "type": "uint256[]"}], "name": "processOutgoingMessages", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}