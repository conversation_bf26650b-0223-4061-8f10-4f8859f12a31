{"_format": "hh-sol-artifact-1", "contractName": "GasRefunder", "sourceName": "src/bridge/GasRefunder.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum GasRefunder.CommonParameterKey", "name": "parameter", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "CommonParameterSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "allowed", "type": "bool"}], "name": "ContractAllowedSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Deposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}], "name": "DisallowerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "refundee", "type": "address"}, {"indexed": true, "internalType": "address", "name": "contractAddress", "type": "address"}, {"indexed": true, "internalType": "enum GasRefunder.RefundDenyReason", "name": "reason", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "gas", "type": "uint256"}], "name": "RefundGasCostsDenied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "refundee", "type": "address"}, {"indexed": true, "internalType": "address", "name": "contractAddress", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "success", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "gas", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "gasPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountPaid", "type": "uint256"}], "name": "RefundedGasCosts", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "allowed", "type": "bool"}], "name": "RefundeeAllowedSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "initiator", "type": "address"}, {"indexed": false, "internalType": "address", "name": "destination", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "name": "allowContracts", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "name": "allowRefundees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "allowedContracts", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "allowedRefundees", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "commonParams", "outputs": [{"internalType": "uint128", "name": "maxRefundeeBalance", "type": "uint128"}, {"internalType": "uint32", "name": "extraGas<PERSON><PERSON>gin", "type": "uint32"}, {"internalType": "uint8", "name": "calldataCost", "type": "uint8"}, {"internalType": "uint64", "name": "maxGasTip", "type": "uint64"}, {"internalType": "uint64", "name": "maxGasCost", "type": "uint64"}, {"internalType": "uint32", "name": "maxSingleGasUsage", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "name": "disallowContracts", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "name": "disallowRefundees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "disallower", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "refundee", "type": "address"}, {"internalType": "uint256", "name": "gasUsed", "type": "uint256"}, {"internalType": "uint256", "name": "calldataSize", "type": "uint256"}], "name": "onGasSpent", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "newValue", "type": "uint8"}], "name": "setCalldataCost", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "newValue", "type": "uint32"}], "name": "setExtraGasMargin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newValue", "type": "uint64"}], "name": "setMaxGasCost", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "newValue", "type": "uint64"}], "name": "setMaxGasTip", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint128", "name": "newValue", "type": "uint128"}], "name": "setMaxRefundeeBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "newValue", "type": "uint32"}], "name": "setMaxSingleGasUsage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "destination", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x608060405234801561001057600080fd5b5061001a336100a5565b6040805160c08101825260008152610fa06020820152600c9181019190915263773594006060820152641bf08eb0006080820152621e848060a090910152600480546001600160e81b03191678773594000c00000fa000000000000000000000000000000000179055600580546001600160601b0319166a1e84800000001bf08eb0001790556100f5565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b6112b9806101046000396000f3fe6080604052600436106101635760003560e01c8063ca101295116100c0578063efe12b0111610074578063f2fde38b11610059578063f2fde38b146104db578063f3fef3a3146104fb578063f52128eb1461051b57600080fd5b8063efe12b011461049b578063f1e845ca146104bb57600080fd5b8063d5138948116100a5578063d513894814610365578063e3db8a491461045b578063e52074531461047b57600080fd5b8063ca10129514610325578063cd499da31461034557600080fd5b80637edddf45116101175780638da5cb5b116100fc5780638da5cb5b146102a3578063a89d2173146102d5578063bffe17801461030557600080fd5b80637edddf451461026357806386b988951461028357600080fd5b8063500de43111610148578063500de431146101e957806351e0e26b14610209578063715018a61461024e57600080fd5b806325416bc9146101a75780632ccb03f2146101c957600080fd5b366101a257604080513381523460208201527f2da466a7b24304f47e87fa2e1e5a81b9831ce54fec19055ce277ca2f39ba42c4910160405180910390a1005b600080fd5b3480156101b357600080fd5b506101c76101c2366004610ffd565b61053b565b005b3480156101d557600080fd5b506101c76101e4366004611072565b610553565b3480156101f557600080fd5b506101c761020436600461109c565b6105d3565b34801561021557600080fd5b506102396102243660046110d7565b60016020526000908152604090205460ff1681565b60405190151581526020015b60405180910390f35b34801561025a57600080fd5b506101c761064d565b34801561026f57600080fd5b506101c761027e36600461109c565b610661565b34801561028f57600080fd5b506101c761029e366004611072565b6106b3565b3480156102af57600080fd5b506000546001600160a01b03165b6040516001600160a01b039091168152602001610245565b3480156102e157600080fd5b506102396102f03660046110d7565b60026020526000908152604090205460ff1681565b34801561031157600080fd5b506101c76103203660046110f4565b61070e565b34801561033157600080fd5b506101c7610340366004610ffd565b61078f565b34801561035157600080fd5b506101c7610360366004610ffd565b6107a3565b34801561037157600080fd5b50600454600554610403916fffffffffffffffffffffffffffffffff81169163ffffffff700100000000000000000000000000000000830481169260ff740100000000000000000000000000000000000000008204169267ffffffffffffffff750100000000000000000000000000000000000000000090920482169291811691680100000000000000009091041686565b604080516fffffffffffffffffffffffffffffffff909716875263ffffffff958616602088015260ff9094169386019390935267ffffffffffffffff91821660608601521660808401521660a082015260c001610245565b34801561046757600080fd5b50610239610476366004611117565b610823565b34801561048757600080fd5b506101c7610496366004610ffd565b610b0d565b3480156104a757600080fd5b506003546102bd906001600160a01b031681565b3480156104c757600080fd5b506101c76104d63660046110d7565b610b88565b3480156104e757600080fd5b506101c76104f63660046110d7565b610bf2565b34801561050757600080fd5b506101c761051636600461114c565b610c82565b34801561052757600080fd5b506101c7610536366004611178565b610d7b565b610543610e03565b61054f82826001610e5d565b5050565b61055b610e03565b600580547fffffffffffffffffffffffffffffffffffffffffffffffff00000000000000001667ffffffffffffffff831617905560045b60405167ffffffffffffffff831681527fda79b6b81f905f788560507c685a42d5a8ab209ee26538cbcf3ce3caed601f9b906020015b60405180910390a250565b6105db610e03565b600580547fffffffffffffffffffffffffffffffffffffffff00000000ffffffffffffffff166801000000000000000063ffffffff8416021781555b60405163ffffffff831681527fda79b6b81f905f788560507c685a42d5a8ab209ee26538cbcf3ce3caed601f9b906020016105c8565b610655610e03565b61065f6000610efc565b565b610669610e03565b600480547fffffffffffffffffffffffff00000000ffffffffffffffffffffffffffffffff1670010000000000000000000000000000000063ffffffff8416021790556001610617565b6106bb610e03565b600480547fffffff0000000000000000ffffffffffffffffffffffffffffffffffffffffff16750100000000000000000000000000000000000000000067ffffffffffffffff8416021790556003610592565b610716610e03565b600480547fffffffffffffffffffffff00ffffffffffffffffffffffffffffffffffffffff167401000000000000000000000000000000000000000060ff841602179055600260405160ff831681527fda79b6b81f905f788560507c685a42d5a8ab209ee26538cbcf3ce3caed601f9b906020016105c8565b610797610e03565b61054f82826001610f64565b6000546001600160a01b03163314806107c657506003546001600160a01b031633145b6108175760405162461bcd60e51b815260206004820152600e60248201527f4e4f545f415554484f52495a454400000000000000000000000000000000000060448201526064015b60405180910390fd5b61054f82826000610e5d565b6000805a90504760008190036108815760035b60405186815233906001600160a01b038916907f2b8ae00e22d9eaf5a92820a22b947c007aee773fa36502ad7a1c9a464ab4932b9060200160405180910390a4600092505050610b06565b3360009081526001602052604090205460ff1661089f576000610836565b6001600160a01b03861660009081526002602052604090205460ff166108c6576001610836565b6004546000906108f9907501000000000000000000000000000000000000000000900467ffffffffffffffff16486111d9565b9050803a101561090657503a5b60055467ffffffffffffffff161580159061092c575060055467ffffffffffffffff1681115b15610940575060055467ffffffffffffffff165b6004546005546001600160a01b03891631916fffffffffffffffffffffffffffffffff81169168010000000000000000900463ffffffff169061099e9074010000000000000000000000000000000000000000900460ff16896111f2565b6004546109c590700100000000000000000000000000000000900463ffffffff16886111d9565b6109cf91906111d9565b6109d9908a6111d9565b98505a6109e6908a611209565b985080158015906109f657508089115b156109ff578098505b6000610a0b8a866111f2565b90508215801590610a24575082610a2282866111d9565b115b15610a4e5782841115610a41576000975050505050505050610b06565b610a4b8484611209565b90505b85811115610a595750845b6040516001600160a01b038c16908290600081818185875af1925050503d8060008114610aa2576040519150601f19603f3d011682016040523d82523d6000602084013e610aa7565b606091505b5050604080518c8152602081018890529081018390529098508815159033906001600160a01b038e16907fd0224505f828ccfcbc56ca0590d97442e239a7aa770f712948fd6388356b20de9060600160405180910390a4505050505050505b9392505050565b6000546001600160a01b0316331480610b3057506003546001600160a01b031633145b610b7c5760405162461bcd60e51b815260206004820152600e60248201527f4e4f545f415554484f52495a4544000000000000000000000000000000000000604482015260640161080e565b61054f82826000610f64565b610b90610e03565b600380547fffffffffffffffffffffffff0000000000000000000000000000000000000000166001600160a01b0383169081179091556040517fc388cec0895ad7ee4635898ec92207ca48d42256d4355f7042efef62c368a97990600090a250565b610bfa610e03565b6001600160a01b038116610c765760405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201527f6464726573730000000000000000000000000000000000000000000000000000606482015260840161080e565b610c7f81610efc565b50565b610c8a610e03565b6000826001600160a01b03168260405160006040518083038185875af1925050503d8060008114610cd7576040519150601f19603f3d011682016040523d82523d6000602084013e610cdc565b606091505b5050905080610d2d5760405162461bcd60e51b815260206004820152600f60248201527f57495448445241575f4641494c45440000000000000000000000000000000000604482015260640161080e565b604080513381526001600160a01b03851660208201529081018390527fd1c19fbcd4551a5edfb66d43d2e337c04837afda3482b42bdf569a8fccdae5fb9060600160405180910390a1505050565b610d83610e03565b600480547fffffffffffffffffffffffffffffffff00000000000000000000000000000000166fffffffffffffffffffffffffffffffff831617905560006040516fffffffffffffffffffffffffffffffff831681527fda79b6b81f905f788560507c685a42d5a8ab209ee26538cbcf3ce3caed601f9b906020016105c8565b6000546001600160a01b0316331461065f5760405162461bcd60e51b815260206004820181905260248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e6572604482015260640161080e565b60005b82811015610ef6576000848483818110610e7c57610e7c61121c565b9050602002016020810190610e9191906110d7565b6001600160a01b038116600081815260016020526040808220805460ff19168815159081179091559051939450927fb0918cd965657b8d231f8adba328fa810b6d61d800de9c795d40eb3623498c019190a35080610eee8161124b565b915050610e60565b50505050565b600080546001600160a01b038381167fffffffffffffffffffffffff0000000000000000000000000000000000000000831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b60005b82811015610ef6576000848483818110610f8357610f8361121c565b9050602002016020810190610f9891906110d7565b6001600160a01b038116600081815260026020526040808220805460ff19168815159081179091559051939450927ff544cca9d5484bfd447775bd759d12d53f1aa7c5f770be82c55070798ff9c63e9190a35080610ff58161124b565b915050610f67565b6000806020838503121561101057600080fd5b823567ffffffffffffffff8082111561102857600080fd5b818501915085601f83011261103c57600080fd5b81358181111561104b57600080fd5b8660208260051b850101111561106057600080fd5b60209290920196919550909350505050565b60006020828403121561108457600080fd5b813567ffffffffffffffff81168114610b0657600080fd5b6000602082840312156110ae57600080fd5b813563ffffffff81168114610b0657600080fd5b6001600160a01b0381168114610c7f57600080fd5b6000602082840312156110e957600080fd5b8135610b06816110c2565b60006020828403121561110657600080fd5b813560ff81168114610b0657600080fd5b60008060006060848603121561112c57600080fd5b8335611137816110c2565b95602085013595506040909401359392505050565b6000806040838503121561115f57600080fd5b823561116a816110c2565b946020939093013593505050565b60006020828403121561118a57600080fd5b81356fffffffffffffffffffffffffffffffff81168114610b0657600080fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fd5b808201808211156111ec576111ec6111aa565b92915050565b80820281158282048414176111ec576111ec6111aa565b818103818111156111ec576111ec6111aa565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b60007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff820361127c5761127c6111aa565b506001019056fea264697066735822122038d227bc2c41a3ccd659e680e1f79b3d8cbf96aefa295a0067b82097107f174064736f6c63430008110033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}