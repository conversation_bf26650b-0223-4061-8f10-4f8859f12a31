import mongoose, { Document, Schema } from 'mongoose';
export interface INFT extends Document {
    _id: string;
    contractAddress: string;
    tokenId: string;
    chainId: number;
    owner: string;
    currentUser?: string;
    metadata: {
        name?: string;
        description?: string;
        image?: string;
        animationUrl?: string;
        externalUrl?: string;
        attributes: Array<{
            traitType: string;
            value: any;
            displayType?: string;
            maxValue?: number;
        }>;
    };
    collection: {
        name?: string;
        symbol?: string;
        floorPrice?: number;
        totalSupply?: number;
        verified: boolean;
    };
    pricing: {
        lastSalePrice?: number;
        currentFloorPrice?: number;
        estimatedValue?: number;
        priceHistory: Array<{
            price: number;
            currency: string;
            timestamp: Date;
            source: 'sale' | 'listing' | 'oracle';
        }>;
    };
    rental: {
        isAvailable: boolean;
        currentListing?: Schema.Types.ObjectId;
        totalRentals: number;
        successfulRentals: number;
        totalRevenue: number;
        avgRentalDuration: number;
        avgDailyPrice: number;
        lastRentalDate?: Date;
        ratings: {
            average: number;
            count: number;
            breakdown: {
                5: number;
                4: number;
                3: number;
                2: number;
                1: number;
            };
        };
    };
    lending: {
        isCollateral: boolean;
        currentLoan?: Schema.Types.ObjectId;
        totalLoans: number;
        totalBorrowed: number;
        lastLoanDate?: Date;
    };
    collateral?: {
        isLocked: boolean;
        loanId?: Schema.Types.ObjectId;
        lockedAt?: Date;
    };
    analytics: {
        views: number;
        favorites: number;
        shares: number;
        demandScore: number;
        rarityScore: number;
        utilityScore: number;
        trendingScore: number;
        lastAnalyzed: Date;
    };
    crossChain: {
        originalChain: number;
        bridgeHistory: Array<{
            fromChain: number;
            toChain: number;
            transactionHash: string;
            bridgeProvider: string;
            timestamp: Date;
            status: 'pending' | 'completed' | 'failed';
        }>;
    };
    verification: {
        isVerified: boolean;
        verificationSource?: string;
        verifiedAt?: Date;
    };
    status: 'active' | 'hidden' | 'reported' | 'suspended';
    createdAt: Date;
    updatedAt: Date;
}
export declare const NFT: mongoose.Model<INFT, {}, {}, {}, mongoose.Document<unknown, {}, INFT, {}, {}> & INFT & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=NFT.d.ts.map