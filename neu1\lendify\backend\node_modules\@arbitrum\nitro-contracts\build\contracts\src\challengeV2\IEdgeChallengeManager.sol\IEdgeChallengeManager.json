{"_format": "hh-sol-artifact-1", "contractName": "IEdgeChallengeManager", "sourceName": "src/challengeV2/IEdgeChallengeManager.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}, {"internalType": "bytes32", "name": "bisectionHistoryRoot", "type": "bytes32"}, {"internalType": "bytes", "name": "prefixProof", "type": "bytes"}], "name": "bisectEdge", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "bytes32", "name": "originId", "type": "bytes32"}, {"internalType": "uint256", "name": "startHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "startHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "endHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "name": "calculateEdgeId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "bytes32", "name": "originId", "type": "bytes32"}, {"internalType": "uint256", "name": "startHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "startHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "endHeight", "type": "uint256"}], "name": "calculateMutualId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "challengePeriod<PERSON>locks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "beforeHash", "type": "bytes32"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "internalType": "struct OneStepData", "name": "oneStepData", "type": "tuple"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "prevConfig", "type": "tuple"}, {"internalType": "bytes32[]", "name": "beforeHistoryInclusionProof", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "afterHistoryInclusionProof", "type": "bytes32[]"}], "name": "confirmEdgeByOneStepProof", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}, {"components": [{"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "assertionState", "type": "tuple"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "internalType": "struct AssertionStateData", "name": "claimStateData", "type": "tuple"}], "name": "confirmEdgeByTime", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mutualId", "type": "bytes32"}], "name": "confirmedRival", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "endHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"internalType": "bytes", "name": "prefixProof", "type": "bytes"}, {"internalType": "bytes", "name": "proof", "type": "bytes"}], "internalType": "struct CreateEdgeArgs", "name": "args", "type": "tuple"}], "name": "createLayerZeroEdge", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "edgeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "edge<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mutualId", "type": "bytes32"}], "name": "firstRival", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "getEdge", "outputs": [{"components": [{"internalType": "bytes32", "name": "originId", "type": "bytes32"}, {"internalType": "bytes32", "name": "startHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "startHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "endHeight", "type": "uint256"}, {"internalType": "bytes32", "name": "lowerChildId", "type": "bytes32"}, {"internalType": "bytes32", "name": "upperChildId", "type": "bytes32"}, {"internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "uint64", "name": "createdAtBlock", "type": "uint64"}, {"internalType": "uint64", "name": "confirmedAtBlock", "type": "uint64"}, {"internalType": "enum EdgeStatus", "name": "status", "type": "uint8"}, {"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "bool", "name": "refunded", "type": "bool"}, {"internalType": "uint64", "name": "totalTimeUnrivaledCache", "type": "uint64"}], "internalType": "struct ChallengeEdge", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum EdgeType", "name": "eType", "type": "uint8"}], "name": "getLayerZeroEndHeight", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "getPrevAssertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "hasLengthOneRival", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "mutualId", "type": "bytes32"}], "name": "hasMadeLayerZeroRival", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "hasRival", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IAssertionChain", "name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint64", "name": "_challengePeriod<PERSON>locks", "type": "uint64"}, {"internalType": "contract IOneStepProofEntry", "name": "_oneStepProofEntry", "type": "address"}, {"internalType": "uint256", "name": "layerZeroBlockEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroBigStepEdgeHeight", "type": "uint256"}, {"internalType": "uint256", "name": "layerZeroSmallStepEdgeHeight", "type": "uint256"}, {"internalType": "contract IERC20", "name": "_stakeToken", "type": "address"}, {"internalType": "address", "name": "_excessStakeReceiver", "type": "address"}, {"internalType": "uint8", "name": "_numBigStepLevel", "type": "uint8"}, {"internalType": "uint256[]", "name": "_stakeAmounts", "type": "uint256[]"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32[]", "name": "edgeIds", "type": "bytes32[]"}, {"internalType": "uint256", "name": "maximumCachedTime", "type": "uint256"}], "name": "multiUpdateTimeCacheByChildren", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "oneStepProofEntry", "outputs": [{"internalType": "contract IOneStepProofEntry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "refundStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "stakeAmounts", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}], "name": "timeUnrivaled", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}, {"internalType": "uint256", "name": "maximumCachedTime", "type": "uint256"}], "name": "updateTimerCacheByChildren", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "edgeId", "type": "bytes32"}, {"internalType": "bytes32", "name": "claimingEdgeId", "type": "bytes32"}, {"internalType": "uint256", "name": "maximumCachedTime", "type": "uint256"}], "name": "updateTimerCacheByClaim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}