{"_format": "hh-sol-artifact-1", "contractName": "ERC20Bridge", "sourceName": "src/bridge/ERC20Bridge.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "stored", "type": "uint256"}, {"internalType": "uint256", "name": "received", "type": "uint256"}], "name": "BadSequencerMessageNumber", "type": "error"}, {"inputs": [], "name": "CallNotAllowed", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "CallTargetNotAllowed", "type": "error"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "InvalidOutboxSet", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "InvalidTokenSet", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "decimals", "type": "uint256"}], "name": "NativeTokenDecimalsTooLarge", "type": "error"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "NotContract", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotDelayedInbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotOutbox", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "rollup", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "NotRollupOrOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "NotSequencerInbox", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "BridgeCallTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "InboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageIndex", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "beforeInboxAcc", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "inbox", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "kind", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "baseFeeL1", "type": "uint256"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "MessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "outbox", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "OutboxToggle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rollup", "type": "address"}], "name": "RollupUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newSequencerInbox", "type": "address"}], "name": "SequencerInboxUpdated", "type": "event"}, {"inputs": [], "name": "acceptFundsFromOldBridge", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "activeOutbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedDelayedInboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}], "name": "allowedDelayedInboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "allowedOutboxList", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}], "name": "allowedOutboxes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "delayedInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "delayedMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "tokenFeeAmount", "type": "uint256"}], "name": "enqueueDelayedMessage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "uint256", "name": "afterDelayedMessagesRead", "type": "uint256"}, {"internalType": "uint256", "name": "prevMessageCount", "type": "uint256"}, {"internalType": "uint256", "name": "newMessageCount", "type": "uint256"}], "name": "enqueueSequencerMessage", "outputs": [{"internalType": "uint256", "name": "seqMessageIndex", "type": "uint256"}, {"internalType": "bytes32", "name": "beforeAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "delayedAcc", "type": "bytes32"}, {"internalType": "bytes32", "name": "acc", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "executeCall", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "rollup_", "type": "address"}, {"internalType": "address", "name": "nativeToken_", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "nativeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nativeTokenDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "contract IOwnable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sequencerInboxAccs", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerReportedSubMessageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setDelayedInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "outbox", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setOutbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_sequencerInbox", "type": "address"}], "name": "setSequencerInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newMsgCount", "type": "uint256"}], "name": "setSequencerReportedSubMessageCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes32", "name": "messageDataHash", "type": "bytes32"}], "name": "submitBatchSpendingReport", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOwnable", "name": "_rollup", "type": "address"}], "name": "updateRollupAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}