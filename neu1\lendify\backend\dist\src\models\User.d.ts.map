{"version": 3, "file": "User.d.ts", "sourceRoot": "", "sources": ["../../../src/models/User.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAC;AAEtD,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,GAAG,EAAE,MAAM,CAAC;IACZ,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE;QACP,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,UAAU,EAAE;QACV,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,aAAa,EAAE,MAAM,CAAC;QACtB,cAAc,EAAE,MAAM,CAAC;QACvB,aAAa,EAAE,MAAM,CAAC;QACtB,UAAU,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB,aAAa,EAAE,MAAM,CAAC;QACtB,YAAY,EAAE,MAAM,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,CAAC;KAClB,CAAC;IACF,WAAW,EAAE;QACX,aAAa,EAAE;YACb,KAAK,EAAE,OAAO,CAAC;YACf,IAAI,EAAE,OAAO,CAAC;YACd,GAAG,EAAE,OAAO,CAAC;YACb,UAAU,EAAE;gBACV,MAAM,EAAE,OAAO,CAAC;gBAChB,OAAO,EAAE,OAAO,CAAC;gBACjB,QAAQ,EAAE,OAAO,CAAC;gBAClB,QAAQ,EAAE,OAAO,CAAC;gBAClB,SAAS,EAAE,OAAO,CAAC;gBACnB,MAAM,EAAE,OAAO,CAAC;aACjB,CAAC;SACH,CAAC;QACF,OAAO,EAAE;YACP,YAAY,EAAE,OAAO,CAAC;YACtB,SAAS,EAAE,OAAO,CAAC;YACnB,SAAS,EAAE,OAAO,CAAC;SACpB,CAAC;KACH,CAAC;IACF,YAAY,EAAE;QACZ,eAAe,EAAE,OAAO,CAAC;QACzB,eAAe,EAAE,OAAO,CAAC;QACzB,aAAa,EAAE,OAAO,CAAC;QACvB,QAAQ,EAAE,MAAM,GAAG,OAAO,GAAG,UAAU,CAAC;KACzC,CAAC;IACF,QAAQ,EAAE;QACR,gBAAgB,EAAE,OAAO,CAAC;QAC1B,aAAa,EAAE,MAAM,CAAC;QACtB,YAAY,CAAC,EAAE,IAAI,CAAC;QACpB,SAAS,EAAE,IAAI,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,OAAO,CAAC;IACrC,MAAM,EAAE,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC1C,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAoKD,eAAO,MAAM,IAAI;;;;OAA4C,CAAC"}