{"_format": "hh-sol-artifact-1", "contractName": "IRollupUser", "sourceName": "src/rollup/IRollupLogic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "sendRoot", "type": "bytes32"}], "name": "AssertionConfirmed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "parentAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "indexed": false, "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"indexed": false, "internalType": "bytes32", "name": "afterInboxBatchAcc", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "inboxMaxCount", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}], "name": "AssertionCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint64", "name": "challengeIndex", "type": "uint64"}, {"indexed": false, "internalType": "address", "name": "asserter", "type": "address"}, {"indexed": false, "internalType": "address", "name": "challenger", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "challengedAsser<PERSON>", "type": "uint64"}], "name": "RollupChallengeStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "machineHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "RollupInitialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserStakeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "initialBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "finalBalance", "type": "uint256"}], "name": "UserWithdrawableFundsUpdated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "staker<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "name": "addToDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "amountStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseStake", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "chainId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "challenge<PERSON>anager", "outputs": [{"internalType": "contract IEdgeChallengeManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "confirmState", "type": "tuple"}, {"internalType": "bytes32", "name": "winningEdgeId", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "prevConfig", "type": "tuple"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "confirmAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "confirmPeriodBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "genesisAssertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertion", "outputs": [{"components": [{"internalType": "uint64", "name": "first<PERSON><PERSON><PERSON><PERSON>lock", "type": "uint64"}, {"internalType": "uint64", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint64"}, {"internalType": "uint64", "name": "createdAtBlock", "type": "uint64"}, {"internalType": "bool", "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "enum AssertionStatus", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "config<PERSON><PERSON>", "type": "bytes32"}], "internalType": "struct AssertionNode", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getAssertionCreationBlockForLogLookup", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getFirstChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "getSecondChildCreationBlock", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "get<PERSON><PERSON>", "outputs": [{"components": [{"internalType": "uint256", "name": "amountStaked", "type": "uint256"}, {"internalType": "bytes32", "name": "latestStakedAssertion", "type": "bytes32"}, {"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "internalType": "struct IRollupCore.Staker", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "stakerNum", "type": "uint64"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getValidators", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakeToken", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}], "name": "isPending", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "isStaked", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isValidator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestConfirmed", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "latestStakedAssertion", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserStakeEscrow", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumAssertionPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "name": "newStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}], "name": "newStakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "newStakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "outbox", "outputs": [{"internalType": "contract IOutbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "target", "type": "uint256"}], "name": "reduceDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "removeWhitelistAfterFork", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "removeWhitelistAfterValidatorAfk", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "returnOldDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker<PERSON><PERSON><PERSON>", "type": "address"}], "name": "returnOldDepositFor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollupEventInbox", "outputs": [{"internalType": "contract IRollupEventInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertion", "type": "tuple"}, {"internalType": "bytes32", "name": "expectedAssertionHash", "type": "bytes32"}], "name": "stakeOnNewAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakerCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "state", "type": "tuple"}, {"internalType": "bytes32", "name": "prevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "inboxAcc", "type": "bytes32"}], "name": "validateAssertionHash", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "assertionHash", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "name": "validateConfig", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validatorAfkBlocks", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validator<PERSON><PERSON><PERSON>stDisabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "wasmModuleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawStakerFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "withdrawableFunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "name": "withdrawalAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}