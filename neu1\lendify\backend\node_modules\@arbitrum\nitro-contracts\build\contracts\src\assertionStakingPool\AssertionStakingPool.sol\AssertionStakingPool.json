{"_format": "hh-sol-artifact-1", "contractName": "AssertionStakingPool", "sourceName": "src/assertionStakingPool/AssertionStakingPool.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_rollup", "type": "address"}, {"internalType": "bytes32", "name": "_assertionHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}], "name": "AmountExceedsBalance", "type": "error"}, {"inputs": [], "name": "EmptyAssertionId", "type": "error"}, {"inputs": [], "name": "ZeroAmount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "StakeWithdrawn", "type": "event"}, {"inputs": [], "name": "assertionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"components": [{"internalType": "bytes32", "name": "prevPrevAssertionHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "sequencerBatchAcc", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "wasmModuleRoot", "type": "bytes32"}, {"internalType": "uint256", "name": "requiredStake", "type": "uint256"}, {"internalType": "address", "name": "challenge<PERSON>anager", "type": "address"}, {"internalType": "uint64", "name": "confirmPeriodBlocks", "type": "uint64"}, {"internalType": "uint64", "name": "nextInboxPosition", "type": "uint64"}], "internalType": "struct ConfigData", "name": "configData", "type": "tuple"}], "internalType": "struct BeforeStateData", "name": "beforeStateData", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "beforeState", "type": "tuple"}, {"components": [{"components": [{"internalType": "bytes32[2]", "name": "bytes32Vals", "type": "bytes32[2]"}, {"internalType": "uint64[2]", "name": "u64Vals", "type": "uint64[2]"}], "internalType": "struct GlobalState", "name": "globalState", "type": "tuple"}, {"internalType": "enum MachineStatus", "name": "machineStatus", "type": "uint8"}, {"internalType": "bytes32", "name": "endHistoryRoot", "type": "bytes32"}], "internalType": "struct AssertionState", "name": "afterState", "type": "tuple"}], "internalType": "struct AssertionInputs", "name": "assertionInputs", "type": "tuple"}], "name": "createAssertion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "depositBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "depositIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "makeStakeWithdrawable", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "makeStakeWithdrawableAndWithdrawBackIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rollup", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawFromPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawStakeBackIntoPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}