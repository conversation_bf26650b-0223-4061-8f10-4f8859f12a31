{"_format": "hh-sol-artifact-1", "contractName": "ERC20Upgradeable", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b506109e6806100206000396000f3fe608060405234801561001057600080fd5b50600436106100c95760003560e01c80633950935111610081578063a457c2d71161005b578063a457c2d714610187578063a9059cbb1461019a578063dd62ed3e146101ad57600080fd5b8063395093511461014357806370a082311461015657806395d89b411461017f57600080fd5b806318160ddd116100b257806318160ddd1461010f57806323b872dd14610121578063313ce5671461013457600080fd5b806306fdde03146100ce578063095ea7b3146100ec575b600080fd5b6100d66101e6565b6040516100e391906107e0565b60405180910390f35b6100ff6100fa366004610868565b610278565b60405190151581526020016100e3565b6035545b6040519081526020016100e3565b6100ff61012f366004610892565b610292565b604051601281526020016100e3565b6100ff610151366004610868565b6102b6565b6101136101643660046108ce565b6001600160a01b031660009081526033602052604090205490565b6100d66102f5565b6100ff610195366004610868565b610304565b6100ff6101a8366004610868565b6103b3565b6101136101bb3660046108f0565b6001600160a01b03918216600090815260346020908152604080832093909416825291909152205490565b6060603680546101f590610923565b80601f016020809104026020016040519081016040528092919081815260200182805461022190610923565b801561026e5780601f106102435761010080835404028352916020019161026e565b820191906000526020600020905b81548152906001019060200180831161025157829003601f168201915b5050505050905090565b6000336102868185856103c1565b60019150505b92915050565b6000336102a0858285610519565b6102ab8585856105c9565b506001949350505050565b3360008181526034602090815260408083206001600160a01b038716845290915281205490919061028690829086906102f0908790610976565b6103c1565b6060603780546101f590610923565b3360008181526034602090815260408083206001600160a01b0387168452909152812054909190838110156103a65760405162461bcd60e51b815260206004820152602560248201527f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f7760448201527f207a65726f00000000000000000000000000000000000000000000000000000060648201526084015b60405180910390fd5b6102ab82868684036103c1565b6000336102868185856105c9565b6001600160a01b03831661043c5760405162461bcd60e51b8152602060048201526024808201527f45524332303a20617070726f76652066726f6d20746865207a65726f2061646460448201527f7265737300000000000000000000000000000000000000000000000000000000606482015260840161039d565b6001600160a01b0382166104b85760405162461bcd60e51b815260206004820152602260248201527f45524332303a20617070726f766520746f20746865207a65726f20616464726560448201527f7373000000000000000000000000000000000000000000000000000000000000606482015260840161039d565b6001600160a01b0383811660008181526034602090815260408083209487168084529482529182902085905590518481527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925910160405180910390a3505050565b6001600160a01b038381166000908152603460209081526040808320938616835292905220547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff81146105c357818110156105b65760405162461bcd60e51b815260206004820152601d60248201527f45524332303a20696e73756666696369656e7420616c6c6f77616e6365000000604482015260640161039d565b6105c384848484036103c1565b50505050565b6001600160a01b0383166106455760405162461bcd60e51b815260206004820152602560248201527f45524332303a207472616e736665722066726f6d20746865207a65726f20616460448201527f6472657373000000000000000000000000000000000000000000000000000000606482015260840161039d565b6001600160a01b0382166106c15760405162461bcd60e51b815260206004820152602360248201527f45524332303a207472616e7366657220746f20746865207a65726f206164647260448201527f6573730000000000000000000000000000000000000000000000000000000000606482015260840161039d565b6001600160a01b038316600090815260336020526040902054818110156107505760405162461bcd60e51b815260206004820152602660248201527f45524332303a207472616e7366657220616d6f756e742065786365656473206260448201527f616c616e63650000000000000000000000000000000000000000000000000000606482015260840161039d565b6001600160a01b03808516600090815260336020526040808220858503905591851681529081208054849290610787908490610976565b92505081905550826001600160a01b0316846001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040516107d391815260200190565b60405180910390a36105c3565b600060208083528351808285015260005b8181101561080d578581018301518582016040015282016107f1565b5060006040828601015260407fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f8301168501019250505092915050565b80356001600160a01b038116811461086357600080fd5b919050565b6000806040838503121561087b57600080fd5b6108848361084c565b946020939093013593505050565b6000806000606084860312156108a757600080fd5b6108b08461084c565b92506108be6020850161084c565b9150604084013590509250925092565b6000602082840312156108e057600080fd5b6108e98261084c565b9392505050565b6000806040838503121561090357600080fd5b61090c8361084c565b915061091a6020840161084c565b90509250929050565b600181811c9082168061093757607f821691505b602082108103610970577f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b50919050565b8082018082111561028c577f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fdfea264697066735822122025923da790ff44cea35ab0cd52e77a6a27c80f4bfaabbd5736d930ebe918965964736f6c63430008110033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}