"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrossChainService = void 0;
const events_1 = require("events");
const axios_1 = __importDefault(require("axios"));
class CrossChainService extends events_1.EventEmitter {
    constructor() {
        super();
        this.supportedChains = new Map();
        this.bridgeConfigs = new Map();
        this.bridgeTransactions = new Map();
        this.chainStatus = new Map();
        this.initializeSupportedChains();
        this.initializeBridgeConfigs();
    }
    async initialize() {
        try {
            await this.checkChainConnectivity();
            await this.initializeBridgeServices();
            this.startMonitoring();
            console.log('✅ Cross-chain service initialized');
        }
        catch (error) {
            console.error('❌ Failed to initialize cross-chain service:', error);
            throw error;
        }
    }
    initializeSupportedChains() {
        this.supportedChains.set(1, {
            chainId: 1,
            name: 'Ethereum',
            symbol: 'ETH',
            rpcUrl: process.env.ETHEREUM_RPC_URL || 'https://eth-mainnet.alchemyapi.io/v2/',
            blockExplorerUrl: 'https://etherscan.io',
            logoUrl: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
            isTestnet: false,
            bridges: ['polygon', 'arbitrum', 'optimism', 'base'],
            supportedTokens: ['ETH', 'USDC', 'USDT', 'DAI']
        });
        this.supportedChains.set(137, {
            chainId: 137,
            name: 'Polygon',
            symbol: 'MATIC',
            rpcUrl: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
            blockExplorerUrl: 'https://polygonscan.com',
            logoUrl: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
            isTestnet: false,
            bridges: ['ethereum', 'arbitrum', 'optimism', 'base'],
            supportedTokens: ['MATIC', 'WETH', 'USDC', 'USDT', 'DAI']
        });
        this.supportedChains.set(42161, {
            chainId: 42161,
            name: 'Arbitrum One',
            symbol: 'ETH',
            rpcUrl: process.env.ARBITRUM_RPC_URL || 'https://arb1.arbitrum.io/rpc',
            blockExplorerUrl: 'https://arbiscan.io',
            logoUrl: 'https://cryptologos.cc/logos/arbitrum-arb-logo.png',
            isTestnet: false,
            bridges: ['ethereum', 'polygon', 'optimism', 'base'],
            supportedTokens: ['ETH', 'USDC', 'USDT', 'DAI']
        });
        this.supportedChains.set(10, {
            chainId: 10,
            name: 'Optimism',
            symbol: 'ETH',
            rpcUrl: process.env.OPTIMISM_RPC_URL || 'https://mainnet.optimism.io',
            blockExplorerUrl: 'https://optimistic.etherscan.io',
            logoUrl: 'https://cryptologos.cc/logos/optimism-op-logo.png',
            isTestnet: false,
            bridges: ['ethereum', 'polygon', 'arbitrum', 'base'],
            supportedTokens: ['ETH', 'USDC', 'USDT', 'DAI', 'OP']
        });
        this.supportedChains.set(8453, {
            chainId: 8453,
            name: 'Base',
            symbol: 'ETH',
            rpcUrl: process.env.BASE_RPC_URL || 'https://mainnet.base.org',
            blockExplorerUrl: 'https://basescan.org',
            logoUrl: 'https://cryptologos.cc/logos/base-base-logo.png',
            isTestnet: false,
            bridges: ['ethereum', 'polygon', 'arbitrum', 'optimism'],
            supportedTokens: ['ETH', 'USDC', 'DAI']
        });
        console.log(`✅ Initialized ${this.supportedChains.size} supported chains`);
    }
    initializeBridgeConfigs() {
        this.bridgeConfigs.set('layerzero', {
            name: 'LayerZero',
            contracts: {
                1: '******************************************',
                137: '******************************************',
                42161: '******************************************',
                10: '******************************************',
                8453: '******************************************'
            },
            supportedChains: [1, 137, 42161, 10, 8453],
            fees: {
                1: '0.01',
                137: '10',
                42161: '0.005',
                10: '0.005',
                8453: '0.005'
            },
            estimatedTimes: {
                '1-137': 10,
                '137-1': 15,
                '1-42161': 7,
                '42161-1': 10,
                '1-10': 7,
                '10-1': 10,
                '137-42161': 20,
                '42161-137': 20
            }
        });
        this.bridgeConfigs.set('axelar', {
            name: 'Axelar',
            contracts: {
                1: '0x4F4495243837681061C4743b74B3eEdf548D56A5',
                137: '0x6f015F16De9fC8791b234eF68D486d2bF203FBA8',
                42161: '0xe432150cce91c13a887f7D836923d5597adD8E31',
                10: '0xe432150cce91c13a887f7D836923d5597adD8E31',
                8453: '0xe432150cce91c13a887f7D836923d5597adD8E31'
            },
            supportedChains: [1, 137, 42161, 10, 8453],
            fees: {
                1: '0.015',
                137: '15',
                42161: '0.008',
                10: '0.008',
                8453: '0.008'
            },
            estimatedTimes: {
                '1-137': 5,
                '137-1': 8,
                '1-42161': 3,
                '42161-1': 5,
                '1-10': 3,
                '10-1': 5
            }
        });
        this.bridgeConfigs.set('hyperlane', {
            name: 'Hyperlane',
            contracts: {
                1: '0x35231d4c2D8B8ADcB5617A638A0c4548684c7C70',
                137: '0x5d934f4e2f797775e53561bB72aca21ba36B96BB',
                42161: '0x979Ca5202784112f4738403dBec5D0F3B9daabB9',
                10: '0xd4C1905BB1D26BC93DAC913e13CaCC278CdCC80D',
                8453: '0xeA87ae93Fa0019a82A727bfd3eBd1cFCa8f64f1D'
            },
            supportedChains: [1, 137, 42161, 10, 8453],
            fees: {
                1: '0.005',
                137: '5',
                42161: '0.003',
                10: '0.003',
                8453: '0.003'
            },
            estimatedTimes: {
                '1-137': 3,
                '137-1': 5,
                '1-42161': 2,
                '42161-1': 3,
                '1-10': 2,
                '10-1': 3
            }
        });
        console.log(`✅ Initialized ${this.bridgeConfigs.size} bridge configurations`);
    }
    async checkChainConnectivity() {
        for (const [chainId, chainInfo] of this.supportedChains) {
            try {
                const response = await axios_1.default.post(chainInfo.rpcUrl, {
                    jsonrpc: '2.0',
                    method: 'eth_blockNumber',
                    params: [],
                    id: 1
                }, { timeout: 10000 });
                this.chainStatus.set(chainId, response.status === 200 && response.data.result);
                console.log(`✅ ${chainInfo.name} connectivity: OK`);
            }
            catch (error) {
                this.chainStatus.set(chainId, false);
                console.warn(`⚠️ ${chainInfo.name} connectivity: FAILED`);
            }
        }
    }
    async initializeBridgeServices() {
        for (const [bridgeName, config] of this.bridgeConfigs) {
            try {
                await this.testBridgeConnection(bridgeName, config);
                console.log(`✅ ${config.name} bridge service initialized`);
            }
            catch (error) {
                console.warn(`⚠️ Failed to initialize ${config.name} bridge:`, error);
            }
        }
    }
    async testBridgeConnection(bridgeName, config) {
        return Promise.resolve();
    }
    startMonitoring() {
        setInterval(() => {
            this.monitorBridgeTransactions();
        }, 30000);
        setInterval(() => {
            this.checkChainConnectivity();
        }, 300000);
        console.log('✅ Cross-chain monitoring started');
    }
    getSupportedChains() {
        return Array.from(this.supportedChains.values());
    }
    getChainInfo(chainId) {
        return this.supportedChains.get(chainId);
    }
    getAvailableBridges(fromChain, toChain) {
        const availableBridges = [];
        for (const config of this.bridgeConfigs.values()) {
            if (config.supportedChains.includes(fromChain) &&
                config.supportedChains.includes(toChain)) {
                availableBridges.push(config);
            }
        }
        return availableBridges;
    }
    async getBridgeQuote(fromChain, toChain, tokenContract, tokenId, bridgeName) {
        const quotes = [];
        const availableBridges = bridgeName
            ? [this.bridgeConfigs.get(bridgeName)].filter(Boolean)
            : this.getAvailableBridges(fromChain, toChain);
        for (const bridge of availableBridges) {
            try {
                const quote = await this.getBridgeQuoteForBridge(bridge, fromChain, toChain, tokenContract, tokenId);
                quotes.push(quote);
            }
            catch (error) {
                console.error(`Failed to get quote from ${bridge.name}:`, error);
            }
        }
        return quotes.sort((a, b) => parseFloat(a.fees.total) - parseFloat(b.fees.total));
    }
    async getBridgeQuoteForBridge(bridge, fromChain, toChain, tokenContract, tokenId) {
        const routeKey = `${fromChain}-${toChain}`;
        const bridgeFee = bridge.fees[fromChain] || '0';
        const gasFee = await this.estimateGasFee(fromChain, tokenContract);
        return {
            bridge: bridge.name,
            fees: {
                gas: gasFee,
                bridge: bridgeFee,
                total: (parseFloat(gasFee) + parseFloat(bridgeFee)).toString()
            },
            estimatedTime: bridge.estimatedTimes[routeKey] || 30,
            route: [fromChain.toString(), toChain.toString()]
        };
    }
    async bridgeNFT(fromChain, toChain, tokenContract, tokenId, toAddress, bridgeName = 'layerzero') {
        const bridge = this.bridgeConfigs.get(bridgeName);
        if (!bridge) {
            throw new Error(`Bridge ${bridgeName} not supported`);
        }
        if (!bridge.supportedChains.includes(fromChain) || !bridge.supportedChains.includes(toChain)) {
            throw new Error(`Bridge ${bridgeName} does not support this route`);
        }
        const transaction = {
            id: this.generateTransactionId(),
            fromChain,
            toChain,
            fromAddress: '',
            toAddress,
            tokenContract,
            tokenId,
            amount: '1',
            status: 'pending',
            createdAt: new Date(),
            estimatedTime: bridge.estimatedTimes[`${fromChain}-${toChain}`] || 30,
            fees: await this.calculateBridgeFees(bridge, fromChain, toChain, tokenContract)
        };
        this.bridgeTransactions.set(transaction.id, transaction);
        try {
            const txHash = await this.initiateBridgeTransaction(bridge, transaction);
            transaction.txHash = txHash;
            transaction.status = 'confirmed';
            this.emit('bridgeTransactionStarted', transaction);
            return transaction;
        }
        catch (error) {
            transaction.status = 'failed';
            this.emit('bridgeTransactionFailed', { transaction, error });
            throw error;
        }
    }
    async initiateBridgeTransaction(bridge, transaction) {
        return `0x${Date.now().toString(16)}${Math.random().toString(16).slice(2)}`;
    }
    async calculateBridgeFees(bridge, fromChain, toChain, tokenContract) {
        const bridgeFee = bridge.fees[fromChain] || '0';
        const gasFee = await this.estimateGasFee(fromChain, tokenContract);
        return {
            gas: gasFee,
            bridge: bridgeFee,
            total: (parseFloat(gasFee) + parseFloat(bridgeFee)).toString()
        };
    }
    async estimateGasFee(chainId, tokenContract) {
        const baseGasFees = {
            1: '0.01',
            137: '0.001',
            42161: '0.002',
            10: '0.002',
            8453: '0.001'
        };
        return baseGasFees[chainId] || '0.005';
    }
    async getBridgeTransaction(transactionId) {
        return this.bridgeTransactions.get(transactionId);
    }
    async getBridgeTransactionsByUser(userAddress) {
        const userTransactions = [];
        for (const transaction of this.bridgeTransactions.values()) {
            if (transaction.fromAddress.toLowerCase() === userAddress.toLowerCase() ||
                transaction.toAddress.toLowerCase() === userAddress.toLowerCase()) {
                userTransactions.push(transaction);
            }
        }
        return userTransactions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    async getNFTBridgeHistory(tokenContract, tokenId) {
        const nftTransactions = [];
        for (const transaction of this.bridgeTransactions.values()) {
            if (transaction.tokenContract.toLowerCase() === tokenContract.toLowerCase() &&
                transaction.tokenId === tokenId) {
                nftTransactions.push(transaction);
            }
        }
        return nftTransactions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    async getCrossChainNFTInfo(tokenContract, tokenId) {
        const bridgeHistory = await this.getNFTBridgeHistory(tokenContract, tokenId);
        if (bridgeHistory.length === 0) {
            return null;
        }
        const lastCompleted = bridgeHistory.find(tx => tx.status === 'completed');
        const currentChain = lastCompleted ? lastCompleted.toChain : bridgeHistory[0].fromChain;
        return {
            originalChain: bridgeHistory[bridgeHistory.length - 1].fromChain,
            currentChain,
            originalContract: tokenContract,
            wrappedContract: lastCompleted ? this.getWrappedContract(tokenContract, currentChain) : undefined,
            tokenId,
            isBridged: bridgeHistory.some(tx => tx.status === 'completed'),
            bridgeHistory,
            metadata: await this.getNFTMetadata(tokenContract, tokenId, currentChain)
        };
    }
    getWrappedContract(originalContract, currentChain) {
        return `${originalContract}_wrapped_${currentChain}`;
    }
    async getNFTMetadata(tokenContract, tokenId, chainId) {
        try {
            const chainInfo = this.supportedChains.get(chainId);
            if (!chainInfo)
                return null;
            return {
                name: `NFT #${tokenId}`,
                description: `Cross-chain NFT from contract ${tokenContract}`,
                image: '',
                attributes: []
            };
        }
        catch (error) {
            console.error('Failed to fetch NFT metadata:', error);
            return null;
        }
    }
    async monitorBridgeTransactions() {
        for (const [id, transaction] of this.bridgeTransactions) {
            if (transaction.status === 'confirmed' || transaction.status === 'bridging') {
                try {
                    const status = await this.checkBridgeTransactionStatus(transaction);
                    if (status !== transaction.status) {
                        transaction.status = status;
                        if (status === 'completed') {
                            transaction.completedAt = new Date();
                            this.emit('bridgeTransactionCompleted', transaction);
                        }
                        else if (status === 'failed') {
                            this.emit('bridgeTransactionFailed', { transaction, error: 'Bridge transaction failed' });
                        }
                    }
                }
                catch (error) {
                    console.error(`Error monitoring bridge transaction ${id}:`, error);
                }
            }
        }
    }
    async checkBridgeTransactionStatus(transaction) {
        const now = new Date();
        const elapsed = now.getTime() - transaction.createdAt.getTime();
        const estimatedCompletion = transaction.estimatedTime * 60 * 1000;
        if (elapsed > estimatedCompletion) {
            return 'completed';
        }
        else if (elapsed > estimatedCompletion * 0.3) {
            return 'bridging';
        }
        else {
            return 'confirmed';
        }
    }
    getChainStatus() {
        return Object.fromEntries(this.chainStatus);
    }
    isChainHealthy(chainId) {
        return this.chainStatus.get(chainId) || false;
    }
    async getChainMetrics(chainId) {
        const chainInfo = this.supportedChains.get(chainId);
        if (!chainInfo)
            return null;
        try {
            const startTime = Date.now();
            const [blockNumber, gasPrice] = await Promise.all([
                this.getBlockNumber(chainInfo.rpcUrl),
                this.getGasPrice(chainInfo.rpcUrl)
            ]);
            const latency = Date.now() - startTime;
            return {
                latency,
                blockHeight: parseInt(blockNumber, 16),
                gasPrice,
                isHealthy: this.chainStatus.get(chainId) || false
            };
        }
        catch (error) {
            console.error(`Failed to get metrics for chain ${chainId}:`, error);
            return null;
        }
    }
    async getBlockNumber(rpcUrl) {
        const response = await axios_1.default.post(rpcUrl, {
            jsonrpc: '2.0',
            method: 'eth_blockNumber',
            params: [],
            id: 1
        });
        return response.data.result;
    }
    async getGasPrice(rpcUrl) {
        const response = await axios_1.default.post(rpcUrl, {
            jsonrpc: '2.0',
            method: 'eth_gasPrice',
            params: [],
            id: 1
        });
        return response.data.result;
    }
    generateTransactionId() {
        return `bridge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async validateBridgeRoute(fromChain, toChain) {
        if (fromChain === toChain)
            return false;
        const fromChainInfo = this.supportedChains.get(fromChain);
        const toChainInfo = this.supportedChains.get(toChain);
        return !!(fromChainInfo && toChainInfo &&
            this.chainStatus.get(fromChain) &&
            this.chainStatus.get(toChain));
    }
    getBridgeStatistics() {
        const transactions = Array.from(this.bridgeTransactions.values());
        return {
            totalTransactions: transactions.length,
            completedTransactions: transactions.filter(tx => tx.status === 'completed').length,
            activeTransactions: transactions.filter(tx => ['pending', 'confirmed', 'bridging'].includes(tx.status)).length,
            failedTransactions: transactions.filter(tx => tx.status === 'failed').length,
            totalVolume: transactions.filter(tx => tx.status === 'completed').length.toString(),
            averageTime: this.calculateAverageBridgeTime(transactions)
        };
    }
    calculateAverageBridgeTime(transactions) {
        const completedTxs = transactions.filter(tx => tx.status === 'completed' && tx.completedAt);
        if (completedTxs.length === 0)
            return 0;
        const totalTime = completedTxs.reduce((sum, tx) => {
            const duration = tx.completedAt.getTime() - tx.createdAt.getTime();
            return sum + duration;
        }, 0);
        return totalTime / completedTxs.length / (1000 * 60);
    }
    onBridgeTransactionUpdate(callback) {
        this.on('bridgeTransactionStarted', callback);
        this.on('bridgeTransactionCompleted', callback);
        this.on('bridgeTransactionFailed', (data) => callback(data.transaction));
    }
    getBridgeConfigs() {
        return Object.fromEntries(this.bridgeConfigs);
    }
    async estimateBridgeTime(fromChain, toChain, bridgeName) {
        const route = `${fromChain}-${toChain}`;
        if (bridgeName) {
            const bridge = this.bridgeConfigs.get(bridgeName);
            return bridge?.estimatedTimes[route] || 30;
        }
        const availableBridges = this.getAvailableBridges(fromChain, toChain);
        const times = availableBridges.map(bridge => bridge.estimatedTimes[route] || 30);
        return times.length > 0 ? Math.min(...times) : 30;
    }
}
exports.CrossChainService = CrossChainService;
