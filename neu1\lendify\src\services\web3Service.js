import { ethers } from 'ethers'

// Supported chain configurations
export const SUPPORTED_CHAINS = {
  ethereum: {
    chainId: 1,
    name: 'Ethereum',
    symbol: 'ETH',
    rpcUrl: 'https://eth-mainnet.alchemyapi.io/v2/demo',
    blockExplorerUrl: 'https://etherscan.io',
    icon: '⟠',
    color: '#627EEA',
    gasPrice: '15 gwei',
    bridgeFee: '0.005 ETH',
    avgTime: '15 min',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18
    }
  },
  polygon: {
    chainId: 137,
    name: 'Polygon',
    symbol: 'MATIC',
    rpcUrl: 'https://polygon-rpc.com',
    blockExplorerUrl: 'https://polygonscan.com',
    icon: '⬟',
    color: '#8247E5',
    gasPrice: '30 gwei',
    bridgeFee: '0.1 MATIC',
    avgTime: '10 min',
    nativeCurrency: {
      name: 'Polygon',
      symbol: 'MATIC',
      decimals: 18
    }
  },
  bsc: {
    chainId: 56,
    name: '<PERSON><PERSON>',
    symbol: 'BNB',
    rpcUrl: 'https://bsc-dataseed.binance.org',
    blockExplorerUrl: 'https://bscscan.com',
    icon: '⬢',
    color: '#F3BA2F',
    gasPrice: '5 gwei',
    bridgeFee: '0.001 BNB',
    avgTime: '5 min',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    }
  },
  avalanche: {
    chainId: 43114,
    name: 'Avalanche',
    symbol: 'AVAX',
    rpcUrl: 'https://api.avax.network/ext/bc/C/rpc',
    blockExplorerUrl: 'https://snowtrace.io',
    icon: '🔺',
    color: '#E84142',
    gasPrice: '25 gwei',
    bridgeFee: '0.01 AVAX',
    avgTime: '2 min',
    nativeCurrency: {
      name: 'Avalanche',
      symbol: 'AVAX',
      decimals: 18
    }
  },
  solana: {
    chainId: 101, // Solana mainnet
    name: 'Solana',
    symbol: 'SOL',
    rpcUrl: 'https://api.mainnet-beta.solana.com',
    blockExplorerUrl: 'https://explorer.solana.com',
    icon: '◎',
    color: '#9945FF',
    gasPrice: '0.00025 SOL',
    bridgeFee: '0.01 SOL',
    avgTime: '1 min',
    nativeCurrency: {
      name: 'Solana',
      symbol: 'SOL',
      decimals: 9
    }
  }
}

class Web3Service {
  constructor() {
    this.providers = new Map()
    this.currentProvider = null
    this.currentChain = null
    this.connectedWallets = new Map()
    this.networkStatus = new Map()
    
    // Initialize providers for all supported chains
    this.initializeProviders()
    
    // Set up event listeners
    this.setupEventListeners()
  }

  initializeProviders() {
    Object.entries(SUPPORTED_CHAINS).forEach(([key, config]) => {
      if (config.chainId !== 101) { // Skip Solana for now (different provider)
        try {
          const provider = new ethers.JsonRpcProvider(config.rpcUrl)
          this.providers.set(config.chainId, provider)
          this.networkStatus.set(config.chainId, false) // Initially disconnected
        } catch (error) {
          console.error(`Failed to initialize provider for ${config.name}:`, error)
        }
      }
    })
  }

  setupEventListeners() {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', this.handleAccountsChanged.bind(this))
      window.ethereum.on('chainChanged', this.handleChainChanged.bind(this))
      window.ethereum.on('disconnect', this.handleDisconnect.bind(this))
    }
  }

  handleAccountsChanged(accounts) {
    if (accounts.length === 0) {
      this.disconnect()
    } else {
      // Account changed, update current account
      this.checkConnection()
    }
  }

  handleChainChanged(chainId) {
    const numericChainId = parseInt(chainId, 16)
    this.currentChain = numericChainId
    
    // Update current provider
    if (this.providers.has(numericChainId)) {
      this.currentProvider = new ethers.BrowserProvider(window.ethereum)
    }
    
    // Emit chain change event
    this.emit('chainChanged', numericChainId)
  }

  handleDisconnect() {
    this.disconnect()
  }

  async checkConnection() {
    try {
      if (!window.ethereum) return false

      const accounts = await window.ethereum.request({ method: 'eth_accounts' })
      if (accounts.length === 0) return false

      const chainId = await window.ethereum.request({ method: 'eth_chainId' })
      const numericChainId = parseInt(chainId, 16)
      
      this.currentProvider = new ethers.BrowserProvider(window.ethereum)
      this.currentChain = numericChainId
      
      // Update network status for current chain
      this.networkStatus.set(numericChainId, true)
      
      return {
        address: accounts[0],
        chainId: numericChainId,
        chainName: this.getChainName(numericChainId)
      }
    } catch (error) {
      console.error('Error checking connection:', error)
      return false
    }
  }

  async connectWallet() {
    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to continue')
      }

      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length === 0) {
        throw new Error('No accounts found')
      }

      const chainId = await window.ethereum.request({ method: 'eth_chainId' })
      const numericChainId = parseInt(chainId, 16)
      
      this.currentProvider = new ethers.BrowserProvider(window.ethereum)
      this.currentChain = numericChainId
      
      // Update network status
      this.networkStatus.set(numericChainId, true)
      
      return {
        address: accounts[0],
        chainId: numericChainId,
        chainName: this.getChainName(numericChainId)
      }
    } catch (error) {
      console.error('Error connecting wallet:', error)
      throw error
    }
  }

  async switchNetwork(chainId) {
    try {
      if (!window.ethereum) {
        throw new Error('MetaMask not found')
      }

      const chainConfig = this.getChainConfig(chainId)
      if (!chainConfig) {
        throw new Error(`Unsupported chain ID: ${chainId}`)
      }

      // Try to switch to the network
      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: `0x${chainId.toString(16)}` }]
        })
      } catch (switchError) {
        // If the network doesn't exist, add it
        if (switchError.code === 4902) {
          await this.addNetwork(chainConfig)
        } else {
          throw switchError
        }
      }

      // Update current chain and provider
      this.currentChain = chainId
      this.currentProvider = new ethers.BrowserProvider(window.ethereum)
      this.networkStatus.set(chainId, true)
      
      return true
    } catch (error) {
      console.error('Error switching network:', error)
      throw error
    }
  }

  async addNetwork(chainConfig) {
    try {
      await window.ethereum.request({
        method: 'wallet_addEthereumChain',
        params: [{
          chainId: `0x${chainConfig.chainId.toString(16)}`,
          chainName: chainConfig.name,
          nativeCurrency: chainConfig.nativeCurrency,
          rpcUrls: [chainConfig.rpcUrl],
          blockExplorerUrls: [chainConfig.blockExplorerUrl]
        }]
      })
    } catch (error) {
      console.error('Error adding network:', error)
      throw error
    }
  }

  disconnect() {
    this.currentProvider = null
    this.currentChain = null
    this.networkStatus.forEach((_, chainId) => {
      this.networkStatus.set(chainId, false)
    })
  }

  getChainConfig(chainId) {
    return Object.values(SUPPORTED_CHAINS).find(chain => chain.chainId === chainId)
  }

  getChainName(chainId) {
    const config = this.getChainConfig(chainId)
    return config ? config.name : `Chain ${chainId}`
  }

  getCurrentChain() {
    return this.currentChain
  }

  getCurrentProvider() {
    return this.currentProvider
  }

  getProvider(chainId) {
    return this.providers.get(chainId)
  }

  getSupportedChains() {
    return Object.values(SUPPORTED_CHAINS)
  }

  getNetworkStatus() {
    return Object.fromEntries(this.networkStatus)
  }

  isChainSupported(chainId) {
    return Object.values(SUPPORTED_CHAINS).some(chain => chain.chainId === chainId)
  }

  // Network health checking
  async checkNetworkHealth(chainId) {
    try {
      const provider = this.getProvider(chainId)
      if (!provider) return false

      const blockNumber = await provider.getBlockNumber()
      const gasPrice = await provider.getFeeData()

      this.networkStatus.set(chainId, true)

      return {
        isHealthy: true,
        blockNumber,
        gasPrice: ethers.formatUnits(gasPrice.gasPrice || 0n, 'gwei'),
        latency: Date.now() // Simple latency measure
      }
    } catch (error) {
      console.error(`Network health check failed for chain ${chainId}:`, error)
      this.networkStatus.set(chainId, false)
      return {
        isHealthy: false,
        error: error.message
      }
    }
  }

  async checkAllNetworks() {
    const results = {}
    const promises = Object.values(SUPPORTED_CHAINS)
      .filter(chain => chain.chainId !== 101) // Skip Solana for now
      .map(async (chain) => {
        const result = await this.checkNetworkHealth(chain.chainId)
        results[chain.chainId] = result
        return result
      })

    await Promise.all(promises)
    return results
  }

  // NFT related functions
  async getUserNFTs(userAddress, chainId) {
    try {
      const provider = this.getProvider(chainId)
      if (!provider) throw new Error(`Provider not found for chain ${chainId}`)

      // This would typically call an NFT indexing service or scan for NFT contracts
      // For now, return mock data structure
      return []
    } catch (error) {
      console.error('Error fetching user NFTs:', error)
      throw error
    }
  }

  // Token balance functions
  async getTokenBalance(tokenAddress, userAddress, chainId) {
    try {
      const provider = this.getProvider(chainId)
      if (!provider) throw new Error(`Provider not found for chain ${chainId}`)

      if (tokenAddress === 'native') {
        // Get native token balance
        const balance = await provider.getBalance(userAddress)
        return ethers.formatEther(balance)
      } else {
        // Get ERC-20 token balance
        const tokenContract = new ethers.Contract(
          tokenAddress,
          ['function balanceOf(address) view returns (uint256)'],
          provider
        )
        const balance = await tokenContract.balanceOf(userAddress)
        return ethers.formatUnits(balance, 18) // Assuming 18 decimals
      }
    } catch (error) {
      console.error('Error fetching token balance:', error)
      throw error
    }
  }

  // Transaction functions
  async sendTransaction(to, value, data = '0x') {
    try {
      if (!this.currentProvider) {
        throw new Error('No wallet connected')
      }

      const signer = await this.currentProvider.getSigner()
      const tx = await signer.sendTransaction({
        to,
        value: ethers.parseEther(value.toString()),
        data
      })

      return tx
    } catch (error) {
      console.error('Error sending transaction:', error)
      throw error
    }
  }

  // Event emitter functionality
  emit(event, data) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data))
    }
  }

  on(event, callback) {
    if (!this.listeners) this.listeners = {}
    if (!this.listeners[event]) this.listeners[event] = []
    this.listeners[event].push(callback)
  }

  off(event, callback) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback)
    }
  }
}

// Create singleton instance
const web3Service = new Web3Service()

export default web3Service
export { Web3Service }
