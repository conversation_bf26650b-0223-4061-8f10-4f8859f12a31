{"_format": "hh-sol-artifact-1", "contractName": "ArbOwnerPublic", "sourceName": "src/precompiles/ArbOwnerPublic.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "rectifiedOwner", "type": "address"}], "name": "ChainOwnerRectified", "type": "event"}, {"inputs": [], "name": "getAllChainOwners", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getBrotliCompressionLevel", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getInfraFeeAccount", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNetworkFeeAccount", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getScheduledUpgrade", "outputs": [{"internalType": "uint64", "name": "arbosVersion", "type": "uint64"}, {"internalType": "uint64", "name": "scheduledForTimestamp", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "ownerToRectify", "type": "address"}], "name": "rectify<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}