{"_format": "hh-sol-artifact-1", "contractName": "IInboxBase", "sourceName": "src/bridge/IInboxBase.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "InboxMessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}], "name": "InboxMessageDeliveredFromOrigin", "type": "event"}, {"inputs": [], "name": "allowListEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "dataLength", "type": "uint256"}, {"internalType": "uint256", "name": "baseFee", "type": "uint256"}], "name": "calculateRetryableSubmissionFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getProxyAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IBridge", "name": "_bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "_sequencerInbox", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxDataSize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "gasLimit", "type": "uint256"}, {"internalType": "uint256", "name": "maxFeePer<PERSON>as", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "sendContractTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "messageData", "type": "bytes"}], "name": "sendL2Message", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "messageData", "type": "bytes"}], "name": "sendL2MessageFromOrigin", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "gasLimit", "type": "uint256"}, {"internalType": "uint256", "name": "maxFeePer<PERSON>as", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "sendUnsignedTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "user", "type": "address[]"}, {"internalType": "bool[]", "name": "val", "type": "bool[]"}], "name": "setAllowList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_allowListEnabled", "type": "bool"}], "name": "setAllowListEnabled", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}