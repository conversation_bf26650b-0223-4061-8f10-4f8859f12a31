import { EventEmitter } from 'events';
export interface NFTMetadata {
    contractAddress: string;
    tokenId: string;
    name?: string;
    description?: string;
    image?: string;
    attributes?: Array<{
        trait_type: string;
        value: any;
    }>;
    collection?: string;
    rarity?: number;
    lastSalePrice?: string;
    floorPrice?: string;
}
export interface MarketData {
    volume24h: number;
    volumeChange24h: number;
    floorPrice: number;
    floorPriceChange24h: number;
    sales24h: number;
    avgSalePrice: number;
    uniqueHolders: number;
    totalSupply: number;
}
export interface RentalHistory {
    tokenId: string;
    rentalCount: number;
    avgRentalDuration: number;
    avgDailyPrice: number;
    lastRentalDate: Date;
    totalEarnings: number;
    avgRating: number;
    demandScore: number;
}
export interface PricePrediction {
    currentPrice: number;
    predictedPrice: number;
    confidence: number;
    trend: 'up' | 'down' | 'stable';
    factors: {
        demandScore: number;
        rarityScore: number;
        utilityScore: number;
        marketSentiment: number;
        seasonality: number;
    };
    timeframe: '1d' | '7d' | '30d';
    reasoning: string[];
}
export interface UserRecommendation {
    type: 'rent' | 'lend' | 'buy' | 'sell';
    nftContract: string;
    tokenId: string;
    score: number;
    reasoning: string;
    expectedReturn?: number;
    riskLevel: 'low' | 'medium' | 'high';
}
export interface AIAnalysis {
    nftContract: string;
    tokenId: string;
    analysisType: 'pricing' | 'demand' | 'risk' | 'recommendation';
    result: any;
    confidence: number;
    timestamp: Date;
    factors: string[];
}
export declare class AIService extends EventEmitter {
    private openaiApiKey;
    private huggingfaceApiKey;
    private analysisCache;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    private testOpenAIConnection;
    private testHuggingFaceConnection;
    analyzeDynamicPricing(nftContract: string, tokenId: string, metadata: NFTMetadata, marketData: MarketData, rentalHistory: RentalHistory): Promise<PricePrediction>;
    private getAIPricingAnalysis;
    generateUserRecommendations(userAddress: string, userHistory: any, preferences: any, marketData: any[]): Promise<UserRecommendation[]>;
    analyzeMarketTrends(collections: string[], timeframe?: '1d' | '7d' | '30d'): Promise<any>;
    assessRisk(nftContract: string, tokenId: string, transactionType: 'rent' | 'lend' | 'buy' | 'sell', amount: number): Promise<any>;
    private calculateRarityScore;
    private calculateDemandScore;
    private calculateUtilityScore;
    private calculateMarketSentiment;
    private calculateSeasonality;
    private calculateBasePrice;
    private calculatePriceMultiplier;
    private calculateConfidence;
    private calculateVariance;
    private generateFallbackReasoning;
    private analyzeUserBehavior;
    private generateRentRecommendations;
    private generateLendRecommendations;
    private analyzeCollectionTrends;
    private calculateOverallMarketSentiment;
    private calculateMarketVolatility;
    private calculateLiquidityRisk;
    private assessSmartContractRisk;
    private calculateUserRisk;
    private calculatePriceRisk;
    private calculateOverallRisk;
    private generateRiskRecommendations;
    clearCache(): void;
    getCacheStats(): any;
}
//# sourceMappingURL=AIService.d.ts.map