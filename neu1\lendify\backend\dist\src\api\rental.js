"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const Rental_1 = require("../models/Rental");
const NFT_1 = require("../models/NFT");
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({ success: true, service: 'Rental API', message: 'Service is healthy' });
});
router.post('/list', auth_1.authMiddleware.authenticate, (0, validation_1.validateRequest)({
    body: joi_1.default.object({
        nftContractAddress: validation_1.commonSchemas.ethereumAddress,
        nftTokenId: validation_1.commonSchemas.tokenId,
        nftChainId: validation_1.commonSchemas.chainId,
        dailyPrice: joi_1.default.number().positive().required(),
        currency: joi_1.default.string().valid('ETH', 'MATIC', 'USDC', 'DAI').default('ETH'),
        minRentalDuration: joi_1.default.number().integer().min(1).max(365).default(1),
        maxRentalDuration: joi_1.default.number().integer().min(1).max(365).default(30),
        collateralRequired: joi_1.default.number().min(0).required(),
        instantRent: joi_1.default.boolean().default(false),
        allowedUseCases: joi_1.default.array().items(joi_1.default.string().valid('gaming', 'metaverse', 'pfp', 'utility', 'yield', 'other')).min(1).required(),
        terms: joi_1.default.object({
            description: joi_1.default.string().max(1000).required(),
            restrictions: joi_1.default.array().items(joi_1.default.string().max(200)).default([]),
            penalties: joi_1.default.object({
                lateReturn: joi_1.default.number().min(0).default(0),
                damage: joi_1.default.number().min(0).default(0)
            }).default({})
        }).required()
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const lenderAddress = req.user.address;
    const { nftContractAddress, nftTokenId, nftChainId, dailyPrice, currency, minRentalDuration, maxRentalDuration, collateralRequired, instantRent, allowedUseCases, terms } = req.body;
    const nft = await NFT_1.NFT.findByIdentifier(nftContractAddress, nftTokenId, nftChainId);
    if (!nft) {
        return res.status(404).json({
            success: false,
            error: 'NFT not found. Please register the NFT first.'
        });
    }
    if (nft.owner.toLowerCase() !== lenderAddress.toLowerCase()) {
        return res.status(403).json({
            success: false,
            error: 'You do not own this NFT'
        });
    }
    if (nft.currentUser || nft.rental?.isAvailable) {
        return res.status(409).json({
            success: false,
            error: 'NFT is already listed for rent or currently rented'
        });
    }
    const rental = new Rental_1.Rental({
        nft: nft._id,
        lender: lenderAddress,
        pricing: {
            dailyPrice,
            currency,
            collateralRequired
        },
        duration: {
            min: minRentalDuration,
            max: maxRentalDuration
        },
        settings: {
            instantRent,
            allowedUseCases,
            autoExtend: false
        },
        terms,
        status: 'active',
        blockchain: {
            chainId: nftChainId,
            contractAddress: nftContractAddress,
            tokenId: nftTokenId
        }
    });
    await rental.save();
    await NFT_1.NFT.findByIdAndUpdate(nft._id, {
        $set: {
            'rental.isAvailable': true,
            'rental.currentListing': rental._id,
            'rental.avgDailyPrice': dailyPrice
        }
    });
    res.status(201).json({
        success: true,
        message: 'NFT listed for rent successfully',
        data: { rental }
    });
}));
router.get('/:rentalId', (0, validation_1.validateRequest)({
    params: joi_1.default.object({
        rentalId: validation_1.commonSchemas.mongoId
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { rentalId } = req.params;
    const rental = await Rental_1.Rental.findById(rentalId)
        .populate('nft');
    if (!rental) {
        return res.status(404).json({
            success: false,
            error: 'Rental not found'
        });
    }
    res.json({
        success: true,
        data: { rental }
    });
}));
router.get('/available', (0, validation_1.validateRequest)({
    query: joi_1.default.object({
        chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).optional(),
        useCase: joi_1.default.string().valid('gaming', 'metaverse', 'pfp', 'utility', 'yield', 'other').optional(),
        minPrice: joi_1.default.number().min(0).optional(),
        maxPrice: joi_1.default.number().min(0).optional(),
        maxDuration: joi_1.default.number().integer().min(1).optional(),
        sortBy: joi_1.default.string().valid('price', 'duration', 'rating', 'recent').default('recent'),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(50).default(20)
    })
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { chainId, useCase, minPrice, maxPrice, maxDuration, sortBy, sortOrder, page, limit } = req.query;
    const filters = {
        status: 'active'
    };
    if (chainId)
        filters['blockchain.chainId'] = chainId;
    if (useCase)
        filters['settings.allowedUseCases'] = useCase;
    if (minPrice !== undefined || maxPrice !== undefined) {
        filters['pricing.dailyPrice'] = {};
        if (minPrice !== undefined)
            filters['pricing.dailyPrice'].$gte = minPrice;
        if (maxPrice !== undefined)
            filters['pricing.dailyPrice'].$lte = maxPrice;
    }
    if (maxDuration)
        filters['duration.max'] = { $lte: maxDuration };
    const sortOptions = {};
    switch (sortBy) {
        case 'price':
            sortOptions['pricing.dailyPrice'] = sortOrder === 'asc' ? 1 : -1;
            break;
        case 'duration':
            sortOptions['duration.max'] = sortOrder === 'asc' ? 1 : -1;
            break;
        default:
            sortOptions.createdAt = sortOrder === 'asc' ? 1 : -1;
    }
    const skip = (page - 1) * limit;
    const [rentals, total] = await Promise.all([
        Rental_1.Rental.find(filters)
            .sort(sortOptions)
            .skip(skip)
            .limit(limit)
            .populate('nft'),
        Rental_1.Rental.countDocuments(filters)
    ]);
    res.json({
        success: true,
        data: {
            rentals,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));
exports.default = router;
