{"_format": "hh-sol-artifact-1", "contractName": "IERC3156FlashBorrower", "sourceName": "contracts/interfaces/IERC3156FlashBorrower.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initiator", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onFlashLoan", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}