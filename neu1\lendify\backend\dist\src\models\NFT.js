"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NFT = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const NFTSchema = new mongoose_1.Schema({
    contractAddress: {
        type: String,
        required: true,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    tokenId: {
        type: String,
        required: true
    },
    chainId: {
        type: Number,
        required: true,
        enum: [1, 137, 42161, 10, 8453]
    },
    owner: {
        type: String,
        required: true,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    currentUser: {
        type: String,
        lowercase: true,
        match: /^0x[a-fA-F0-9]{40}$/
    },
    metadata: {
        name: String,
        description: String,
        image: String,
        animationUrl: String,
        externalUrl: String,
        attributes: [{
                traitType: { type: String, required: true },
                value: { type: mongoose_1.default.Schema.Types.Mixed, required: true },
                displayType: String,
                maxValue: Number
            }]
    },
    collection: {
        name: String,
        symbol: String,
        floorPrice: { type: Number, min: 0 },
        totalSupply: { type: Number, min: 0 },
        verified: { type: Boolean, default: false }
    },
    pricing: {
        lastSalePrice: { type: Number, min: 0 },
        currentFloorPrice: { type: Number, min: 0 },
        estimatedValue: { type: Number, min: 0 },
        priceHistory: [{
                price: { type: Number, required: true, min: 0 },
                currency: { type: String, required: true },
                timestamp: { type: Date, required: true },
                source: { type: String, enum: ['sale', 'listing', 'oracle'], required: true }
            }]
    },
    rental: {
        isAvailable: { type: Boolean, default: false },
        currentListing: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Rental' },
        totalRentals: { type: Number, default: 0, min: 0 },
        successfulRentals: { type: Number, default: 0, min: 0 },
        totalRevenue: { type: Number, default: 0, min: 0 },
        avgRentalDuration: { type: Number, default: 0, min: 0 },
        avgDailyPrice: { type: Number, default: 0, min: 0 },
        lastRentalDate: Date,
        ratings: {
            average: { type: Number, default: 0, min: 0, max: 5 },
            count: { type: Number, default: 0, min: 0 },
            breakdown: {
                5: { type: Number, default: 0, min: 0 },
                4: { type: Number, default: 0, min: 0 },
                3: { type: Number, default: 0, min: 0 },
                2: { type: Number, default: 0, min: 0 },
                1: { type: Number, default: 0, min: 0 }
            }
        }
    },
    lending: {
        isCollateral: { type: Boolean, default: false },
        currentLoan: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Loan' },
        totalLoans: { type: Number, default: 0, min: 0 },
        totalBorrowed: { type: Number, default: 0, min: 0 },
        lastLoanDate: Date
    },
    collateral: {
        isLocked: { type: Boolean, default: false },
        loanId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Loan' },
        lockedAt: Date
    },
    analytics: {
        views: { type: Number, default: 0, min: 0 },
        favorites: { type: Number, default: 0, min: 0 },
        shares: { type: Number, default: 0, min: 0 },
        demandScore: { type: Number, default: 0, min: 0, max: 100 },
        rarityScore: { type: Number, default: 0, min: 0, max: 100 },
        utilityScore: { type: Number, default: 0, min: 0, max: 100 },
        trendingScore: { type: Number, default: 0, min: 0, max: 100 },
        lastAnalyzed: { type: Date, default: Date.now }
    },
    crossChain: {
        originalChain: { type: Number, required: true },
        bridgeHistory: [{
                fromChain: { type: Number, required: true },
                toChain: { type: Number, required: true },
                transactionHash: { type: String, required: true },
                bridgeProvider: { type: String, required: true },
                timestamp: { type: Date, required: true },
                status: { type: String, enum: ['pending', 'completed', 'failed'], required: true }
            }]
    },
    verification: {
        isVerified: { type: Boolean, default: false },
        verificationSource: String,
        verifiedAt: Date
    },
    status: {
        type: String,
        enum: ['active', 'hidden', 'reported', 'suspended'],
        default: 'active'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
NFTSchema.index({ contractAddress: 1, tokenId: 1, chainId: 1 }, { unique: true });
NFTSchema.index({ owner: 1 });
NFTSchema.index({ currentUser: 1 });
NFTSchema.index({ 'rental.isAvailable': 1 });
NFTSchema.index({ 'lending.isCollateral': 1 });
NFTSchema.index({ 'analytics.demandScore': -1 });
NFTSchema.index({ 'analytics.trendingScore': -1 });
NFTSchema.index({ 'collection.name': 1 });
NFTSchema.index({ chainId: 1 });
NFTSchema.index({ createdAt: -1 });
NFTSchema.index({ status: 1 });
NFTSchema.index({
    'metadata.name': 'text',
    'metadata.description': 'text',
    'collection.name': 'text'
});
NFTSchema.virtual('identifier').get(function () {
    return `${this.contractAddress}:${this.tokenId}:${this.chainId}`;
});
NFTSchema.virtual('rarityRank').get(function () {
    return Math.floor((100 - this.analytics.rarityScore) / 10) + 1;
});
NFTSchema.methods.updateRating = function (rating) {
    const ratings = this.rental.ratings;
    ratings.breakdown[rating] += 1;
    ratings.count += 1;
    const total = (ratings.breakdown[5] * 5) +
        (ratings.breakdown[4] * 4) +
        (ratings.breakdown[3] * 3) +
        (ratings.breakdown[2] * 2) +
        (ratings.breakdown[1] * 1);
    ratings.average = total / ratings.count;
    return this.save();
};
NFTSchema.methods.updateAnalytics = function (data) {
    if (data.views)
        this.analytics.views += data.views;
    if (data.favorites)
        this.analytics.favorites += data.favorites;
    if (data.shares)
        this.analytics.shares += data.shares;
    if (data.demandScore !== undefined)
        this.analytics.demandScore = data.demandScore;
    if (data.rarityScore !== undefined)
        this.analytics.rarityScore = data.rarityScore;
    if (data.utilityScore !== undefined)
        this.analytics.utilityScore = data.utilityScore;
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    const recentViews = this.analytics.views;
    const recentFavorites = this.analytics.favorites;
    this.analytics.trendingScore = Math.min(100, (recentViews * 0.1) +
        (recentFavorites * 2) +
        (this.analytics.demandScore * 0.5));
    this.analytics.lastAnalyzed = new Date();
    return this.save();
};
NFTSchema.methods.recordRental = function (data) {
    this.rental.totalRentals += 1;
    if (data.successful) {
        this.rental.successfulRentals += 1;
        this.rental.totalRevenue += data.price;
        const totalDuration = (this.rental.avgRentalDuration * (this.rental.successfulRentals - 1)) + data.duration;
        this.rental.avgRentalDuration = totalDuration / this.rental.successfulRentals;
        const totalPrice = (this.rental.avgDailyPrice * (this.rental.successfulRentals - 1)) + (data.price / data.duration);
        this.rental.avgDailyPrice = totalPrice / this.rental.successfulRentals;
    }
    this.rental.lastRentalDate = new Date();
    return this.save();
};
NFTSchema.methods.addPriceHistory = function (price, currency, source) {
    this.pricing.priceHistory.push({
        price,
        currency,
        timestamp: new Date(),
        source
    });
    if (this.pricing.priceHistory.length > 100) {
        this.pricing.priceHistory = this.pricing.priceHistory.slice(-100);
    }
    if (source === 'sale') {
        this.pricing.lastSalePrice = price;
    }
    return this.save();
};
NFTSchema.statics.findByIdentifier = function (contractAddress, tokenId, chainId) {
    return this.findOne({ contractAddress, tokenId, chainId });
};
NFTSchema.statics.findAvailableForRent = function (filters = {}) {
    return this.find({
        'rental.isAvailable': true,
        status: 'active',
        ...filters
    });
};
NFTSchema.statics.findTrending = function (limit = 20) {
    return this.find({ status: 'active' })
        .sort({ 'analytics.trendingScore': -1 })
        .limit(limit);
};
exports.NFT = mongoose_1.default.model('NFT', NFTSchema);
