{"_format": "hh-sol-artifact-1", "contractName": "LibArbitrumL2Upgradeable", "sourceName": "contracts/crosschain/arbitrum/LibArbitrumL2Upgradeable.sol", "abi": [{"inputs": [], "name": "ARBSYS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6091610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063bf0a12cf146038575b600080fd5b603f606481565b6040516001600160a01b03909116815260200160405180910390f3fea2646970667358221220f100003732253acff0afe22ae1fd87f62bfd41627989b620bf52660e06e1282b64736f6c63430008090033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063bf0a12cf146038575b600080fd5b603f606481565b6040516001600160a01b03909116815260200160405180910390f3fea2646970667358221220f100003732253acff0afe22ae1fd87f62bfd41627989b620bf52660e06e1282b64736f6c63430008090033", "linkReferences": {}, "deployedLinkReferences": {}}