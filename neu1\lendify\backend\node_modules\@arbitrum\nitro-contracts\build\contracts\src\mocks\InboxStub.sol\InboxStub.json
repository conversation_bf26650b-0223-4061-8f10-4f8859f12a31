{"_format": "hh-sol-artifact-1", "contractName": "InboxStub", "sourceName": "src/mocks/InboxStub.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "InboxMessageDelivered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "messageNum", "type": "uint256"}], "name": "InboxMessageDeliveredFromOrigin", "type": "event"}, {"inputs": [], "name": "allowListEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "bridge", "outputs": [{"internalType": "contract IBridge", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "calculateRetryableSubmissionFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "createRetryableTicket", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "depositEth", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "getProxyAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "contract IBridge", "name": "_bridge", "type": "address"}, {"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "maxDataSize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IBridge", "name": "_bridge", "type": "address"}], "name": "postUpgradeInit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendContractTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendL1FundedContractTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendL1FundedUnsignedTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendL1FundedUnsignedTransactionToFork", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "messageData", "type": "bytes"}], "name": "sendL2Message", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "messageData", "type": "bytes"}], "name": "sendL2MessageFromOrigin", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendUnsignedTransaction", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "sendUnsignedTransactionToFork", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "sendWithdrawEthToFork", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "sequencerInbox", "outputs": [{"internalType": "contract ISequencerInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "bool[]", "name": "", "type": "bool[]"}], "name": "setAllowList", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "", "type": "bool"}], "name": "setAllowListEnabled", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "unsafeCreateRetryableTicket", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}