"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.commonSchemas = exports.validateRequest = void 0;
const joi_1 = __importDefault(require("joi"));
const validateRequest = (schema) => {
    return (req, res, next) => {
        const errors = [];
        if (schema.body) {
            const { error } = schema.body.validate(req.body);
            if (error) {
                errors.push(`Body: ${error.details.map(d => d.message).join(', ')}`);
            }
        }
        if (schema.query) {
            const { error } = schema.query.validate(req.query);
            if (error) {
                errors.push(`Query: ${error.details.map(d => d.message).join(', ')}`);
            }
        }
        if (schema.params) {
            const { error } = schema.params.validate(req.params);
            if (error) {
                errors.push(`Params: ${error.details.map(d => d.message).join(', ')}`);
            }
        }
        if (errors.length > 0) {
            res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors
            });
            return;
        }
        next();
    };
};
exports.validateRequest = validateRequest;
exports.commonSchemas = {
    ethereumAddress: joi_1.default.string().pattern(/^0x[a-fA-F0-9]{40}$/).required(),
    objectId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    mongoId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    pagination: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20)
    }),
    chainId: joi_1.default.number().valid(1, 137, 42161, 10, 8453).required(),
    tokenId: joi_1.default.string().required(),
    price: joi_1.default.string().pattern(/^\d+\.?\d*$/).required(),
    duration: joi_1.default.number().integer().min(1).max(365).required()
};
