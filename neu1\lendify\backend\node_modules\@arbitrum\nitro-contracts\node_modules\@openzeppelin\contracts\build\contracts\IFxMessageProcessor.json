{"_format": "hh-sol-artifact-1", "contractName": "IFxMessageProcessor", "sourceName": "contracts/vendor/polygon/IFxMessageProcessor.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "stateId", "type": "uint256"}, {"internalType": "address", "name": "rootMessageSender", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "processMessageFromRoot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}